﻿++Solution 'Core' ‎ (1 of 1 project)
i:{00000000-0000-0000-0000-000000000000}:Core.sln
++Core
i:{00000000-0000-0000-0000-000000000000}:Core
++References
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:{0A8FE3C4-3E1B-4554-AFF0-268C9B8CD0BA}
++External Dependencies
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:{7CC1DB7C-48A3-456F-90CB-7A8AB0181FF9}
++__msvc_bit_utils.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_BIT_UTILS.HPP
++__msvc_filebuf.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_FILEBUF.HPP
++__msvc_format_ucd_tables.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_FORMAT_UCD_TABLES.HPP
++__msvc_formatter.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_FORMATTER.HPP
++__msvc_iter_core.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_ITER_CORE.HPP
++__msvc_minmax.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_MINMAX.HPP
++__msvc_print.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_PRINT.HPP
++__msvc_sanitizer_annotate_container.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_SANITIZER_ANNOTATE_CONTAINER.HPP
++__msvc_system_error_abi.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_SYSTEM_ERROR_ABI.HPP
++__msvc_xlocinfo_types.hpp
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\__MSVC_XLOCINFO_TYPES.HPP
++algorithm
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ALGORITHM
++ammintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\AMMINTRIN.H
++apiquery2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\APIQUERY2.H
++apiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\APISET.H
++apisetcconv.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\APISETCCONV.H
++arm_neon.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ARM_NEON.H
++arm64_neon.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ARM64_NEON.H
++arm64intr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ARM64INTR.H
++armintr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ARMINTR.H
++assert.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\ASSERT.H
++atomic
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ATOMIC
++basetsd.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\BASETSD.H
++bcrypt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\BCRYPT.H
++bit
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\BIT
++cctype
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CCTYPE
++cderr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\CDERR.H
++cerrno
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CERRNO
++cfloat
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CFLOAT
++cguid.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\CGUID.H
++charconv
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CHARCONV
++climits
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CLIMITS
++clocale
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CLOCALE
++cmath
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CMATH
++combaseapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\COMBASEAPI.H
++comdef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\COMDEF.H
++comdefsp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\COMDEFSP.H
++comip.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\COMIP.H
++coml2api.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\COML2API.H
++commctrl.rh
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\COMMCTRL.RH
++commdlg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\COMMDLG.H
++commdlg.inl
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\COMMDLG.INL
++compare
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\COMPARE
++comutil.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\COMUTIL.H
++concepts
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CONCEPTS
++concurrencysal.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CONCURRENCYSAL.H
++consoleapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\CONSOLEAPI.H
++consoleapi2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\CONSOLEAPI2.H
++consoleapi3.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\CONSOLEAPI3.H
++corecrt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT.H
++corecrt_io.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_IO.H
++corecrt_malloc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_MALLOC.H
++corecrt_math.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_MATH.H
++corecrt_math_defines.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_MATH_DEFINES.H
++corecrt_memcpy_s.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_MEMCPY_S.H
++corecrt_memory.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_MEMORY.H
++corecrt_search.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_SEARCH.H
++corecrt_share.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_SHARE.H
++corecrt_startup.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_STARTUP.H
++corecrt_stdio_config.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_STDIO_CONFIG.H
++corecrt_terminate.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_TERMINATE.H
++corecrt_wconio.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WCONIO.H
++corecrt_wctype.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WCTYPE.H
++corecrt_wdirect.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WDIRECT.H
++corecrt_wio.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WIO.H
++corecrt_wprocess.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WPROCESS.H
++corecrt_wstdio.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WSTDIO.H
++corecrt_wstdlib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WSTDLIB.H
++corecrt_wstring.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WSTRING.H
++corecrt_wtime.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CORECRT_WTIME.H
++crtdbg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CRTDBG.H
++crtdefs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CRTDEFS.H
++cstddef
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CSTDDEF
++cstdint
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CSTDINT
++cstdio
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CSTDIO
++cstdlib
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CSTDLIB
++cstring
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CSTRING
++ctime
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CTIME
++ctype.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\CTYPE.H
++cwchar
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CWCHAR
++datetimeapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DATETIMEAPI.H
++DbgHelp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DBGHELP.H
++dde.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DDE.H
++dde.rh
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DDE.RH
++ddeml.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DDEML.H
++debugapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DEBUGAPI.H
++detours.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KALONLINE HACKS\XEAXMASTE THEHYPERNETWORK\XEAXMASTE - THEHYPERNETWORK SOURCES\CORE\CORE\DETOURS\DETOURS.H
++devpkey.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\DEVPKEY.H
++devpropdef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\DEVPROPDEF.H
++direct.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\DIRECT.H
++DispEx.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DISPEX.H
++dlgs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DLGS.H
++dpapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\DPAPI.H
++driverspecs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\DRIVERSPECS.H
++eh.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\EH.H
++emmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\EMMINTRIN.H
++enclaveapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\ENCLAVEAPI.H
++enigma_ide.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KALONLINE HACKS\XEAXMASTE THEHYPERNETWORK\XEAXMASTE - THEHYPERNETWORK SOURCES\CORE\CORE\DETOURS\ENIGMA_IDE.H
++errhandlingapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\ERRHANDLINGAPI.H
++errno.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\ERRNO.H
++exception
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\EXCEPTION
++excpt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\EXCPT.H
++fcntl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\FCNTL.H
++fibersapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\FIBERSAPI.H
++fileapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\FILEAPI.H
++fileapifromapp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\FILEAPIFROMAPP.H
++float.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\FLOAT.H
++format
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\FORMAT
++fstream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\FSTREAM
++guiddef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\GUIDDEF.H
++handleapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\HANDLEAPI.H
++hashtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\HASHTYPES.H
++heapapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\HEAPAPI.H
++hstring.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\WINRT\HSTRING.H
++ifdef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IFDEF.H
++ifmib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IFMIB.H
++ImageHlp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IMAGEHLP.H
++ime_cmodes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IME_CMODES.H
++imm.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IMM.H
++immintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\IMMINTRIN.H
++in6addr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IN6ADDR.H
++inaddr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\INADDR.H
++initializer_list
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\INITIALIZER_LIST
++interlockedapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\INTERLOCKEDAPI.H
++intrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\INTRIN.H
++intrin0.inl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\INTRIN0.INL.H
++inttypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\INTTYPES.H
++io.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\IO.H
++ioapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IOAPISET.H
++ioctltypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\IOCTLTYPES.H
++iomanip
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\IOMANIP
++ios
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\IOS
++iosfwd
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\IOSFWD
++iostream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\IOSTREAM
++IPExport.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IPEXPORT.H
++iphlpapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IPHLPAPI.H
++ipifcons.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IPIFCONS.H
++ipmib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IPMIB.H
++Iprtrmib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IPRTRMIB.H
++IPTypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\IPTYPES.H
++ipv6prefast.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\IPV6PREFAST.H
++istream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ISTREAM
++iterator
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ITERATOR
++jobapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\JOBAPI.H
++jobapi2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\JOBAPI2.H
++joystickapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\JOYSTICKAPI.H
++kernelspecs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\KERNELSPECS.H
++ktmtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\KTMTYPES.H
++libloaderapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LIBLOADERAPI.H
++limits
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\LIMITS
++limits.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\LIMITS.H
++list
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\LIST
++LM.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LM.H
++LMaccess.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMACCESS.H
++LMalert.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMALERT.H
++LMAPIbuf.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMAPIBUF.H
++LMaudit.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMAUDIT.H
++LMConfig.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMCONFIG.H
++lmcons.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\LMCONS.H
++lmerr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\LMERR.H
++LMErrlog.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMERRLOG.H
++LMJoin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMJOIN.H
++LMMsg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMMSG.H
++LMRemUtl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMREMUTL.H
++LMRepl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMREPL.H
++LMServer.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMSERVER.H
++LMShare.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMSHARE.H
++LMSName.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMSNAME.H
++lmstats.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMSTATS.H
++LMSvc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMSVC.H
++LMUse.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMUSE.H
++lmuseflg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMUSEFLG.H
++lmwksta.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LMWKSTA.H
++locale
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\LOCALE
++locale.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\LOCALE.H
++lzexpand.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\LZEXPAND.H
++malloc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\MALLOC.H
++map
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\MAP
++math.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\MATH.H
++mciapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MCIAPI.H
++mcx.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MCX.H
++memory
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\MEMORY
++memoryapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MEMORYAPI.H
++minidumpapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MINIDUMPAPISET.H
++minwinbase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MINWINBASE.H
++minwindef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\MINWINDEF.H
++mm3dnow.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\MM3DNOW.H
++mmeapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MMEAPI.H
++mmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\MMINTRIN.H
++mmiscapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MMISCAPI.H
++mmiscapi2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MMISCAPI2.H
++mmsyscom.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MMSYSCOM.H
++mmsystem.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MMSYSTEM.H
++mprapidef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\MPRAPIDEF.H
++msxml.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\MSXML.H
++namedpipeapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\NAMEDPIPEAPI.H
++namespaceapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\NAMESPACEAPI.H
++nb30.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\NB30.H
++nbluro.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\NBLURO.H
++ncrypt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\NCRYPT.H
++ndisport.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\NDISPORT.H
++ndkinfo.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDKINFO.H
++netioapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NETIOAPI.H
++new
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\NEW
++new.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\NEW.H
++nicswitchtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\NICSWITCHTYPES.H
++nldef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NLDEF.H
++nmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\NMMINTRIN.H
++no_sal2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NO_SAL2.H
++ntddndis.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NTDDNDIS.H
++oaidl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OAIDL.H
++objbase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OBJBASE.H
++objectheader.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\OBJECTHEADER.H
++objidl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OBJIDL.H
++objidlbase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OBJIDLBASE.H
++ocidl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OCIDL.H
++offloadtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\OFFLOADTYPES.H
++oidtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\OIDTYPES.H
++ole.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OLE.H
++ole2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OLE2.H
++oleauto.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OLEAUTO.H
++olectl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OLECTL.H
++oleidl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\OLEIDL.H
++optional
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\OPTIONAL
++ostream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\OSTREAM
++pciprop.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\PCIPROP.H
++playsoundapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PLAYSOUNDAPI.H
++pmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\PMMINTRIN.H
++poppack.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\POPPACK.H
++process.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\PROCESS.H
++processenv.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROCESSENV.H
++processthreadsapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROCESSTHREADSAPI.H
++processtopologyapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROCESSTOPOLOGYAPI.H
++profileapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROFILEAPI.H
++PropIdl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROPIDL.H
++PropIdlBase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PROPIDLBASE.H
++prsht.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PRSHT.H
++prsht.inl
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\PRSHT.INL
++pshpack1.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\PSHPACK1.H
++pshpack2.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\PSHPACK2.H
++pshpack4.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\PSHPACK4.H
++pshpack8.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\PSHPACK8.H
++realtimeapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\REALTIMEAPISET.H
++reason.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\REASON.H
++RestrictedErrorInfo.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\RESTRICTEDERRORINFO.H
++roerrorapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\WINRT\ROERRORAPI.H
++rpc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPC.H
++rpcasync.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCASYNC.H
++rpcdce.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCDCE.H
++rpcdcep.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCDCEP.H
++rpcndr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCNDR.H
++rpcnsi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\RPCNSI.H
++rpcnsip.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\RPCNSIP.H
++rpcnterr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCNTERR.H
++rpcsal.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\RPCSAL.H
++sal.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SAL.H
++SCardErr.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SCARDERR.H
++sdkddkver.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\SDKDDKVER.H
++sdv_driverspecs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\SDV_DRIVERSPECS.H
++securityappcontainer.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SECURITYAPPCONTAINER.H
++securitybaseapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SECURITYBASEAPI.H
++servprov.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SERVPROV.H
++set
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SET
++setjmp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SETJMP.H
++setjmpex.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SETJMPEX.H
++share.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\SHARE.H
++shellapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SHELLAPI.H
++signal.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\SIGNAL.H
++smmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SMMINTRIN.H
++softintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SOFTINTRIN.H
++sourceannotations.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\CODEANALYSIS\SOURCEANNOTATIONS.H
++specstrings.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\SPECSTRINGS.H
++specstrings_strict.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\SPECSTRINGS_STRICT.H
++specstrings_undef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\SPECSTRINGS_UNDEF.H
++sql.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SQL.H
++sqlext.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SQLEXT.H
++sqltypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SQLTYPES.H
++sqlucode.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SQLUCODE.H
++sstream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SSTREAM
++stat.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\SYS\STAT.H
++stdarg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STDARG.H
++stddef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\STDDEF.H
++stdexcept
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STDEXCEPT
++stdint.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STDINT.H
++stdio.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\STDIO.H
++stdlib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\STDLIB.H
++stralign.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\STRALIGN.H
++streambuf
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STREAMBUF
++string
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STRING
++string.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\STRING.H
++stringapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\STRINGAPISET.H
++strstream
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\STRSTREAM
++synchapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SYNCHAPI.H
++sysinfoapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SYSINFOAPI.H
++system_error
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\SYSTEM_ERROR
++systemtopologyapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\SYSTEMTOPOLOGYAPI.H
++tcpestats.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\TCPESTATS.H
++tcpmib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\TCPMIB.H
++threadpoolapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\THREADPOOLAPISET.H
++threadpoollegacyapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\THREADPOOLLEGACYAPISET.H
++time.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\TIME.H
++timeapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\TIMEAPI.H
++timezoneapi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\TIMEZONEAPI.H
++tmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\TMMINTRIN.H
++tuple
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\TUPLE
++tvout.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\TVOUT.H
++type_traits
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\TYPE_TRAITS
++typeinfo
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\TYPEINFO
++types.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\SYS\TYPES.H
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\TYPES.H
++udpmib.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\UDPMIB.H
++Unknwn.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\UNKNWN.H
++Unknwnbase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\UNKNWNBASE.H
++unordered_map
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\UNORDERED_MAP
++urlmon.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\URLMON.H
++use_ansi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\USE_ANSI.H
++utilapiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\UTILAPISET.H
++utility
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\UTILITY
++vadefs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VADEFS.H
++vcruntime.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME.H
++vcruntime_exception.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_EXCEPTION.H
++vcruntime_new.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_NEW.H
++vcruntime_new_debug.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_NEW_DEBUG.H
++vcruntime_startup.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_STARTUP.H
++vcruntime_string.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_STRING.H
++vcruntime_typeinfo.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VCRUNTIME_TYPEINFO.H
++vector
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\VECTOR
++verrsrc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\VERRSRC.H
++version.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\NDIS\VERSION.H
++WbemCli.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WBEMCLI.H
++WbemDisp.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WBEMDISP.H
++WbemIdl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WBEMIDL.H
++WbemProv.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WBEMPROV.H
++WbemTran.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WBEMTRAN.H
++wchar.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UCRT\WCHAR.H
++widemath.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WIDEMATH.H
++winapifamily.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINAPIFAMILY.H
++WinBase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINBASE.H
++winbase.inl
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINBASE.INL
++wincon.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINCON.H
++wincontypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINCONTYPES.H
++wincrypt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINCRYPT.H
++windef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINDEF.H
++windot11.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINDOT11.H
++Windows.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINDOWS.H
++winefs.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINEFS.H
++winerror.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINERROR.H
++wingdi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINGDI.H
++wininet.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WININET.H
++winioctl.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINIOCTL.H
++winnetwk.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINNETWK.H
++WinNls.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINNLS.H
++winnt.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINNT.H
++winnt.rh
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINNT.RH
++winpackagefamily.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINPACKAGEFAMILY.H
++winperf.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINPERF.H
++winreg.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINREG.H
++winresrc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINRESRC.H
++winscard.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINSCARD.H
++winsmcrd.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WINSMCRD.H
++winsock.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINSOCK.H
++winspool.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINSPOOL.H
++winsvc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINSVC.H
++WinTrust.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINTRUST.H
++WinUser.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINUSER.H
++winuser.inl
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINUSER.INL
++winuser.rh
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINUSER.RH
++winver.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WINVER.H
++wlantypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WLANTYPES.H
++wmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\WMMINTRIN.H
++wnnc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WNNC.H
++wow64apiset.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\UM\WOW64APISET.H
++ws2def.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WS2DEF.H
++ws2ipdef.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WS2IPDEF.H
++wtypes.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WTYPES.H
++WTypesbase.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.26100.0\SHARED\WTYPESBASE.H
++xatomic.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XATOMIC.H
++xatomic_wait.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XATOMIC_WAIT.H
++xbit_ops.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XBIT_OPS.H
++xcall_once.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XCALL_ONCE.H
++xcharconv.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XCHARCONV.H
++xcharconv_ryu.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XCHARCONV_RYU.H
++xcharconv_ryu_tables.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XCHARCONV_RYU_TABLES.H
++xcharconv_tables.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XCHARCONV_TABLES.H
++xerrc.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XERRC.H
++xfacet
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XFACET
++xfilesystem_abi.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XFILESYSTEM_ABI.H
++xhash
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XHASH
++xiosbase
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XIOSBASE
++xkeycheck.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XKEYCHECK.H
++xlocale
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCALE
++xlocbuf
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCBUF
++xlocinfo
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCINFO
++xlocmes
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCMES
++xlocmon
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCMON
++xlocnum
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCNUM
++xloctime
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XLOCTIME
++xmemory
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XMEMORY
++xmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XMMINTRIN.H
++xnode_handle.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XNODE_HANDLE.H
++xpolymorphic_allocator.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XPOLYMORPHIC_ALLOCATOR.H
++xsmf_control.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XSMF_CONTROL.H
++xstring
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XSTRING
++xthreads.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XTHREADS.H
++xtimec.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XTIMEC.H
++xtr1common
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XTR1COMMON
++xtree
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XTREE
++xutility
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\XUTILITY
++yvals.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\YVALS.H
++yvals_core.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\YVALS_CORE.H
++zmmintrin.h
e:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\VC\TOOLS\MSVC\14.40.33807\INCLUDE\ZMMINTRIN.H
++Header Files
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:Header Files
++AmplificationOfBlood.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\AmplificationOfBlood.h
++AnkleAmputate.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\AnkleAmputate.h
++ArmorBreaker.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ArmorBreaker.h
++ArmorPut.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ArmorPut.h
++ArrowExplosion.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ArrowExplosion.h
++ArrowRain.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ArrowRain.h
++ArrowsOfTheMaster.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ArrowsOfTheMaster.h
++Assault.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Assault.h
++AutoLearn.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\AutoLearn.h
++base64.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\base64.h
++Behead.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Behead.h
++Blessing.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Blessing.h
++BlessingOfAgility.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BlessingOfAgility.h
++BlessingOfCriticalHit.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BlessingOfCriticalHit.h
++BlessingOfHealth.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BlessingOfHealth.h
++BlessingOfIntelligence.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BlessingOfIntelligence.h
++BlessingOfStrength.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BlessingOfStrength.h
++BloodSuction.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BloodSuction.h
++Bombing.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Bombing.h
++Buffer.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Buffer.h
++BuyItemEx.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\BuyItemEx.h
++Calculations.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Calculations.h
++Calls.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Calls.h
++CanAttack.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CanAttack.h
++CastleWarFix.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CastleWarFix.h
++CBaseDelete.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CBaseDelete.h
++CChar.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CChar.h
++CDBProcess.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CDBProcess.h
++Channel.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Channel.h
++Cleaner.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Cleaner.h
++Collapse.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Collapse.h
++CombativeSpirit.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CombativeSpirit.h
++Command.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Command.h
++ConcurrentMap.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ConcurrentMap.h
++ConcurrentSet.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ConcurrentSet.h
++ConcurrentVector.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ConcurrentVector.h
++Confusion.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Confusion.h
++ContinuesSkill.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ContinuesSkill.h
++CounterDamage.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CounterDamage.h
++CreateBuff.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CreateBuff.h
++CriticalDiffusion.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CriticalDiffusion.h
++CriticalLock.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CriticalLock.h
++CriticalSection.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CriticalSection.h
++CriticalStrike.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CriticalStrike.h
++CutDownExp.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\CutDownExp.h
++DefenseChangePrefix.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DefenseChangePrefix.h
++DefenseImprovement.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DefenseImprovement.h
++DestroyingArmor.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DestroyingArmor.h
++DirectoryReader.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DirectoryReader.h
++dirent.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\dirent.h
++Doggebi.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Doggebi.h
++DrainBlood.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DrainBlood.h
++DropItem.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DropItem.h
++DualShadow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DualShadow.h
++Duel.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Duel.h
++DuelTournament.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\DuelTournament.h
++EagleStat.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\EagleStat.h
++EggExp.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\EggExp.h
++EggSkill.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\EggSkill.h
++EggThunderbolt.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\EggThunderbolt.h
++Entangling.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Entangling.h
++ExecutiveDirector.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ExecutiveDirector.h
++ExplodingSpirit.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ExplodingSpirit.h
++Exports.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Exports.h
++ExpTable.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ExpTable.h
++FatalWound.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\FatalWound.h
++FinalBlow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\FinalBlow.h
++FinalDamage.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\FinalDamage.h
++Fireball.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Fireball.h
++FlameInjection.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\FlameInjection.h
++Functions.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Functions.h
++Gangshin4th.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Gangshin4th.h
++GetBuff.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\GetBuff.h
++GhostFlash.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\GhostFlash.h
++GhostKnife.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\GhostKnife.h
++GhostWindow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\GhostWindow.h
++Guild.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Guild.h
++Hiding.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Hiding.h
++HighClassHiding.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\HighClassHiding.h
++IBuff.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IBuff.h
++IceArrow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IceArrow.h
++IChar.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IChar.h
++Icicle.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Icicle.h
++IItem.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IItem.h
++Incapacitation.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Incapacitation.h
++Interface.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Interface.h
++IQuest.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IQuest.h
++ISkill.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ISkill.h
++ItemFixes.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ItemFixes.h
++ItemType.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ItemType.h
++ItemUse.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ItemUse.h
++JeungjangKing.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\JeungjangKing.h
++JeungjangKingOfTaein.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\JeungjangKingOfTaein.h
++JigukKing.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\JigukKing.h
++JigukKingOfTaein.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\JigukKingOfTaein.h
++Lawless.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Lawless.h
++LevelUp.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\LevelUp.h
++LifeAbsorption.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\LifeAbsorption.h
++LightningArrow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\LightningArrow.h
++LightningSlash.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\LightningSlash.h
++Lisans.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Lisans.h
++Loader.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Loader.h
++Lock.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Lock.h
++MagicalExplosion.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MagicalExplosion.h
++MapData.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MapData.h
++MassiveFire.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MassiveFire.h
++MD5.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MD5.h
++Memory.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Memory.h
++MentalBreakdown.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MentalBreakdown.h
++Menu.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Menu.h
++Mix.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Mix.h
++MonsterBlob.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MonsterBlob.h
++MudRoom.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MudRoom.h
++MutexMap.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MutexMap.h
++MyIterator.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\MyIterator.h
++NormalHit.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\NormalHit.h
++NPC.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\NPC.h
++OneHitStrike.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\OneHitStrike.h
++OnLoadPlayer.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\OnLoadPlayer.h
++Ornament.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Ornament.h
++Packet.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Packet.h
++PartyVsParty.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\PartyVsParty.h
++PasswordDecode.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\PasswordDecode.h
++PerfectDefense.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\PerfectDefense.h
++Player.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Player.h
++Pointer.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Pointer.h
++PowerfulWideningWound.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\PowerfulWideningWound.h
++Process.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Process.h
++ProvocationOfBlow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ProvocationOfBlow.h
++Punishment.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Punishment.h
++Quest.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Quest.h
++ReadConfig.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ReadConfig.h
++RefiningWeapon.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RefiningWeapon.h
++RefreshBuff.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RefreshBuff.h
++Registration.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Registration.h
++RegistrationMap.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RegistrationMap.h
++ReleasingTheEnergy.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ReleasingTheEnergy.h
++Reload.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Reload.h
++RemoteLisans.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RemoteLisans.h
++ResetContinueSkill.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ResetContinueSkill.h
++Revival.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Revival.h
++RevolveAttack.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RevolveAttack.h
++RisingKing4th.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\RisingKing4th.h
++Rupture.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Rupture.h
++SaveAllProperty.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SaveAllProperty.h
++SendMail.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SendMail.h
++Sha256.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Sha256.h
++ShadowSlash.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ShadowSlash.h
++ShinRhoe.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ShinRhoe.h
++ShoutOfDefense.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ShoutOfDefense.h
++ShoutOfFightingSpirit.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ShoutOfFightingSpirit.h
++SixSouls.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SixSouls.h
++Skill.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Skill.h
++SkillCalculation.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SkillCalculation.h
++SkillCheck.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SkillCheck.h
++SkillPointer.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SkillPointer.h
++SkillUpgradeCheck.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SkillUpgradeCheck.h
++SoulBlow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SoulBlow.h
++SoulShield.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SoulShield.h
++SpearOfPain.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpearOfPain.h
++SpeedUp.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpeedUp.h
++SpinAttack.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpinAttack.h
++SpinBlade.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpinBlade.h
++SpinSlash.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpinSlash.h
++SpiritOfTheArrows.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpiritOfTheArrows.h
++SpiritWave.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SpiritWave.h
++sqlite3.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\sqlite3.h
++StandardPut.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\StandardPut.h
++Start.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Start.h
++StatPointValue.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\StatPointValue.h
++StoneOfBirth.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\StoneOfBirth.h
++StoneOfChance.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\StoneOfChance.h
++Storage.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Storage.h
++Strangle.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Strangle.h
++StrikeOfGod.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\StrikeOfGod.h
++Stun.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Stun.h
++SufferingValley.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SufferingValley.h
++SuicidalBlow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SuicidalBlow.h
++Summon.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Summon.h
++SwordDance.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SwordDance.h
++SystemRegistration.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\SystemRegistration.h
++TargetFind.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TargetFind.h
++TheBoomOfEarth.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TheBoomOfEarth.h
++TherapeuticTouch.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TherapeuticTouch.h
++TheSoulsPenance.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TheSoulsPenance.h
++TheWaveOfEarth.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TheWaveOfEarth.h
++Thunderbolt.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Thunderbolt.h
++Time.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Time.h
++Timer.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Timer.h
++Tools.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Tools.h
++Trade.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Trade.h
++Transform.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Transform.h
++TriangularBattle.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TriangularBattle.h
++TwinBladeStrike.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\TwinBladeStrike.h
++VirulentArrow.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\VirulentArrow.h
++VitalStrike.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\VitalStrike.h
++WalkOnTheAir.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WalkOnTheAir.h
++Wave.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Wave.h
++WaveOfEmperor.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WaveOfEmperor.h
++WeaponChangePrefix.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WeaponChangePrefix.h
++WeaponPut.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WeaponPut.h
++WhirlwindFeather.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WhirlwindFeather.h
++WorldCup.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WorldCup.h
++WrathOfHeaven.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\WrathOfHeaven.h
++XEATools.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\XEATools.h
++ZilPoong.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ZilPoong.h
++Source Files
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:Source Files
++base64.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\base64.cpp
++Core.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Core.cpp
++Exports.def
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Exports.def
++IBuff.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IBuff.cpp
++IChar.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IChar.cpp
++IItem.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IItem.cpp
++Interface.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Interface.cpp
++IQuest.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\IQuest.cpp
++ISkill.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ISkill.cpp
++Memory.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Memory.cpp
++mprintf.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\curl\mprintf.h
++multi.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\curl\multi.h
++sha256.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\sha256.cpp
++shell.c
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\shell.c
++sqlite3.c
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\sqlite3.c
++stdcheaders.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\curl\stdcheaders.h
++system.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\curl\system.h
++Tools.cpp
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\Tools.cpp
++typecheck-gcc.h
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\curl\typecheck-gcc.h
++ClassDiagram.cd
i:{1edfcae1-9cf5-42f7-9751-66d16c902aae}:B:\KalOnline Hacks\XEAxMaste TheHyperNetwork\XEAxMaste - TheHyperNetwork Sources\Core\Core\ClassDiagram.cd
