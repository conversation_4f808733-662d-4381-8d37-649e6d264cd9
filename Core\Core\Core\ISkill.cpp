#include <windows.h>
#include "Functions.h"
#include "ISkill.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>

ISkill::ISkill(void* Offset)
{
	if (!Offset) {
		throw std::invalid_argument("ISkill::ISkill: Offset cannot be null");
	}
	this->Offset = Offset;
}

ISkill::~ISkill()
{
}

void *ISkill::GetOffset()
{
	if (!this->Offset) {
		throw std::runtime_error("ISkill::GetOffset: Offset is null");
	}
	return this->Offset;
}

int ISkill::GetIndex()
{
	try {
		if (this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 4)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int ISkill::GetGrade()
{
	try {
		if (this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 8)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int ISkill::GetLoadingTime()
{
	try {
		if (this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 56)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int ISkill::GetMaxGrade()
{
	try {
		if (this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 28)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int ISkill::DecreaseMana()
{
	try {
		if (this->Offset) {
			DWORD* vtable = reinterpret_cast<DWORD*>(this->Offset);
			if (!vtable || !*vtable) {
				return 0;
			}
			return (*(int (__thiscall **)(void*, DWORD))(*vtable + 0x20))(this->Offset, 0);
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

void* ISkill::GetPlayer()
{
	try {
		if (this->Offset) {
			DWORD* playerPtr = reinterpret_cast<DWORD*>(this->Offset);
			if (!playerPtr) {
				return nullptr;
			}
			return reinterpret_cast<void*>(*playerPtr);
		}
		return nullptr;
	} catch (...) {
		return nullptr;
	}
}
