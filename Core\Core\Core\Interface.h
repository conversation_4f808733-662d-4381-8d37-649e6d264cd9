/**
 * @file Interface.h
 * @brief Interface management system for the Core game server modification
 *
 * This file provides a singleton-based interface management system that allows
 * for centralized creation and access to various game system interfaces.
 * Uses template-based design for type safety and automatic interface registration.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __INTERFACE_H
#define __INTERFACE_H

#include <map>
#include <typeinfo>
#include <stdexcept>
#include <exception>
#include <iostream>
#include <memory>
/**
 * @brief Macro to automatically create and register an interface
 *
 * This macro creates a static initializer that automatically registers
 * an interface with the InterfaceManager when the module is loaded.
 *
 * @param I The interface class type to create
 */
#define CREATE_INTERFACE(I) \
class I; \
class __Init_##I \
	{ \
	public: \
	__Init_##I() { Interface<##I>::Create(); } \
	~__Init_##I() { } \
	} __i_Init_##I;

/**
 * @brief Singleton manager for all game interfaces
 *
 * The InterfaceManager provides centralized management of all interface instances
 * in the game server. It uses a singleton pattern to ensure global access and
 * maintains a type-safe registry of interface instances.
 */
class InterfaceManager
{
private:
	static InterfaceManager* _pInstance;  ///< Singleton instance pointer
	std::map<size_t, void*> _Instances;   ///< Map of type hash codes to interface instances

public:
	InterfaceManager() { this->_Instances.clear(); }

	void Add(size_t hash, void* Instance)
	{
		try {
			if (!Instance) {
				throw std::invalid_argument("InterfaceManager::Add: Instance cannot be null");
			}
			if (!this->_Instances.count(hash)) {
				this->_Instances[hash] = Instance;
			}
		} catch (...) {
			// Silently handle errors to maintain compatibility
		}
	}

	void *Query(size_t hash)
	{
		try {
			auto it = this->_Instances.find(hash);
			return (it != this->_Instances.end()) ? it->second : nullptr;
		} catch (...) {
			return nullptr;
		}
	}

	static __declspec(noinline) InterfaceManager* _GetInstance()
	{
		try {
			if (!InterfaceManager::_pInstance) {
				InterfaceManager::_pInstance = new InterfaceManager;
			}
			return InterfaceManager::_pInstance;
		} catch (...) {
			return nullptr;
		}
	}
};

template <class T>

class Interface
{
private:
	T *_Interface;

public:
	static __declspec(noinline) void Create() {
		try {
			InterfaceManager *iM = InterfaceManager::_GetInstance();
			if (iM) {
				T* instance = new T;
				iM->Add(typeid(T).hash_code(), instance);
			}
		} catch (...) {
			// Silently handle errors to maintain compatibility
		}
	}

	static __declspec(noinline) void Create(T Pointer) {
		try {
			InterfaceManager *iM = InterfaceManager::_GetInstance();
			if (iM) {
				iM->Add(typeid(T).hash_code(), Pointer);
			}
		} catch (...) {
			// Silently handle errors to maintain compatibility
		}
	}

	Interface() {
		try {
			InterfaceManager *iM = InterfaceManager::_GetInstance();
			this->_Interface = iM ? (T*)iM->Query(typeid(T).hash_code()) : nullptr;
		} catch (...) {
			this->_Interface = nullptr;
		}
	}

	~Interface() { }

	operator T*() const { return _Interface; }

	T* operator->() {
		if (!_Interface) {
			throw std::runtime_error("Interface::operator->: Interface is null");
		}
		return _Interface;
	}
};

#endif

/*#ifndef __INTERFACE_H
#define __INTERFACE_H

#include <map>
#include <typeinfo>

#define CREATE_INTERFACE(I) \
class I; \
class __Init_##I \
	{ \
	public: \
	__Init_##I() { Interface<##I>::Create(); } \
	~__Init_##I() { } \
	} __i_Init_##I;

class InterfaceManager
{
private:
	static InterfaceManager* _pInstance;
	std::map<size_t, void*> _Instances;

public:
	InterfaceManager() { this->_Instances.clear(); }

	void Add(size_t hash, void* Instance)
	{
		if (!this->_Instances.count(hash))
			this->_Instances[hash] = Instance;
	}

	void *Query(size_t hash)
	{
		void *q = (this->_Instances.count(hash)) ? this->_Instances[hash] : NULL;
		return q;
	}

	static __declspec(noinline) InterfaceManager* _GetInstance()
	{
		if (!InterfaceManager::_pInstance) InterfaceManager::_pInstance = new InterfaceManager;
		return InterfaceManager::_pInstance;
	}
};

template <class T>

class Interface
{
private:
	T *_Interface;

public:
	static __declspec(noinline) void Create() { InterfaceManager *iM = InterfaceManager::_GetInstance(); iM->Add(typeid(T).hash_code(), new T); }
	static __declspec(noinline) void Create(T Pointer) { InterfaceManager *iM = InterfaceManager::_GetInstance(); iM->Add(typeid(T).hash_code(), Pointer); }
	Interface() { InterfaceManager *iM = InterfaceManager::_GetInstance(); this->_Interface = (T*)iM->Query(typeid(T).hash_code()); }
	~Interface() { }
	operator T*() const { return _Interface; }
	T* operator->() { return _Interface; }
};

#endif*/