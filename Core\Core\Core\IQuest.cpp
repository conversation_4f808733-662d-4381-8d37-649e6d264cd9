#include <windows.h>
#include "IQuest.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>

IQuest::IQuest(void *Offset)
{
    if (!Offset) {
        throw std::invalid_argument("IQuest::IQuest: Offset cannot be null");
    }
    this->Offset = Offset;
}

IQuest::~IQuest()
{
}

void *IQuest::GetOffset()
{
    if (!this->Offset) {
        throw std::runtime_error("IQuest::GetOffset: Offset is null");
    }
    return this->Offset;
}

int IQuest::GetIndex()
{
	try {
		if (this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 34)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int IQuest::GetFlag()
{
	try {
		if (this->Offset) {
			int index = this->GetIndex();
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 32))) - (65536 * index);
		}
		return 0;
	} catch (...) {
		return 0;
	}
}