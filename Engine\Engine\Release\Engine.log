﻿  PacketProtection.cpp
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(247,16): error C2248: 'AntiCheat::packetTimings': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(110,49):
      see declaration of 'AntiCheat::packetTimings'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(248,16): error C2248: 'AntiCheat::packetCounts': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(111,61):
      see declaration of 'AntiCheat::packetCounts'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(249,16): error C2248: 'AntiCheat::packetHistory': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(112,54):
      see declaration of 'AntiCheat::packetHistory'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(309,19): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(320,16): error C2248: 'AntiCheat::packetTimings': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(110,49):
      see declaration of 'AntiCheat::packetTimings'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(323,32): error C2248: 'AntiCheat::packetTimings': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(110,49):
      see declaration of 'AntiCheat::packetTimings'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(328,16): error C2248: 'AntiCheat::packetCounts': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(111,61):
      see declaration of 'AntiCheat::packetCounts'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(331,32): error C2248: 'AntiCheat::packetHistory': cannot access private member declared in class 'AntiCheat'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(112,54):
      see declaration of 'AntiCheat::packetHistory'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(97,7):
      see declaration of 'AntiCheat'
  
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(356,25): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(445,28): warning C4018: '>=': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(469,30): error C2248: 'PacketProtection::CalculateChecksum': cannot access private member declared in class 'PacketProtection'
      G:\Kal\New Project\Packet Protection\PacketProtection.cpp(81,28):
      see declaration of 'PacketProtection::CalculateChecksum'
      G:\Kal\New Project\Packet Protection\PacketProtection.h(12,7):
      see declaration of 'PacketProtection'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\random(2340,5): error C2338: static_assert failed: 'invalid template argument for uniform_int_distribution: N4950 [rand.req.genl]/1.5 requires one of short, int, long, long long, unsigned short, unsigned int, unsigned long, or unsigned long long'
  (compiling source file '../../PacketProtection.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\random(2340,5):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Packet Protection\PacketProtection.cpp(532,57):
          see reference to class template instantiation 'std::uniform_int_distribution<unsigned char>' being compiled
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\random(2340,5): error C2338: static_assert failed: 'note: char, signed char, unsigned char, char8_t, int8_t, and uint8_t are not allowed'
  (compiling source file '../../PacketProtection.cpp')
  
