﻿  PacketProtection.cpp
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(283,19): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(330,25): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(419,28): warning C4018: '>=': signed/unsigned mismatch
  Packets.cpp
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(497,38): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(511,32): warning C4267: '=': conversion from 'size_t' to 'unsigned short', possible loss of data
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1445,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1469,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1780,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1791,37): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1796,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1820,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1852,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1884,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1890,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1935,40): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1958,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1963,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2225,45): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2277,46): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2318,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2323,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Packets.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Packet Protection\Engine\Engine\Hardware.h(156,13):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              G:\Kal\New Project\Packet Protection\Engine\Engine\Hardware.h(156,13):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'getHWID'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned int,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned int
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned int,
              _InIt=wchar_t *,
              _SizeTy=unsigned int
          ]
  
     Creating library G:\Kal\New Project\Packet Protection\Engine\Release\Engine.lib and object G:\Kal\New Project\Packet Protection\Engine\Release\Engine.exp
  Generating code
  78 of 3186 functions ( 2.4%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    5 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  Engine.vcxproj -> G:\Kal\New Project\Packet Protection\Engine\Release\Engine.dll
