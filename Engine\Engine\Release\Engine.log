﻿  Packets.cpp
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(497,38): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(511,32): warning C4267: '=': conversion from 'size_t' to 'unsigned short', possible loss of data
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1399,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1423,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1734,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1745,37): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1750,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1774,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1806,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1838,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1844,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1889,40): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1912,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1917,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2179,45): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2231,46): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2272,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2277,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Packets.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Packet Protection\Engine\Engine\Hardware.h(156,13):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              G:\Kal\New Project\Packet Protection\Engine\Engine\Hardware.h(156,13):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'getHWID'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned int,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned int
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned int,
              _InIt=wchar_t *,
              _SizeTy=unsigned int
          ]
  
     Creating library G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.lib and object G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.exp
Packets.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::DetectPacketInjection(int,char const *,unsigned int)" (?DetectPacketInjection@PacketProtection@@SA_NHPBDI@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::ValidateClientToken(int,unsigned int,unsigned char)" (?ValidateClientToken@PacketProtection@@SA_NHIE@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static unsigned int __cdecl PacketProtection::GenerateClientToken(int,unsigned char)" (?GenerateClientToken@PacketProtection@@SAIHE@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::EncryptPacket(int,char *,unsigned int,unsigned char)" (?EncryptPacket@PacketProtection@@SA_NHPADIE@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::InitializeClientProtection(int)" (?InitializeClientProtection@PacketProtection@@SA_NH@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl AntiCheat::AnalyzeSuspiciousActivity(int)" (?AnalyzeSuspiciousActivity@AntiCheat@@SA_NH@Z)
Packets.obj : error LNK2001: unresolved external symbol "public: static void __cdecl AntiCheat::UpdatePlayerBehavior(int,unsigned char)" (?UpdatePlayerBehavior@AntiCheat@@SAXHE@Z)
Packets.obj : error LNK2001: unresolved external symbol "struct ProtectionConfig g_protectionConfig" (?g_protectionConfig@@3UProtectionConfig@@A)
G:\Kal\New Project\Cleaning Sources\Sources\Engine\Release\Engine.dll : fatal error LNK1120: 8 unresolved externals
