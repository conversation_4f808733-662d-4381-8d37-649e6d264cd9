﻿  PacketProtection.cpp
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(309,19): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(356,25): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(445,28): warning C4018: '>=': signed/unsigned mismatch
  Packets.cpp
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(497,38): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(511,32): warning C4267: '=': conversion from 'size_t' to 'unsigned short', possible loss of data
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(553,36): error C2065: 'currentSocket': undeclared identifier
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(558,36): error C2065: 'currentSocket': undeclared identifier
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(559,45): error C2065: 'currentSocket': undeclared identifier
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(565,91): error C2065: 'currentSocket': undeclared identifier
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1311,37): error C2065: 'currentSocket': undeclared identifier
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1427,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1451,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1762,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1773,37): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1778,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1802,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1834,35): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1866,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1872,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1917,40): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1940,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(1945,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2207,45): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2259,46): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2300,44): warning C4302: 'type cast': truncation from 'char *' to 'char'
G:\Kal\New Project\Packet Protection\Engine\Engine\Packets.cpp(2305,36): warning C4302: 'type cast': truncation from 'char *' to 'char'
