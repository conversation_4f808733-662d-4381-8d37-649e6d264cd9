// Performance test for memory pool implementation
// This file can be used to verify the memory pool is working correctly

#include "MemoryPool.h"
#include <Windows.h>
#include <iostream>
#include <chrono>

void TestMemoryPool() {
    const int TEST_ITERATIONS = 10000;
    const size_t TEST_SIZE = 1024;
    
    // Test memory pool performance
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < TEST_ITERATIONS; i++) {
        char* data = g_PacketPool.Acquire(TEST_SIZE);
        // Simulate some work
        memset(data, 0, TEST_SIZE);
        g_PacketPool.Release(data, TEST_SIZE);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto pool_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // Test regular new/delete performance
    start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < TEST_ITERATIONS; i++) {
        char* data = new char[TEST_SIZE];
        // Simulate some work
        memset(data, 0, TEST_SIZE);
        delete[] data;
    }
    
    end = std::chrono::high_resolution_clock::now();
    auto regular_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // Output results
    std::cout << "Memory Pool Performance Test Results:" << std::endl;
    std::cout << "Pool allocation time: " << pool_duration.count() << " microseconds" << std::endl;
    std::cout << "Regular allocation time: " << regular_duration.count() << " microseconds" << std::endl;
    std::cout << "Performance improvement: " << (double)regular_duration.count() / pool_duration.count() << "x faster" << std::endl;
}

// Uncomment to run test
// int main() {
//     TestMemoryPool();
//     return 0;
// }
