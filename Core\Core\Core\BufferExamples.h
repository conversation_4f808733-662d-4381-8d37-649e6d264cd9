/**
 * @file BufferExamples.h
 * @brief Examples and best practices for using RAII buffer classes
 *
 * This file demonstrates how to use the new RAII-compliant buffer classes
 * to prevent memory leaks, buffer overflows, and other memory-related issues
 * that can cause crashes in the game server.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __BUFFER_EXAMPLES_H
#define __BUFFER_EXAMPLES_H

#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include "Buffer.h"
#include <iostream>
#include <string>

/**
 * @brief Examples of how RAII buffers prevent memory leaks and crashes
 */
namespace BufferExamples {

    /**
     * @brief Example 1: Basic SecureBuffer usage
     * 
     * This shows how SecureBuffer automatically manages memory and prevents leaks
     */
    void example_basic_secure_buffer() {
        try {
            // Create a secure buffer - memory is automatically allocated
            SecureByteBuffer buffer(1024);
            
            // Use the buffer safely with bounds checking
            buffer[0] = 0xFF;
            buffer[1023] = 0xAA;
            
            // This would throw an exception instead of causing a crash:
            // buffer[1024] = 0x00; // BufferOverflowException
            
            // Memory is automatically freed when buffer goes out of scope
            // No need for manual delete[] - RAII handles it!
            
        } catch (const BufferOverflowException& e) {
            std::cout << "Bounds check prevented crash: " << e.what() << std::endl;
        }
        // Buffer memory is automatically cleaned up here
    }

    /**
     * @brief Example 2: Safe packet handling
     * 
     * Shows how to safely handle network packets without buffer overflows
     */
    void example_safe_packet_handling() {
        try {
            // Create packet buffer with automatic cleanup
            SecurePacketBuffer packet(512);
            
            // Safely copy data with bounds checking
            const char* data = "Hello, World!";
            packet.safe_copy_from(reinterpret_cast<const unsigned char*>(data), 
                                strlen(data) + 1);
            
            // Safe access with automatic bounds checking
            for (size_t i = 0; i < packet.size() && packet[i] != 0; ++i) {
                std::cout << static_cast<char>(packet[i]);
            }
            std::cout << std::endl;
            
            // Memory is securely wiped and freed automatically
            
        } catch (const std::exception& e) {
            std::cout << "Safe packet handling prevented crash: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 3: Replacing unsafe manual memory management
     * 
     * Shows how to replace dangerous manual new/delete with RAII
     */
    void example_replace_manual_memory() {
        // OLD UNSAFE WAY (prone to leaks and crashes):
        /*
        char* unsafe_buffer = new char[1024];
        // ... use buffer ...
        // If exception occurs here, memory leaks!
        delete[] unsafe_buffer; // Easy to forget or miss in error paths
        */
        
        // NEW SAFE WAY with RAII:
        {
            SecureCharBuffer safe_buffer(1024);
            
            // Use buffer safely
            safe_buffer.fill('A');
            safe_buffer.zero(); // Clear contents
            
            // Even if exception occurs, memory is automatically cleaned up
            // No manual delete needed - RAII handles everything!
        } // Memory automatically freed here
    }

    /**
     * @brief Example 4: Thread-safe buffer operations
     * 
     * Shows how SecureBuffer integrates with existing Lock system
     */
    void example_thread_safe_operations() {
        SecureByteBuffer shared_buffer(1024);
        
        // The buffer has built-in thread safety using your existing Lock class
        // Multiple threads can safely access the buffer
        
        // Manual locking for complex operations:
        {
            MutexMap lock(shared_buffer.get_lock());
            
            // Perform multiple operations atomically
            shared_buffer.zero();
            shared_buffer[0] = 0xFF;
            shared_buffer[1] = 0xAA;
            
            // Lock is automatically released when scope ends
        }
    }

    /**
     * @brief Example 5: Using MemoryGuard for C-style APIs
     * 
     * Shows how to safely wrap C-style memory allocation
     */
    void example_memory_guard_usage() {
        try {
            // Safe malloc wrapper with automatic cleanup
            MallocGuard memory(1024);
            
            if (memory.is_valid()) {
                // Use memory safely
                char* ptr = memory.get_as<char>();
                strcpy_s(ptr, 1024, "Safe memory usage");
                
                // Memory is automatically freed and securely wiped
            }
            
        } catch (const std::bad_alloc& e) {
            std::cout << "Memory allocation failed safely: " << e.what() << std::endl;
        }
        // Memory automatically freed here, even if exception occurred
    }

    /**
     * @brief Example 6: Resource management with ResourceGuard
     * 
     * Shows how to safely manage any resource with custom cleanup
     */
    void example_resource_guard() {
        // Example: Safe file handle management
        HANDLE file = CreateFileA("test.txt", GENERIC_READ, 0, nullptr, 
                                 OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
        
        if (file != INVALID_HANDLE_VALUE) {
            // Wrap handle in ResourceGuard for automatic cleanup
            ResourceGuard<HANDLE> file_guard(file, [](HANDLE h) {
                CloseHandle(h);
                std::cout << "File handle automatically closed" << std::endl;
            });
            
            // Use file safely
            // Even if exception occurs, file handle is automatically closed
        }
    }

    /**
     * @brief Example 7: Scope-based cleanup
     * 
     * Shows how to use ScopeGuard for automatic cleanup of arbitrary code
     */
    void example_scope_guard() {
        bool resource_acquired = false;
        
        // Automatic cleanup when scope ends
        SCOPE_GUARD({
            if (resource_acquired) {
                std::cout << "Cleaning up resources automatically" << std::endl;
                // Perform cleanup operations
            }
        });
        
        // Acquire some resource
        resource_acquired = true;
        
        // Even if exception occurs, cleanup code runs automatically
        // when function exits
    }

    /**
     * @brief Example 8: Migrating existing unsafe code
     * 
     * Shows how to migrate from the old Buffer class to the new safe version
     */
    void example_migration_from_old_buffer() {
        // Old code (still works but now safer):
        try {
            Buffer packet(1024); // Now uses RAII internally
            
            char* data = packet.getPacket(); // Still works for compatibility
            
            // But now you can also use safe methods:
            packet.at(0) = 'H';  // Safe access with bounds checking
            packet.zero();       // Safe zeroing
            
            // Memory is automatically cleaned up - no manual delete needed!
            
        } catch (const std::exception& e) {
            std::cout << "Migration example caught error safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 9: Exception safety guarantees
     * 
     * Shows how RAII provides strong exception safety
     */
    void example_exception_safety() {
        try {
            SecureByteBuffer buffer1(1024);
            SecureByteBuffer buffer2(2048);
            
            // Fill buffers with data
            buffer1.fill(0xAA);
            buffer2.fill(0xBB);
            
            // Even if this operation throws an exception:
            buffer1.safe_copy_from(buffer2.data(), buffer2.size()); // May throw
            
            // Both buffers are still automatically cleaned up
            
        } catch (const BufferOverflowException& e) {
            std::cout << "Exception safely handled: " << e.what() << std::endl;
            // All buffers are automatically cleaned up even in exception path
        }
    }

    /**
     * @brief Example 10: Performance with move semantics
     * 
     * Shows how move semantics provide efficiency without sacrificing safety
     */
    SecureByteBuffer create_large_buffer() {
        SecureByteBuffer buffer(1024 * 1024); // 1MB buffer
        buffer.fill(0xFF);
        return buffer; // Efficiently moved, not copied
    }

    void example_move_semantics() {
        // Efficient transfer without copying - no performance penalty for safety
        SecureByteBuffer my_buffer = create_large_buffer();
        
        // Buffer is efficiently moved, memory is still safely managed
        std::cout << "Large buffer size: " << my_buffer.size() << " bytes" << std::endl;
        
        // Memory automatically cleaned up when my_buffer goes out of scope
    }

    /**
     * @brief Run all examples to demonstrate RAII buffer safety
     */
    void run_all_examples() {
        std::cout << "=== RAII Buffer Examples ===" << std::endl;
        
        std::cout << "\n1. Basic SecureBuffer usage:" << std::endl;
        example_basic_secure_buffer();
        
        std::cout << "\n2. Safe packet handling:" << std::endl;
        example_safe_packet_handling();
        
        std::cout << "\n3. Replacing manual memory management:" << std::endl;
        example_replace_manual_memory();
        
        std::cout << "\n4. Thread-safe operations:" << std::endl;
        example_thread_safe_operations();
        
        std::cout << "\n5. MemoryGuard usage:" << std::endl;
        example_memory_guard_usage();
        
        std::cout << "\n6. ResourceGuard usage:" << std::endl;
        example_resource_guard();
        
        std::cout << "\n7. ScopeGuard usage:" << std::endl;
        example_scope_guard();
        
        std::cout << "\n8. Migration from old Buffer:" << std::endl;
        example_migration_from_old_buffer();
        
        std::cout << "\n9. Exception safety:" << std::endl;
        example_exception_safety();
        
        std::cout << "\n10. Move semantics performance:" << std::endl;
        example_move_semantics();
        
        std::cout << "\n=== All examples completed safely! ===" << std::endl;
    }

} // namespace BufferExamples

#endif // __BUFFER_EXAMPLES_H
