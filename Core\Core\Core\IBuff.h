/**
 * @file IBuff.h
 * @brief Buff/Debuff interface for game server buff management
 *
 * This file defines the IBuff interface which provides access to buff and debuff
 * objects in the game server, allowing manipulation of temporary character effects.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

/**
 * @brief Interface for buff/debuff objects in the game server
 *
 * The IBuff interface provides access to temporary character effects including
 * buffs, debuffs, and other timed effects. It allows querying buff properties,
 * managing buff duration, and modifying buff values.
 */
class IBuff
{
public:
	void* Offset;  ///< Pointer to the buff object in game memory

public:
	/**
	 * @brief Default constructor
	 * Creates an empty buff interface with null offset.
	 */
	IBuff()
	{
	}

	/**
	 * @brief Constructor with buff object pointer
	 * @param Object Pointer to the buff object in game memory
	 */
	IBuff(void* Object);

	/**
	 * @brief Virtual destructor
	 */
	virtual ~IBuff();

	void *GetOffset();           ///< Get the memory offset of the buff object
	int GetValue();              ///< Get the buff's effect value/strength
	int GetBuffID();             ///< Get the unique identifier of the buff
	int GetTime();               ///< Get remaining time for the buff in seconds
	bool Exists();               ///< Check if the buff object is valid and exists
	int GetType();               ///< Get the type/category of the buff
	void DeleteThis();           ///< Remove/delete this buff from the character
	void updateValue(int Value); ///< Update the buff's effect value
};