/**
 * @file MemoryRAII_Examples.h
 * @brief Examples showing how RAII improvements in Memory.cpp prevent leaks and crashes
 *
 * This file demonstrates the improvements made to Memory.cpp and how they
 * prevent memory leaks, crashes, and other issues that were present in the
 * original implementation.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __MEMORY_RAII_EXAMPLES_H
#define __MEMORY_RAII_EXAMPLES_H

#include "Memory.h"
#include "Interface.h"
#include <iostream>
#include <stdexcept>

/**
 * @brief Examples demonstrating RAII improvements in Memory.cpp
 */
namespace MemoryRAII_Examples {

    /**
     * @brief Example 1: Safe memory patching with automatic cleanup
     * 
     * Shows how the improved Fill method prevents memory leaks
     */
    void example_safe_memory_fill() {
        std::cout << "=== Safe Memory Fill Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            // Allocate some test memory
            char test_memory[1024] = {0};
            
            // OLD WAY (prone to leaks):
            /*
            unsigned char *Data = new unsigned char[Size];  // Manual allocation
            FillMemory(Data, Size, Fill);
            // If exception occurs here, Data leaks!
            this->m_Patches[Destination] = new Patch(Destination, Data, Size, Recoverable);
            delete[] Data;  // Might never execute if exception occurs
            */
            
            // NEW WAY (RAII-safe):
            Memory->Fill(test_memory, 0xAA, sizeof(test_memory), true);
            
            std::cout << "✓ Memory fill completed safely with automatic cleanup" << std::endl;
            std::cout << "✓ No memory leaks even if exceptions occur" << std::endl;
            
            // Cleanup
            Memory->Restore(test_memory);
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
            std::cout << "✓ All memory automatically cleaned up" << std::endl;
        }
    }

    /**
     * @brief Example 2: Safe hook creation with bounds checking
     * 
     * Shows how the improved Hook method prevents buffer overflows
     */
    void example_safe_hook_creation() {
        std::cout << "\n=== Safe Hook Creation Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            // Simulate some code to hook
            char dummy_function[32] = {0x90, 0x90, 0x90, 0x90, 0x90}; // NOPs
            char hook_target[32] = {0xCC, 0xCC, 0xCC, 0xCC, 0xCC};    // INT3s
            
            // OLD WAY (prone to leaks and overflows):
            /*
            unsigned char *Data = new unsigned char[Size];  // Manual allocation
            FillMemory(Data, Size, IMemory::_I_NOP);
            // No bounds checking - could overflow!
            Data[0] = Instruction;
            CopyMemory(Data+1, &Target, 4);  // Potential overflow
            // If exception occurs, Data leaks!
            delete[] Data;
            */
            
            // NEW WAY (RAII-safe with bounds checking):
            Memory->Hook(dummy_function, hook_target, IMemory::_I_CALL, 5, true);
            
            std::cout << "✓ Hook created safely with bounds checking" << std::endl;
            std::cout << "✓ Automatic memory management prevents leaks" << std::endl;
            
            // Test invalid parameters (should throw instead of crashing)
            try {
                Memory->Hook(nullptr, hook_target, IMemory::_I_CALL, 5, true);
            } catch (const std::invalid_argument& e) {
                std::cout << "✓ Invalid parameters caught safely: " << e.what() << std::endl;
            }
            
            // Cleanup
            Memory->Restore(dummy_function);
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 3: Exception-safe memory protection
     * 
     * Shows how the improved Copy method ensures protection is always restored
     */
    void example_exception_safe_copy() {
        std::cout << "\n=== Exception-Safe Memory Copy Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            char source[256] = "Hello, RAII World!";
            char destination[256] = {0};
            
            // OLD WAY (protection might not be restored):
            /*
            VirtualProtect(Destination, Size, PAGE_EXECUTE_READWRITE, &p1);
            VirtualProtect(Source, Size, PAGE_EXECUTE_READWRITE, &p2);
            CopyMemory(Destination, Source, Size);
            // If exception occurs here, protection is never restored!
            VirtualProtect(Destination, Size, p1, &p1);
            VirtualProtect(Source, Size, p1, &p2);
            */
            
            // NEW WAY (RAII ensures protection is always restored):
            Memory->Copy(destination, source, strlen(source) + 1);
            
            std::cout << "✓ Memory copied safely: " << destination << std::endl;
            std::cout << "✓ Memory protection automatically restored" << std::endl;
            
            // Test with invalid parameters
            try {
                Memory->Copy(nullptr, source, 100);
            } catch (const std::invalid_argument& e) {
                std::cout << "✓ Invalid copy parameters caught: " << e.what() << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 4: Safe patch restoration with error handling
     * 
     * Shows how the improved Restore method handles errors gracefully
     */
    void example_safe_patch_restoration() {
        std::cout << "\n=== Safe Patch Restoration Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            char test_code[64] = {0x90, 0x90, 0x90, 0x90, 0x90}; // NOPs
            
            // Create a patch
            Memory->Fill(test_code, 0xCC, 5, true); // Fill with INT3
            
            std::cout << "✓ Patch created successfully" << std::endl;
            
            // OLD WAY (unsafe restoration):
            /*
            if (this->m_Patches.count(Address)) {
                delete this->m_Patches[Address];  // Could throw and leave map inconsistent
                this->m_Patches.erase(Address);   // Might never execute
            }
            */
            
            // NEW WAY (RAII-safe restoration):
            Memory->Restore(test_code);
            
            std::cout << "✓ Patch restored safely with automatic cleanup" << std::endl;
            
            // Test restoring non-existent patch (should handle gracefully)
            Memory->Restore(test_code); // Second restore should be safe
            std::cout << "✓ Redundant restore handled gracefully" << std::endl;
            
            // Test with null pointer (should handle gracefully)
            Memory->Restore(static_cast<void*>(nullptr));
            std::cout << "✓ Null pointer restore handled gracefully" << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 5: Safe API hooking with transaction management
     * 
     * Shows how the improved HookAPI method ensures transactions are properly handled
     */
    void example_safe_api_hooking() {
        std::cout << "\n=== Safe API Hooking Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            // Note: This is a demonstration - in real usage you'd hook actual APIs
            void* dummy_api = reinterpret_cast<void*>(0x12345678);
            void* hook_function = reinterpret_cast<void*>(0x87654321);
            
            // OLD WAY (transaction might not be properly cleaned up):
            /*
            DetourTransactionBegin();
            DetourUpdateThread(GetCurrentThread());
            DetourAttach((void**)&Address, (void*)Destination);
            // If this fails, transaction is never committed or aborted!
            DetourTransactionCommit();
            */
            
            // NEW WAY (RAII ensures transaction is properly handled):
            try {
                Memory->HookAPI(dummy_api, hook_function);
                std::cout << "✓ API hook would be created safely" << std::endl;
            } catch (const std::runtime_error& e) {
                std::cout << "✓ Hook failure handled safely: " << e.what() << std::endl;
                std::cout << "✓ Transaction automatically cleaned up" << std::endl;
            }
            
            // Test with invalid parameters
            try {
                Memory->HookAPI(nullptr, hook_function);
            } catch (const std::invalid_argument& e) {
                std::cout << "✓ Invalid hook parameters caught: " << e.what() << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Example 6: Demonstrating the improved Patch class
     * 
     * Shows how the new Patch class uses RAII for automatic cleanup
     */
    void example_improved_patch_class() {
        std::cout << "\n=== Improved Patch Class Example ===" << std::endl;
        
        try {
            Interface<IMemory> Memory;
            
            char original_code[16] = {0x90, 0x90, 0x90, 0x90, 0x90}; // NOPs
            char backup[16];
            memcpy(backup, original_code, sizeof(backup));
            
            // Create a patch that will automatically restore on destruction
            Memory->Fill(original_code, 0xCC, 5, true);
            
            std::cout << "✓ Patch applied - original code backed up safely" << std::endl;
            
            // OLD Patch class problems:
            /*
            - Manual new/delete for m_Original (could leak)
            - No exception safety in constructor
            - Destructor could fail silently
            */
            
            // NEW Patch class benefits:
            /*
            - Uses SecureBuffer for automatic memory management
            - Exception-safe constructor
            - Guaranteed cleanup in destructor
            - No manual memory management needed
            */
            
            // When we restore, the Patch destructor automatically restores original data
            Memory->Restore(original_code);
            
            std::cout << "✓ Original code automatically restored by RAII" << std::endl;
            std::cout << "✓ No memory leaks even if exceptions occur" << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "✓ Exception handled safely: " << e.what() << std::endl;
        }
    }

    /**
     * @brief Run all Memory RAII examples
     */
    void run_all_memory_examples() {
        std::cout << "=== Memory.cpp RAII Improvements Examples ===" << std::endl;
        std::cout << "Demonstrating how RAII prevents memory leaks and crashes\n" << std::endl;
        
        example_safe_memory_fill();
        example_safe_hook_creation();
        example_exception_safe_copy();
        example_safe_patch_restoration();
        example_safe_api_hooking();
        example_improved_patch_class();
        
        std::cout << "\n=== Summary of RAII Improvements ===" << std::endl;
        std::cout << "✓ Automatic memory cleanup prevents leaks" << std::endl;
        std::cout << "✓ Exception safety ensures consistent state" << std::endl;
        std::cout << "✓ Bounds checking prevents buffer overflows" << std::endl;
        std::cout << "✓ Input validation prevents crashes" << std::endl;
        std::cout << "✓ RAII guards ensure proper resource cleanup" << std::endl;
        std::cout << "✓ Smart pointers eliminate manual memory management" << std::endl;
        std::cout << "\nYour Memory.cpp is now much safer and more robust!" << std::endl;
    }

} // namespace MemoryRAII_Examples

#endif // __MEMORY_RAII_EXAMPLES_H
