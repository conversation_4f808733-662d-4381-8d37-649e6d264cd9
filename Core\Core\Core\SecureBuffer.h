/**
 * @file SecureBuffer.h
 * @brief RAII-compliant secure buffer class with automatic cleanup and bounds checking
 *
 * This file provides a secure, RAII-compliant buffer implementation that prevents
 * memory leaks, buffer overflows, and provides exception safety. Designed to replace
 * unsafe manual memory management patterns throughout the codebase.
 *
 * Key Features:
 * - Automatic memory cleanup using RAII principles
 * - Bounds checking for all operations
 * - Exception safety guarantees
 * - Secure memory wiping on destruction
 * - Move semantics for performance
 * - Thread safety integration with existing Lock system
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __SECURE_BUFFER_H
#define __SECURE_BUFFER_H

#include <Windows.h>
#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cstring>
#include <sstream>
#include <climits>
#include "Lock.h"
#include "MutexMap.h"

// C++14 compatibility defines
#ifndef SIZE_MAX
#define SIZE_MAX ((size_t)-1)
#endif

// Helper functions for C++14 compatibility
namespace SecureBufferUtils {
    template<typename T>
    std::string to_string(T value) {
        std::ostringstream oss;
        oss << value;
        return oss.str();
    }

    // Compatibility make_unique for C++14 (in case it's not available)
    #if __cplusplus < 201402L || (defined(_MSC_VER) && _MSC_VER < 1900)
    template<typename T, typename... Args>
    std::unique_ptr<T> make_unique(Args&&... args) {
        return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
    }
    #else
    using std::make_unique;
    #endif
}

/**
 * @brief Exception thrown when buffer operations exceed bounds
 */
class BufferOverflowException : public std::runtime_error {
public:
    explicit BufferOverflowException(const std::string& message)
        : std::runtime_error("Buffer overflow: " + message) {}
};

/**
 * @brief Exception thrown when buffer operations are invalid
 */
class BufferInvalidOperationException : public std::runtime_error {
public:
    explicit BufferInvalidOperationException(const std::string& message)
        : std::runtime_error("Invalid buffer operation: " + message) {}
};

/**
 * @brief RAII-compliant secure buffer class with automatic cleanup
 *
 * This class provides secure buffer management with the following guarantees:
 * - Automatic memory cleanup on destruction (RAII)
 * - Bounds checking for all read/write operations
 * - Exception safety (strong exception guarantee)
 * - Secure memory wiping to prevent data leaks
 * - Thread safety when used with locks
 * - Move semantics for efficient transfers
 */
template<typename T = unsigned char>
class SecureBuffer {
private:
    std::unique_ptr<T[]> m_buffer;      ///< Smart pointer managing the buffer memory
    size_t m_size;                      ///< Size of the buffer in elements
    size_t m_capacity;                  ///< Allocated capacity in elements
    mutable Lock m_lock;                ///< Thread safety lock
    bool m_secure_wipe;                 ///< Whether to securely wipe memory on destruction

    /**
     * @brief Securely wipe memory contents
     * @param ptr Pointer to memory to wipe
     * @param size Size in bytes to wipe
     */
    void secure_wipe_memory(void* ptr, size_t size) noexcept {
        if (ptr && size > 0) {
            // Use SecureZeroMemory on Windows for secure wiping
            SecureZeroMemory(ptr, size);
        }
    }

    /**
     * @brief Validate buffer bounds for operations
     * @param offset Starting offset for operation
     * @param length Length of operation
     * @throws BufferOverflowException if bounds are exceeded
     */
    void validate_bounds(size_t offset, size_t length = 1) const {
        if (offset >= m_size || offset + length > m_size) {
            throw BufferOverflowException(
                "Access at offset " + SecureBufferUtils::to_string(offset) +
                " with length " + SecureBufferUtils::to_string(length) +
                " exceeds buffer size " + SecureBufferUtils::to_string(m_size)
            );
        }
    }

    /**
     * @brief Validate that buffer is initialized
     * @throws BufferInvalidOperationException if buffer is null
     */
    void validate_initialized() const {
        if (!m_buffer) {
            throw BufferInvalidOperationException("Buffer not initialized");
        }
    }

public:
    /**
     * @brief Default constructor - creates empty buffer
     */
    SecureBuffer() noexcept
        : m_buffer(nullptr), m_size(0), m_capacity(0), m_secure_wipe(true) {}

    /**
     * @brief Constructor with size
     * @param size Number of elements to allocate
     * @param secure_wipe Whether to securely wipe memory on destruction
     * @throws std::bad_alloc if allocation fails
     */
    explicit SecureBuffer(size_t size, bool secure_wipe = true)
        : m_size(size), m_capacity(size), m_secure_wipe(secure_wipe) {
        if (size > 0) {
            m_buffer.reset(new T[size]);
            // Initialize to zero for security
            std::memset(m_buffer.get(), 0, size * sizeof(T));
        }
    }

    /**
     * @brief Constructor with size and initial value
     * @param size Number of elements to allocate
     * @param initial_value Value to initialize all elements with
     * @param secure_wipe Whether to securely wipe memory on destruction
     * @throws std::bad_alloc if allocation fails
     */
    SecureBuffer(size_t size, const T& initial_value, bool secure_wipe = true)
        : m_size(size), m_capacity(size), m_secure_wipe(secure_wipe) {
        if (size > 0) {
            m_buffer.reset(new T[size]);
            std::fill_n(m_buffer.get(), size, initial_value);
        }
    }

    /**
     * @brief Copy constructor
     * @param other Buffer to copy from
     */
    SecureBuffer(const SecureBuffer& other)
        : m_size(other.m_size), m_capacity(other.m_capacity), m_secure_wipe(other.m_secure_wipe) {
        MutexMap lock(other.m_lock);
        if (other.m_buffer && m_size > 0) {
            m_buffer.reset(new T[m_capacity]);
            std::memcpy(m_buffer.get(), other.m_buffer.get(), m_size * sizeof(T));
        }
    }

    /**
     * @brief Move constructor
     * @param other Buffer to move from
     */
    SecureBuffer(SecureBuffer&& other) noexcept
        : m_buffer(std::move(other.m_buffer)),
          m_size(other.m_size),
          m_capacity(other.m_capacity),
          m_secure_wipe(other.m_secure_wipe) {
        other.m_size = 0;
        other.m_capacity = 0;
    }

    /**
     * @brief Destructor - automatically cleans up memory
     */
    ~SecureBuffer() noexcept {
        if (m_buffer && m_secure_wipe) {
            secure_wipe_memory(m_buffer.get(), m_capacity * sizeof(T));
        }
        // unique_ptr automatically deallocates memory
    }

    /**
     * @brief Copy assignment operator
     * @param other Buffer to copy from
     * @return Reference to this buffer
     */
    SecureBuffer& operator=(const SecureBuffer& other) {
        if (this != &other) {
            MutexMap lock1(m_lock);
            MutexMap lock2(other.m_lock);

            // Securely wipe old data if needed
            if (m_buffer && m_secure_wipe) {
                secure_wipe_memory(m_buffer.get(), m_capacity * sizeof(T));
            }

            m_size = other.m_size;
            m_capacity = other.m_capacity;
            m_secure_wipe = other.m_secure_wipe;

            if (other.m_buffer && m_size > 0) {
                m_buffer.reset(new T[m_capacity]);
                std::memcpy(m_buffer.get(), other.m_buffer.get(), m_size * sizeof(T));
            } else {
                m_buffer.reset();
            }
        }
        return *this;
    }

    /**
     * @brief Move assignment operator
     * @param other Buffer to move from
     * @return Reference to this buffer
     */
    SecureBuffer& operator=(SecureBuffer&& other) noexcept {
        if (this != &other) {
            MutexMap lock(m_lock);

            // Securely wipe old data if needed
            if (m_buffer && m_secure_wipe) {
                secure_wipe_memory(m_buffer.get(), m_capacity * sizeof(T));
            }

            m_buffer = std::move(other.m_buffer);
            m_size = other.m_size;
            m_capacity = other.m_capacity;
            m_secure_wipe = other.m_secure_wipe;

            other.m_size = 0;
            other.m_capacity = 0;
        }
        return *this;
    }

    // ============================================================================
    // ACCESS METHODS WITH BOUNDS CHECKING
    // ============================================================================

    /**
     * @brief Safe array access operator with bounds checking
     * @param index Index to access
     * @return Reference to element at index
     * @throws BufferOverflowException if index is out of bounds
     */
    T& operator[](size_t index) {
        MutexMap lock(m_lock);
        validate_initialized();
        validate_bounds(index);
        return m_buffer[index];
    }

    /**
     * @brief Safe const array access operator with bounds checking
     * @param index Index to access
     * @return Const reference to element at index
     * @throws BufferOverflowException if index is out of bounds
     */
    const T& operator[](size_t index) const {
        MutexMap lock(m_lock);
        validate_initialized();
        validate_bounds(index);
        return m_buffer[index];
    }

    /**
     * @brief Safe element access with bounds checking
     * @param index Index to access
     * @return Reference to element at index
     * @throws BufferOverflowException if index is out of bounds
     */
    T& at(size_t index) {
        return operator[](index);
    }

    /**
     * @brief Safe const element access with bounds checking
     * @param index Index to access
     * @return Const reference to element at index
     * @throws BufferOverflowException if index is out of bounds
     */
    const T& at(size_t index) const {
        return operator[](index);
    }

    /**
     * @brief Get raw pointer to buffer data (use with caution)
     * @return Raw pointer to buffer data or nullptr if empty
     */
    T* data() noexcept {
        MutexMap lock(m_lock);
        return m_buffer.get();
    }

    /**
     * @brief Get const raw pointer to buffer data
     * @return Const raw pointer to buffer data or nullptr if empty
     */
    const T* data() const noexcept {
        MutexMap lock(m_lock);
        return m_buffer.get();
    }

    /**
     * @brief Get raw pointer for compatibility with C APIs (use with extreme caution)
     * @return Raw pointer to buffer data
     * @warning This bypasses RAII protection - use only when absolutely necessary
     */
    T* unsafe_raw_ptr() noexcept {
        return m_buffer.get();
    }

    // ============================================================================
    // SIZE AND CAPACITY METHODS
    // ============================================================================

    /**
     * @brief Get current size of buffer
     * @return Number of elements in buffer
     */
    size_t size() const noexcept {
        MutexMap lock(m_lock);
        return m_size;
    }

    /**
     * @brief Get current capacity of buffer
     * @return Number of elements that can be stored without reallocation
     */
    size_t capacity() const noexcept {
        MutexMap lock(m_lock);
        return m_capacity;
    }

    /**
     * @brief Check if buffer is empty
     * @return True if buffer is empty, false otherwise
     */
    bool empty() const noexcept {
        MutexMap lock(m_lock);
        return m_size == 0;
    }

    /**
     * @brief Get size in bytes
     * @return Size of buffer in bytes
     */
    size_t size_bytes() const noexcept {
        MutexMap lock(m_lock);
        return m_size * sizeof(T);
    }

    // ============================================================================
    // BUFFER MANIPULATION METHODS
    // ============================================================================

    /**
     * @brief Resize buffer to new size
     * @param new_size New size in elements
     * @param preserve_data Whether to preserve existing data
     * @throws std::bad_alloc if allocation fails
     */
    void resize(size_t new_size, bool preserve_data = true) {
        MutexMap lock(m_lock);

        if (new_size == m_size) {
            return; // No change needed
        }

        if (new_size == 0) {
            clear();
            return;
        }

        std::unique_ptr<T[]> new_buffer(new T[new_size]);

        if (preserve_data && m_buffer && m_size > 0) {
            size_t copy_size = std::min(m_size, new_size);
            std::memcpy(new_buffer.get(), m_buffer.get(), copy_size * sizeof(T));
        }

        // Initialize new elements to zero
        if (new_size > m_size) {
            std::memset(new_buffer.get() + m_size, 0, (new_size - m_size) * sizeof(T));
        }

        // Securely wipe old buffer if needed
        if (m_buffer && m_secure_wipe) {
            secure_wipe_memory(m_buffer.get(), m_capacity * sizeof(T));
        }

        m_buffer = std::move(new_buffer);
        m_size = new_size;
        m_capacity = new_size;
    }

    /**
     * @brief Clear buffer contents and free memory
     */
    void clear() noexcept {
        MutexMap lock(m_lock);

        if (m_buffer && m_secure_wipe) {
            secure_wipe_memory(m_buffer.get(), m_capacity * sizeof(T));
        }

        m_buffer.reset();
        m_size = 0;
        m_capacity = 0;
    }

    /**
     * @brief Fill buffer with specified value
     * @param value Value to fill buffer with
     */
    void fill(const T& value) {
        MutexMap lock(m_lock);
        validate_initialized();
        std::fill_n(m_buffer.get(), m_size, value);
    }

    /**
     * @brief Zero out buffer contents
     */
    void zero() {
        MutexMap lock(m_lock);
        validate_initialized();
        std::memset(m_buffer.get(), 0, m_size * sizeof(T));
    }

    // ============================================================================
    // SAFE COPY AND WRITE METHODS
    // ============================================================================

    /**
     * @brief Safely copy data to buffer with bounds checking
     * @param source Source data to copy from
     * @param source_size Size of source data in elements
     * @param dest_offset Offset in destination buffer
     * @throws BufferOverflowException if operation would exceed bounds
     */
    void safe_copy_from(const T* source, size_t source_size, size_t dest_offset = 0) {
        if (!source) {
            throw BufferInvalidOperationException("Source pointer is null");
        }

        MutexMap lock(m_lock);
        validate_initialized();
        validate_bounds(dest_offset, source_size);

        std::memcpy(m_buffer.get() + dest_offset, source, source_size * sizeof(T));
    }

    /**
     * @brief Safely copy data from buffer with bounds checking
     * @param dest Destination buffer to copy to
     * @param dest_size Size of destination buffer in elements
     * @param source_offset Offset in source buffer
     * @param copy_size Number of elements to copy
     * @throws BufferOverflowException if operation would exceed bounds
     */
    void safe_copy_to(T* dest, size_t dest_size, size_t source_offset = 0, size_t copy_size = SIZE_MAX) const {
        if (!dest) {
            throw BufferInvalidOperationException("Destination pointer is null");
        }

        MutexMap lock(m_lock);
        validate_initialized();

        if (copy_size == SIZE_MAX) {
            copy_size = m_size - source_offset;
        }

        validate_bounds(source_offset, copy_size);

        if (copy_size > dest_size) {
            throw BufferOverflowException("Copy size exceeds destination buffer size");
        }

        std::memcpy(dest, m_buffer.get() + source_offset, copy_size * sizeof(T));
    }

    /**
     * @brief Write data at specific offset with bounds checking
     * @param data Data to write
     * @param offset Offset to write at
     * @throws BufferOverflowException if offset is out of bounds
     */
    void write_at(const T& data, size_t offset) {
        MutexMap lock(m_lock);
        validate_initialized();
        validate_bounds(offset);
        m_buffer[offset] = data;
    }

    /**
     * @brief Read data at specific offset with bounds checking
     * @param offset Offset to read from
     * @return Copy of data at offset
     * @throws BufferOverflowException if offset is out of bounds
     */
    T read_at(size_t offset) const {
        MutexMap lock(m_lock);
        validate_initialized();
        validate_bounds(offset);
        return m_buffer[offset];
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * @brief Check if buffer is valid (initialized and non-empty)
     * @return True if buffer is valid, false otherwise
     */
    bool is_valid() const noexcept {
        MutexMap lock(m_lock);
        return m_buffer != nullptr && m_size > 0;
    }

    /**
     * @brief Get lock for manual synchronization (advanced usage)
     * @return Reference to internal lock
     * @warning Use with caution - prefer automatic locking in methods
     */
    Lock& get_lock() const noexcept {
        return m_lock;
    }

    /**
     * @brief Enable or disable secure wiping
     * @param enable Whether to enable secure wiping on destruction
     */
    void set_secure_wipe(bool enable) noexcept {
        MutexMap lock(m_lock);
        m_secure_wipe = enable;
    }

    /**
     * @brief Check if secure wiping is enabled
     * @return True if secure wiping is enabled, false otherwise
     */
    bool is_secure_wipe_enabled() const noexcept {
        MutexMap lock(m_lock);
        return m_secure_wipe;
    }

    // ============================================================================
    // COMPARISON OPERATORS
    // ============================================================================

    /**
     * @brief Equality comparison operator
     * @param other Buffer to compare with
     * @return True if buffers are equal, false otherwise
     */
    bool operator==(const SecureBuffer& other) const {
        if (this == &other) return true;

        MutexMap lock1(m_lock);
        MutexMap lock2(other.m_lock);

        if (m_size != other.m_size) return false;
        if (!m_buffer && !other.m_buffer) return true;
        if (!m_buffer || !other.m_buffer) return false;

        return std::memcmp(m_buffer.get(), other.m_buffer.get(), m_size * sizeof(T)) == 0;
    }

    /**
     * @brief Inequality comparison operator
     * @param other Buffer to compare with
     * @return True if buffers are not equal, false otherwise
     */
    bool operator!=(const SecureBuffer& other) const {
        return !operator==(other);
    }
};

// ============================================================================
// TYPE ALIASES FOR COMMON USAGE
// ============================================================================

using SecureByteBuffer = SecureBuffer<unsigned char>;
using SecureCharBuffer = SecureBuffer<char>;
using SecureIntBuffer = SecureBuffer<int>;
using SecurePacketBuffer = SecureByteBuffer;  // For network packets

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * @brief Create a secure buffer from existing data
 * @param data Source data to copy
 * @param size Size of data in elements
 * @return SecureBuffer containing copy of the data
 */
template<typename T>
SecureBuffer<T> make_secure_buffer(const T* data, size_t size) {
    SecureBuffer<T> buffer(size);
    if (data && size > 0) {
        buffer.safe_copy_from(data, size);
    }
    return buffer;
}

/**
 * @brief Create a secure buffer with specific size and initial value
 * @param size Size of buffer in elements
 * @param initial_value Value to initialize all elements with
 * @return SecureBuffer initialized with the value
 */
template<typename T>
SecureBuffer<T> make_secure_buffer(size_t size, const T& initial_value) {
    return SecureBuffer<T>(size, initial_value);
}

#endif // __SECURE_BUFFER_H
