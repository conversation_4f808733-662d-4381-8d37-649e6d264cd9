/**
 * @file BufferTest.cpp
 * @brief Test file to verify RAII buffer implementation
 *
 * This file contains tests to verify that the RAII buffer classes
 * work correctly and prevent memory leaks and crashes.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include "Buffer.h"
#include "BufferExamples.h"
#include <iostream>
#include <cassert>

/**
 * @brief Test basic SecureBuffer functionality
 */
void test_secure_buffer_basic() {
    std::cout << "Testing SecureBuffer basic functionality..." << std::endl;
    
    try {
        // Test construction and basic operations
        SecureByteBuffer buffer(1024);
        assert(buffer.size() == 1024);
        assert(buffer.is_valid());
        assert(!buffer.empty());
        
        // Test safe access
        buffer[0] = 0xFF;
        buffer[1023] = 0xAA;
        assert(buffer[0] == 0xFF);
        assert(buffer[1023] == 0xAA);
        
        // Test bounds checking
        bool exception_caught = false;
        try {
            buffer[1024] = 0x00; // Should throw
        } catch (const BufferOverflowException&) {
            exception_caught = true;
        }
        assert(exception_caught);
        
        std::cout << "✓ SecureBuffer basic functionality test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ SecureBuffer basic test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test SecureBuffer copy operations
 */
void test_secure_buffer_copy() {
    std::cout << "Testing SecureBuffer copy operations..." << std::endl;
    
    try {
        SecureByteBuffer buffer1(100);
        buffer1.fill(0xAA);
        
        // Test copy constructor
        SecureByteBuffer buffer2(buffer1);
        assert(buffer2.size() == 100);
        assert(buffer2[0] == 0xAA);
        assert(buffer2[99] == 0xAA);
        
        // Test assignment
        SecureByteBuffer buffer3(50);
        buffer3 = buffer1;
        assert(buffer3.size() == 100);
        assert(buffer3[0] == 0xAA);
        
        // Test move semantics
        SecureByteBuffer buffer4 = std::move(buffer3);
        assert(buffer4.size() == 100);
        assert(buffer4[0] == 0xAA);
        assert(buffer3.empty()); // Moved from
        
        std::cout << "✓ SecureBuffer copy operations test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ SecureBuffer copy test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test safe copy operations
 */
void test_safe_copy_operations() {
    std::cout << "Testing safe copy operations..." << std::endl;
    
    try {
        SecureByteBuffer buffer(100);
        
        // Test safe copy from
        const unsigned char data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
        buffer.safe_copy_from(data, sizeof(data));
        
        assert(buffer[0] == 0x01);
        assert(buffer[4] == 0x05);
        
        // Test safe copy to
        unsigned char output[5] = {0};
        buffer.safe_copy_to(output, sizeof(output), 0, 5);
        
        assert(output[0] == 0x01);
        assert(output[4] == 0x05);
        
        // Test bounds checking in copy operations
        bool exception_caught = false;
        try {
            buffer.safe_copy_from(data, 200); // Should throw - too large
        } catch (const BufferOverflowException&) {
            exception_caught = true;
        }
        assert(exception_caught);
        
        std::cout << "✓ Safe copy operations test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Safe copy operations test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test improved Buffer class
 */
void test_improved_buffer() {
    std::cout << "Testing improved Buffer class..." << std::endl;
    
    try {
        // Test basic functionality
        Buffer buffer(1024);
        assert(buffer.getSize() == 1024);
        assert(buffer.isValid());
        
        // Test legacy interface
        char* packet = buffer.getPacket();
        assert(packet != nullptr);
        
        // Test new safe methods
        buffer.at(0) = 'H';
        buffer.at(1) = 'i';
        assert(buffer.at(0) == 'H');
        assert(buffer.at(1) == 'i');
        
        // Test bounds checking
        bool exception_caught = false;
        try {
            buffer.at(1024) = 'X'; // Should throw
        } catch (const std::out_of_range&) {
            exception_caught = true;
        }
        assert(exception_caught);
        
        // Test zero operation
        buffer.zero();
        assert(buffer.at(0) == 0);
        assert(buffer.at(1) == 0);
        
        std::cout << "✓ Improved Buffer class test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Improved Buffer test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test MemoryGuard classes
 */
void test_memory_guard() {
    std::cout << "Testing MemoryGuard classes..." << std::endl;
    
    try {
        // Test MallocGuard
        {
            MallocGuard memory(1024);
            assert(memory.is_valid());
            assert(memory.size() == 1024);
            
            char* ptr = memory.get_as<char>();
            assert(ptr != nullptr);
            
            strcpy_s(ptr, 1024, "Test string");
            assert(strcmp(ptr, "Test string") == 0);
            
            // Memory automatically freed when guard goes out of scope
        }
        
        // Test ResourceGuard
        {
            bool cleanup_called = false;
            {
                ResourceGuard<int> guard(42, [&cleanup_called](int) {
                    cleanup_called = true;
                });
                
                assert(guard.get() == 42);
                assert(guard.is_valid());
            } // Guard destructor called here
            
            assert(cleanup_called);
        }
        
        // Test ScopeGuard
        {
            bool cleanup_called = false;
            {
                ScopeGuard guard([&cleanup_called]() {
                    cleanup_called = true;
                });
                
                // Do some work...
            } // Guard destructor called here
            
            assert(cleanup_called);
        }
        
        std::cout << "✓ MemoryGuard classes test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ MemoryGuard test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test exception safety
 */
void test_exception_safety() {
    std::cout << "Testing exception safety..." << std::endl;
    
    try {
        // Test that resources are cleaned up even when exceptions occur
        bool cleanup_occurred = false;
        
        try {
            SecureByteBuffer buffer(1024);
            
            // Simulate an operation that might throw
            throw std::runtime_error("Simulated error");
            
        } catch (const std::runtime_error&) {
            // Exception caught - buffer should be automatically cleaned up
            cleanup_occurred = true;
        }
        
        assert(cleanup_occurred);
        
        std::cout << "✓ Exception safety test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Exception safety test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Test utility functions
 */
void test_utility_functions() {
    std::cout << "Testing utility functions..." << std::endl;
    
    try {
        // Test make_secure_buffer
        const char data[] = "Hello, World!";
        auto buffer = make_secure_buffer(data, strlen(data) + 1);
        
        assert(buffer.size() == strlen(data) + 1);
        assert(buffer[0] == 'H');
        assert(buffer[12] == '!');
        
        // Test make_secure_buffer with initial value
        auto buffer2 = make_secure_buffer<int>(10, 42);
        assert(buffer2.size() == 10);
        assert(buffer2[0] == 42);
        assert(buffer2[9] == 42);
        
        std::cout << "✓ Utility functions test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Utility functions test failed: " << e.what() << std::endl;
    }
}

/**
 * @brief Run all buffer tests
 */
void run_all_buffer_tests() {
    std::cout << "=== RAII Buffer Tests ===" << std::endl;
    
    test_secure_buffer_basic();
    test_secure_buffer_copy();
    test_safe_copy_operations();
    test_improved_buffer();
    test_memory_guard();
    test_exception_safety();
    test_utility_functions();
    
    std::cout << "\n=== Running Examples ===" << std::endl;
    BufferExamples::run_all_examples();
    
    std::cout << "\n=== All Tests Completed ===" << std::endl;
    std::cout << "If you see this message, all RAII buffer classes are working correctly!" << std::endl;
    std::cout << "Memory leaks and crashes should be significantly reduced." << std::endl;
}

/**
 * @brief Main test function (can be called from your existing code)
 */
extern "C" __declspec(dllexport) void test_raii_buffers() {
    run_all_buffer_tests();
}

// Uncomment the following to run tests automatically when DLL loads
/*
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Run tests when DLL loads
        run_all_buffer_tests();
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}
*/
