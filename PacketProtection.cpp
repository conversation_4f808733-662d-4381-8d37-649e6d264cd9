#include "PacketProtection.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>

// Static member definitions
std::map<int, uint32_t> PacketProtection::socketKeys;
std::map<int, uint64_t> PacketProtection::socketTokens;
std::map<int, uint32_t> PacketProtection::socketSequence;
std::map<int, uint64_t> PacketProtection::socketTimestamp;

std::map<int, std::vector<uint64_t>> AntiCheat::packetTimings;
std::map<int, std::map<unsigned char, uint32_t>> AntiCheat::packetCounts;
std::map<int, std::vector<unsigned char>> AntiCheat::packetHistory;

ProtectionConfig g_protectionConfig;

// CRC32 lookup table for fast checksum calculation
static const uint32_t crc32_table[256] = {
    0x00000000, 0x77073096, 0xee0e612c, 0x990951ba, 0x076dc419, 0x706af48f,
    0xe963a535, 0x9e6495a3, 0x0edb8832, 0x79dcb8a4, 0xe0d5e91e, 0x97d2d988,
    0x09b64c2b, 0x7eb17cbd, 0xe7b82d07, 0x90bf1d91, 0x1db71064, 0x6ab020f2,
    0xf3b97148, 0x84be41de, 0x1adad47d, 0x6ddde4eb, 0xf4d4b551, 0x83d385c7,
    0x136c9856, 0x646ba8c0, 0xfd62f97a, 0x8a65c9ec, 0x14015c4f, 0x63066cd9,
    0xfa0f3d63, 0x8d080df5, 0x3b6e20c8, 0x4c69105e, 0xd56041e4, 0xa2677172,
    0x3c03e4d1, 0x4b04d447, 0xd20d85fd, 0xa50ab56b, 0x35b5a8fa, 0x42b2986c,
    0xdbbbc9d6, 0xacbcf940, 0x32d86ce3, 0x45df5c75, 0xdcd60dcf, 0xabd13d59,
    0x26d930ac, 0x51de003a, 0xc8d75180, 0xbfd06116, 0x21b4f4b5, 0x56b3c423,
    0xcfba9599, 0xb8bda50f, 0x2802b89e, 0x5f058808, 0xc60cd9b2, 0xb10be924,
    0x2f6f7c87, 0x58684c11, 0xc1611dab, 0xb6662d3d, 0x76dc4190, 0x01db7106,
    0x98d220bc, 0xefd5102a, 0x71b18589, 0x06b6b51f, 0x9fbfe4a5, 0xe8b8d433,
    0x7807c9a2, 0x0f00f934, 0x9609a88e, 0xe10e9818, 0x7f6a0dbb, 0x086d3d2d,
    0x91646c97, 0xe6635c01, 0x6b6b51f4, 0x1c6c6162, 0x856530d8, 0xf262004e,
    0x6c0695ed, 0x1b01a57b, 0x8208f4c1, 0xf50fc457, 0x65b0d9c6, 0x12b7e950,
    0x8bbeb8ea, 0xfcb9887c, 0x62dd1ddf, 0x15da2d49, 0x8cd37cf3, 0xfbd44c65,
    0x4db26158, 0x3ab551ce, 0xa3bc0074, 0xd4bb30e2, 0x4adfa541, 0x3dd895d7,
    0xa4d1c46d, 0xd3d6f4fb, 0x4369e96a, 0x346ed9fc, 0xad678846, 0xda60b8d0,
    0x44042d73, 0x33031de5, 0xaa0a4c5f, 0xdd0d7cc9, 0x5005713c, 0x270241aa,
    0xbe0b1010, 0xc90c2086, 0x5768b525, 0x206f85b3, 0xb966d409, 0xce61e49f,
    0x5edef90e, 0x29d9c998, 0xb0d09822, 0xc7d7a8b4, 0x59b33d17, 0x2eb40d81,
    0xb7bd5c3b, 0xc0ba6cad, 0xedb88320, 0x9abfb3b6, 0x03b6e20c, 0x74b1d29a,
    0xead54739, 0x9dd277af, 0x04db2615, 0x73dc1683, 0xe3630b12, 0x94643b84,
    0x0d6d6a3e, 0x7a6a5aa8, 0xe40ecf0b, 0x9309ff9d, 0x0a00ae27, 0x7d079eb1,
    0xf00f9344, 0x8708a3d2, 0x1e01f268, 0x6906c2fe, 0xf762575d, 0x806567cb,
    0x196c3671, 0x6e6b06e7, 0xfed41b76, 0x89d32be0, 0x10da7a5a, 0x67dd4acc,
    0xf9b9df6f, 0x8ebeeff9, 0x17b7be43, 0x60b08ed5, 0xd6d6a3e8, 0xa1d1937e,
    0x38d8c2c4, 0x4fdff252, 0xd1bb67f1, 0xa6bc5767, 0x3fb506dd, 0x48b2364b,
    0xd80d2bda, 0xaf0a1b4c, 0x36034af6, 0x41047a60, 0xdf60efc3, 0xa867df55,
    0x316e8eef, 0x4669be79, 0xcb61b38c, 0xbc66831a, 0x256fd2a0, 0x5268e236,
    0xcc0c7795, 0xbb0b4703, 0x220216b9, 0x5505262f, 0xc5ba3bbe, 0xb2bd0b28,
    0x2bb45a92, 0x5cb36a04, 0xc2d7ffa7, 0xb5d0cf31, 0x2cd99e8b, 0x5bdeae1d,
    0x9b64c2b0, 0xec63f226, 0x756aa39c, 0x026d930a, 0x9c0906a9, 0xeb0e363f,
    0x72076785, 0x05005713, 0x95bf4a82, 0xe2b87a14, 0x7bb12bae, 0x0cb61b38,
    0x92d28e9b, 0xe5d5be0d, 0x7cdcefb7, 0x0bdbdf21, 0x86d3d2d4, 0xf1d4e242,
    0x68ddb3f8, 0x1fda836e, 0x81be16cd, 0xf6b9265b, 0x6fb077e1, 0x18b74777,
    0x88085ae6, 0xff0f6a70, 0x66063bca, 0x11010b5c, 0x8f659eff, 0xf862ae69,
    0x616bffd3, 0x166ccf45, 0xa00ae278, 0xd70dd2ee, 0x4e048354, 0x3903b3c2,
    0xa7672661, 0xd06016f7, 0x4969474d, 0x3e6e77db, 0xaed16a4a, 0xd9d65adc,
    0x40df0b66, 0x37d83bf0, 0xa9bcae53, 0xdebb9ec5, 0x47b2cf7f, 0x30b5ffe9,
    0xbdbdf21c, 0xcabac28a, 0x53b39330, 0x24b4a3a6, 0xbad03605, 0xcdd70693,
    0x54de5729, 0x23d967bf, 0xb3667a2e, 0xc4614ab8, 0x5d681b02, 0x2a6f2b94,
    0xb40bbe37, 0xc30c8ea1, 0x5a05df1b, 0x2d02ef8d
};

// PacketProtection Implementation
uint32_t PacketProtection::SimpleXOR(uint32_t data, uint32_t key) {
    return data ^ key ^ PROTECTION_MAGIC;
}

uint64_t PacketProtection::GenerateToken(uint32_t seed, uint64_t timestamp) {
    // Generate a secure token based on seed and timestamp
    uint64_t token = seed;
    token = (token << 32) | (timestamp & 0xFFFFFFFF);
    token ^= PROTECTION_MAGIC;
    token = ((token << 13) | (token >> 51)) ^ timestamp;
    return token;
}

uint32_t PacketProtection::CalculateChecksum(const char* data, size_t length) {
    uint32_t crc = 0xFFFFFFFF;
    for (size_t i = 0; i < length; i++) {
        crc = crc32_table[(crc ^ data[i]) & 0xFF] ^ (crc >> 8);
    }
    return crc ^ 0xFFFFFFFF;
}

bool PacketProtection::ValidateTimestamp(uint64_t timestamp, uint64_t lastTimestamp) {
    if (!g_protectionConfig.enableTimestampCheck) return true;

    uint64_t currentTime = GetCurrentTimestamp();
    uint64_t timeDiff = (timestamp > lastTimestamp) ? (timestamp - lastTimestamp) : 0;

    // Check if timestamp is too far in the future or past
    if (timestamp > currentTime + 1000 || timestamp < currentTime - g_protectionConfig.maxPacketDelay) {
        return false;
    }

    // Check if packets are coming too fast (potential speed hack)
    if (timeDiff > 0 && timeDiff < 10) {
        return false;
    }

    return true;
}

bool PacketProtection::ValidateSequence(uint32_t sequence, uint32_t lastSequence) {
    if (!g_protectionConfig.enableSequenceCheck) return true;

    // Allow sequence numbers within a reasonable window
    if (sequence <= lastSequence) {
        return (lastSequence - sequence) <= g_protectionConfig.sequenceWindow;
    }

    // Don't allow sequence numbers too far ahead
    return (sequence - lastSequence) <= g_protectionConfig.sequenceWindow;
}

// Client-side functions
bool PacketProtection::InitializeClientProtection(int socket) {
    uint32_t key = GenerateRandomKey();
    socketKeys[socket] = key;
    socketTokens[socket] = GenerateToken(key, GetCurrentTimestamp());
    socketSequence[socket] = 1;
    socketTimestamp[socket] = GetCurrentTimestamp();
    return true;
}

bool PacketProtection::EncryptPacket(int socket, char* data, size_t length, unsigned char packetType) {
    if (!g_protectionConfig.enableEncryption) return true;

    auto keyIt = socketKeys.find(socket);
    if (keyIt == socketKeys.end()) {
        InitializeClientProtection(socket);
        keyIt = socketKeys.find(socket);
    }

    uint32_t key = keyIt->second;
    uint64_t timestamp = GetCurrentTimestamp();
    uint32_t sequence = ++socketSequence[socket];

    // Create protection header
    ProtectionHeader header;
    header.magic = PROTECTION_MAGIC;
    header.token = GenerateClientToken(socket, packetType);
    header.sequence = sequence;
    header.timestamp = timestamp;
    header.flags = PROTECTION_FLAG_ENCRYPTED;
    header.reserved = 0;

    // Calculate checksum of original data
    header.checksum = CalculateChecksum(data, length);

    // Simple XOR encryption of packet data
    for (size_t i = 0; i < length; i++) {
        data[i] = (char)SimpleXOR((uint32_t)(unsigned char)data[i], key + i);
    }

    // Update socket timestamp
    socketTimestamp[socket] = timestamp;

    // Log if enabled
    if (g_protectionConfig.enableLogging) {
        std::ofstream log(g_protectionConfig.logFilePath, std::ios::app);
        log << "CLIENT ENCRYPT: Socket=" << socket << " Type=" << (int)packetType
            << " Seq=" << sequence << " Time=" << timestamp << std::endl;
    }

    return true;
}

uint32_t PacketProtection::GenerateClientToken(int socket, unsigned char packetType) {
    auto keyIt = socketKeys.find(socket);
    if (keyIt == socketKeys.end()) return 0;

    uint32_t key = keyIt->second;
    uint64_t timestamp = GetCurrentTimestamp();
    uint32_t token = (uint32_t)GenerateToken(key ^ packetType, timestamp);

    return token;
}

// Server-side functions
bool PacketProtection::InitializeServerProtection(int socket, uint32_t clientKey) {
    socketKeys[socket] = clientKey;
    socketTokens[socket] = GenerateToken(clientKey, GetCurrentTimestamp());
    socketSequence[socket] = 0;
    socketTimestamp[socket] = GetCurrentTimestamp();
    return true;
}

bool PacketProtection::DecryptPacket(int socket, char* data, size_t length, unsigned char packetType) {
    if (!g_protectionConfig.enableEncryption) return true;

    auto keyIt = socketKeys.find(socket);
    if (keyIt == socketKeys.end()) {
        return false; // No key established
    }

    uint32_t key = keyIt->second;

    // Simple XOR decryption of packet data
    for (size_t i = 0; i < length; i++) {
        data[i] = (char)SimpleXOR((uint32_t)(unsigned char)data[i], key + i);
    }

    // Update anti-cheat metrics
    UpdateSocketMetrics(socket, packetType);

    // Log if enabled
    if (g_protectionConfig.enableLogging) {
        std::ofstream log(g_protectionConfig.logFilePath, std::ios::app);
        log << "SERVER DECRYPT: Socket=" << socket << " Type=" << (int)packetType << std::endl;
    }

    return true;
}

bool PacketProtection::ValidateClientToken(int socket, uint32_t token, unsigned char packetType) {
    auto keyIt = socketKeys.find(socket);
    if (keyIt == socketKeys.end()) return false;

    uint32_t key = keyIt->second;
    uint64_t timestamp = GetCurrentTimestamp();
    uint32_t expectedToken = (uint32_t)GenerateToken(key ^ packetType, timestamp);

    // Allow some tolerance for network delay
    for (int i = 0; i < 5; i++) {
        uint32_t testToken = (uint32_t)GenerateToken(key ^ packetType, timestamp - (i * 100));
        if (testToken == token) {
            return true;
        }
    }

    return false;
}

// Common functions
void PacketProtection::CleanupSocket(int socket) {
    socketKeys.erase(socket);
    socketTokens.erase(socket);
    socketSequence.erase(socket);
    socketTimestamp.erase(socket);

    // Clean up anti-cheat data
    AntiCheat::packetTimings.erase(socket);
    AntiCheat::packetCounts.erase(socket);
    AntiCheat::packetHistory.erase(socket);
}

uint64_t PacketProtection::GetCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
}

uint32_t PacketProtection::GenerateRandomKey() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint32_t> dis(0x10000000, 0xFFFFFFFF);
    return dis(gen);
}

// Advanced protection features
bool PacketProtection::DetectPacketReplay(int socket, uint64_t timestamp, uint32_t sequence) {
    if (!g_protectionConfig.enableAntiReplay) return false;

    auto timestampIt = socketTimestamp.find(socket);
    auto sequenceIt = socketSequence.find(socket);

    if (timestampIt == socketTimestamp.end() || sequenceIt == socketSequence.end()) {
        return false; // No previous data to compare
    }

    // Check for timestamp replay
    if (timestamp <= timestampIt->second) {
        return true; // Potential replay attack
    }

    // Check for sequence replay
    if (sequence <= sequenceIt->second) {
        return true; // Potential replay attack
    }

    return false;
}

bool PacketProtection::DetectPacketInjection(int socket, const char* data, size_t length) {
    // Simple heuristic checks for packet injection
    if (length == 0 || length > 65535) {
        return true; // Invalid packet size
    }

    // Check for suspicious patterns in packet data
    int nullCount = 0;
    int repeatCount = 0;
    char lastByte = data[0];

    for (size_t i = 0; i < length; i++) {
        if (data[i] == 0) nullCount++;
        if (data[i] == lastByte) repeatCount++;
        else repeatCount = 0;

        if (repeatCount > 50) return true; // Too many repeated bytes
        lastByte = data[i];
    }

    // Too many null bytes might indicate injection
    if (nullCount > length / 2) {
        return true;
    }

    return false;
}

void PacketProtection::UpdateSocketMetrics(int socket, unsigned char packetType) {
    uint64_t currentTime = GetCurrentTimestamp();

    // Update packet timings for anti-cheat analysis
    AntiCheat::packetTimings[socket].push_back(currentTime);

    // Keep only recent timings (last 60 seconds)
    auto& timings = AntiCheat::packetTimings[socket];
    timings.erase(std::remove_if(timings.begin(), timings.end(),
        [currentTime](uint64_t time) { return currentTime - time > 60000; }), timings.end());

    // Update packet counts
    AntiCheat::packetCounts[socket][packetType]++;

    // Update packet history
    auto& history = AntiCheat::packetHistory[socket];
    history.push_back(packetType);
    if (history.size() > 100) {
        history.erase(history.begin());
    }
}

// AntiCheat Implementation
bool AntiCheat::DetectSpeedHack(int socket, uint64_t timestamp) {
    auto timingsIt = packetTimings.find(socket);
    if (timingsIt == packetTimings.end() || timingsIt->second.size() < 10) {
        return false; // Not enough data
    }

    auto& timings = timingsIt->second;

    // Check if packets are coming too fast
    int fastPackets = 0;
    for (size_t i = 1; i < timings.size(); i++) {
        if (timings[i] - timings[i-1] < 10) { // Less than 10ms between packets
            fastPackets++;
        }
    }

    // If more than 50% of packets are too fast, it's suspicious
    return (fastPackets > timings.size() / 2);
}

bool AntiCheat::DetectPacketFlooding(int socket, unsigned char packetType) {
    auto countsIt = packetCounts.find(socket);
    if (countsIt == packetCounts.end()) return false;

    auto typeCountIt = countsIt->second.find(packetType);
    if (typeCountIt == countsIt->second.end()) return false;

    // Check if this packet type is being sent too frequently
    uint32_t count = typeCountIt->second;
    return count > g_protectionConfig.maxPacketsPerSecond;
}

bool AntiCheat::DetectInvalidPacketOrder(int socket, unsigned char packetType) {
    auto historyIt = packetHistory.find(socket);
    if (historyIt == packetHistory.end() || historyIt->second.size() < 5) {
        return false; // Not enough history
    }

    auto& history = historyIt->second;

    // Simple check for invalid packet sequences
    // For example, movement packets should not come before login packets
    if (packetType == 0x01 && history.back() != 0x00) { // Login after non-handshake
        return true;
    }

    return false;
}

bool AntiCheat::DetectMemoryPatching(const char* criticalData, size_t length) {
    // Simple integrity check - in a real implementation, this would be more sophisticated
    uint32_t checksum = 0;
    for (size_t i = 0; i < length; i++) {
        checksum += (unsigned char)criticalData[i];
    }

    // This is a placeholder - you would store expected checksums for critical game data
    static uint32_t expectedChecksum = 0x12345678; // Replace with actual expected value

    return checksum != expectedChecksum;
}

void AntiCheat::UpdatePlayerBehavior(int socket, unsigned char packetType) {
    // Update behavioral metrics
    uint64_t currentTime = PacketProtection::GetCurrentTimestamp();

    // Track packet patterns
    packetTimings[socket].push_back(currentTime);
    packetCounts[socket][packetType]++;

    // Analyze for suspicious patterns
    if (AnalyzeSuspiciousActivity(socket)) {
        if (g_protectionConfig.enableLogging) {
            std::ofstream log(g_protectionConfig.logFilePath, std::ios::app);
            log << "SUSPICIOUS ACTIVITY DETECTED: Socket=" << socket
                << " Type=" << (int)packetType << " Time=" << currentTime << std::endl;
        }
    }
}

bool AntiCheat::AnalyzeSuspiciousActivity(int socket) {
    int suspiciousScore = 0;

    // Check for speed hacking
    if (DetectSpeedHack(socket, PacketProtection::GetCurrentTimestamp())) {
        suspiciousScore += 3;
    }

    // Check for packet flooding
    auto countsIt = packetCounts.find(socket);
    if (countsIt != packetCounts.end()) {
        for (auto& pair : countsIt->second) {
            if (DetectPacketFlooding(socket, pair.first)) {
                suspiciousScore += 2;
            }
        }
    }

    // Check packet order
    auto historyIt = packetHistory.find(socket);
    if (historyIt != packetHistory.end() && !historyIt->second.empty()) {
        if (DetectInvalidPacketOrder(socket, historyIt->second.back())) {
            suspiciousScore += 1;
        }
    }

    return suspiciousScore >= g_protectionConfig.suspiciousThreshold;
}

// ProtectionUtils Implementation
std::string ProtectionUtils::BytesToHex(const unsigned char* data, size_t length) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (size_t i = 0; i < length; i++) {
        ss << std::setw(2) << static_cast<unsigned>(data[i]);
    }
    return ss.str();
}

std::vector<unsigned char> ProtectionUtils::HexToBytes(const std::string& hex) {
    std::vector<unsigned char> bytes;
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        unsigned char byte = static_cast<unsigned char>(strtol(byteString.c_str(), nullptr, 16));
        bytes.push_back(byte);
    }
    return bytes;
}

uint32_t ProtectionUtils::CRC32(const char* data, size_t length) {
    return PacketProtection::CalculateChecksum(data, length);
}

std::string ProtectionUtils::Base64Encode(const unsigned char* data, size_t length) {
    static const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::string result;
    int val = 0, valb = -6;

    for (size_t i = 0; i < length; i++) {
        val = (val << 8) + data[i];
        valb += 8;
        while (valb >= 0) {
            result.push_back(chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (result.size() % 4) {
        result.push_back('=');
    }

    return result;
}

std::vector<unsigned char> ProtectionUtils::Base64Decode(const std::string& encoded) {
    static const int T[128] = {
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,-1,
        -1,-1,-1,-1, -1,-1,-1,-1, -1,-1,-1,62, -1,-1,-1,63,
        52,53,54,55, 56,57,58,59, 60,61,-1,-1, -1,-1,-1,-1,
        -1, 0, 1, 2,  3, 4, 5, 6,  7, 8, 9,10, 11,12,13,14,
        15,16,17,18, 19,20,21,22, 23,24,25,-1, -1,-1,-1,-1,
        -1,26,27,28, 29,30,31,32, 33,34,35,36, 37,38,39,40,
        41,42,43,44, 45,46,47,48, 49,50,51,-1, -1,-1,-1,-1
    };

    std::vector<unsigned char> result;
    int val = 0, valb = -8;

    for (unsigned char c : encoded) {
        if (T[c] == -1) break;
        val = (val << 6) + T[c];
        valb += 6;
        if (valb >= 0) {
            result.push_back(char((val >> valb) & 0xFF));
            valb -= 8;
        }
    }

    return result;
}

uint32_t ProtectionUtils::SecureRandom() {
    return PacketProtection::GenerateRandomKey();
}

void ProtectionUtils::SecureRandomBytes(unsigned char* buffer, size_t length) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<unsigned char> dis(0, 255);

    for (size_t i = 0; i < length; i++) {
        buffer[i] = dis(gen);
    }
}