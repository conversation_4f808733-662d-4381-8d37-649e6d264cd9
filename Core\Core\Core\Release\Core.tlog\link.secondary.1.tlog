^G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\BASE64.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\BUFFERTEST.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\C++14_COMPATIBILITY_TEST.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\CORE.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\IBUFF.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\ICHAR.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\IITEM.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\INTERFACE.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\IQUEST.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\ISKILL.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\MEMORY.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\PACKETPROTECTION.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\SHA256.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\SHELL.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\SQLITE3.OBJ|G:\KAL\NEW PROJECT\PACKET PROTECTION\CORE\CORE\CORE\RELEASE\TOOLS.OBJ
G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.lib
G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.EXP
G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\Core.iobj
