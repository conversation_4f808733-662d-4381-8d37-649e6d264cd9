#include <windows.h>
#include "IBuff.h"
#include "IChar.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <time.h>
#include "Functions.h"

IBuff::IBuff(void* Offset)
{
	if (!Offset) {
		throw std::invalid_argument("IBuff::IBuff: Offset cannot be null");
	}
	this->Offset = Offset;
}

IBuff::~IBuff()
{
}

void* IBuff::GetOffset()
{
	if (!this->Offset) {
		throw std::runtime_error("IBuff::GetOffset: Offset is null");
	}
	return this->Offset;
}

int IBuff::GetValue() {
	try {
		if (this->Exists() && this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 12)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int IBuff::GetBuffID() {
	try {
		if (this->Exists() && this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 4)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int IBuff::GetTime() {
	try {
		int Check = 0;
		if (this->Exists() && this->Offset) {
			Check = static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 8)));

			int buffID = this->GetBuffID();
			int currentTime = static_cast<int>(time(0));
			DWORD tickCount = GetTickCount();

			if (Check > 0 && ((buffID >= 119 && buffID <= 155) || (buffID >= 30 && buffID <= 32) || buffID == 99 || buffID == 101) && Check > currentTime) {
				Check = Check - currentTime;
			}

			if (Check > 0 && buffID >= 256 && Check > static_cast<int>(tickCount)) {
				Check = (Check - static_cast<int>(tickCount)) / 1000;
			}
		}

		return Check;
	} catch (...) {
		return 0;
	}
}

bool IBuff::Exists() {
	try {
		if (this->Offset) {
			if (CBase::IsDeleted(reinterpret_cast<int>(this->Offset))) {
				return false;
			}
			return true;
		}
		return false;
	} catch (...) {
		return false;
	}
}

int IBuff::GetType() {
	try {
		if (this->Exists() && this->Offset) {
			return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 23)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

void IBuff::updateValue(int value) {
	try {
		if (this->Exists() && this->Offset) {
			*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 12)) = static_cast<DWORD>(value);
		}
	} catch (...) {
		// Silently handle errors
	}
}

void IBuff::DeleteThis() {
	try {
		delete this;
	} catch (...) {
		// Handle deletion errors gracefully
	}
}