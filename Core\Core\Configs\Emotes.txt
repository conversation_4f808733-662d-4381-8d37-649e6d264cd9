; Emote System Configuration
; Format: (emote (quest %d) (effect "effect_name"))
; Quest: Quest ID that triggers the emote
; Effect: Effect name to display when emote is triggered

; Example emote configurations:
(emote (quest 1001) (effect "emote_10"))
(emote (quest 1002) (effect "emote_dance"))
(emote (quest 1003) (effect "emote_wave"))
(emote (quest 1004) (effect "emote_bow"))
(emote (quest 1005) (effect "emote_cheer"))
