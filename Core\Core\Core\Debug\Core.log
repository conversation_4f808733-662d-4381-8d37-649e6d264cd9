﻿  base64.cpp
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\base64.cpp(60,65): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\base64.cpp(71,64): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
  Core.cpp
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\TargetFind.h(24,39): warning C4302: 'type cast': truncation from 'const char *' to 'char'
  (compiling source file 'Core.cpp')
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Exports.h(5,7): error C2664: 'HMODULE LoadLibraryW(LPCWSTR)': cannot convert argument 1 from 'const char [15]' to 'LPCWSTR'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Exports.h(5,19):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h(601,1):
      see declaration of 'LoadLibraryW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Exports.h(5,7):
      while trying to match the argument list '(const char [15])'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5012,7): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5012,40):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5012,7):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5029,8): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5029,42):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ReadConfig.h(5029,8):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(955,12): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(955,45):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(955,12):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(969,12): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(969,49):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Process.h(969,12):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1869,2): error C2664: 'DWORD GetModuleFileNameW(HMODULE,LPWSTR,DWORD)': cannot convert argument 2 from 'char [260]' to 'LPWSTR'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1869,26):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h(219,1):
      see declaration of 'GetModuleFileNameW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1869,2):
      while trying to match the argument list '(int, char [260], int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1928,2): error C2664: 'HINSTANCE ShellExecuteW(HWND,LPCWSTR,LPCWSTR,LPCWSTR,LPCWSTR,INT)': cannot convert argument 2 from 'const char [5]' to 'LPCWSTR'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1928,21):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h(96,22):
      see declaration of 'ShellExecuteW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\ExpTable.h(1928,2):
      while trying to match the argument list '(int, const char [5], const _Elem *, int, int, int)'
          with
          [
              _Elem=char
          ]
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1792,9): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1792,43):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1792,9):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1806,9): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1806,47):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1806,9):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1830,10): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1830,44):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1830,10):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1844,10): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1844,48):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1844,10):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1908,9): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1908,43):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1908,9):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1922,9): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1922,47):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1922,9):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1947,10): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1947,44):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1947,10):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1961,10): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1961,48):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(1961,10):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2004,8): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2004,41):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2004,8):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2018,8): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2018,45):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2018,8):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2043,10): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2043,44):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2043,10):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2057,10): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2057,48):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Command.h(2057,10):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2): error C2664: 'BOOL CreateDirectoryW(LPCWSTR,LPSECURITY_ATTRIBUTES)': cannot convert argument 1 from 'const _Elem *' to 'LPCWSTR'
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2): error C2664:         with
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2): error C2664:         [
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2): error C2664:             _Elem=char
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2): error C2664:         ]
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,28):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h(60,1):
      see declaration of 'CreateDirectoryW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(6,2):
      while trying to match the argument list '(const _Elem *, int)'
          with
          [
              _Elem=char
          ]
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10): error C2664: 'HANDLE FindFirstFileW(LPCWSTR,LPWIN32_FIND_DATAW)': cannot convert argument 1 from 'const _Elem *' to 'LPCWSTR'
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10): error C2664:         with
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10): error C2664:         [
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10): error C2664:             _Elem=char
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10): error C2664:         ]
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,34):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h(248,1):
      see declaration of 'FindFirstFileW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(8,10):
      while trying to match the argument list '(const _Elem *, WIN32_FIND_DATA *)'
          with
          [
              _Elem=char
          ]
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42): error C2440: 'type cast': cannot convert from 'WCHAR [260]' to 'std::string'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42):
      'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string': no overloaded function could convert all the argument types
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(744,5):
          could be 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )'
          with
          [
              _Elem=char
          ]
              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42):
              'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )': cannot convert argument 1 from 'WCHAR [260]' to 'const _Elem *const '
          with
          [
              _Elem=char
          ]
                  G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                  Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(1316,5):
          or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)'
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42):
              'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)': cannot convert argument 1 from 'WCHAR [260]' to 'std::initializer_list<_Elem>'
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
                  G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                  'std::initializer_list<_Elem>::initializer_list': no overloaded function could convert all the argument types
          with
          [
              _Elem=char
          ]
                      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\initializer_list(51,1):
                      could be 'std::initializer_list<_Elem>::initializer_list(std::initializer_list<_Elem> &&)'
          with
          [
              _Elem=char
          ]
                          G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                          'std::initializer_list<_Elem>::initializer_list(std::initializer_list<_Elem> &&)': cannot convert argument 1 from 'WCHAR [260]' to 'std::initializer_list<_Elem> &&'
          with
          [
              _Elem=char
          ]
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Reason: cannot convert from 'WCHAR [260]' to 'std::initializer_list<_Elem>'
          with
          [
              _Elem=char
          ]
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Conversion requires a second user-defined-conversion operator or constructor
                      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\initializer_list(51,1):
                      or       'std::initializer_list<_Elem>::initializer_list(const std::initializer_list<_Elem> &)'
          with
          [
              _Elem=char
          ]
                          G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                          'std::initializer_list<_Elem>::initializer_list(const std::initializer_list<_Elem> &)': cannot convert argument 1 from 'WCHAR [260]' to 'const std::initializer_list<_Elem> &'
          with
          [
              _Elem=char
          ]
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Reason: cannot convert from 'WCHAR [260]' to 'const std::initializer_list<_Elem>'
          with
          [
              _Elem=char
          ]
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Conversion requires a second user-defined-conversion operator or constructor
                      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                      while trying to match the argument list '(WCHAR [260])'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(688,5):
          or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Alloc &) noexcept'
          with
          [
              _Alloc=std::allocator<char>
          ]
              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42):
              'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Alloc &) noexcept': cannot convert argument 1 from 'WCHAR [260]' to 'const _Alloc &'
          with
          [
              _Alloc=std::allocator<char>
          ]
                  G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                  Reason: cannot convert from 'WCHAR [260]' to 'const _Alloc'
          with
          [
              _Alloc=std::allocator<char>
          ]
                  G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                  'std::allocator<char>::allocator': no overloaded function could convert all the argument types
                      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xmemory(972,5):
                      could be 'std::allocator<char>::allocator(const std::allocator<char> &) noexcept'
                          G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                          'std::allocator<char>::allocator(const std::allocator<char> &) noexcept': cannot convert argument 1 from 'WCHAR [260]' to 'const std::allocator<char> &'
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Reason: cannot convert from 'WCHAR [260]' to 'const std::allocator<char>'
                              G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                              Conversion requires a second user-defined-conversion operator or constructor
                      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xmemory(970,5):
                      or       'std::allocator<char>::allocator(void) noexcept'
                          G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                          'std::allocator<char>::allocator': function does not take 1 arguments
                      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xmemory(974,5):
                      or       'std::allocator<char>::allocator(const std::allocator<_Other> &) noexcept'
                      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,64):
                      while trying to match the argument list '(WCHAR [260])'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(774,5):
          or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(_Iter,_Iter,const _Alloc &)'
          with
          [
              _Alloc=std::allocator<char>
          ]
          G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(16,42):
          while trying to match the argument list '(WCHAR [260])'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5): error C2664: 'HMODULE LoadLibraryW(LPCWSTR)': cannot convert argument 1 from 'const _Elem *' to 'LPCWSTR'
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5): error C2664:         with
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5): error C2664:         [
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5): error C2664:             _Elem=char
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5): error C2664:         ]
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,31):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h(601,1):
      see declaration of 'LoadLibraryW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Loader.h(17,5):
      while trying to match the argument list '(const _Elem *)'
          with
          [
              _Elem=char
          ]
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(119,7): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(119,40):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(119,7):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(133,7): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(133,44):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(133,7):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(234,7): error C2664: 'SQLRETURN SQLConnectW(SQLHDBC,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT,SQLWCHAR *,SQLSMALLINT)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(234,40):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(85,19):
      see declaration of 'SQLConnectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(234,7):
      while trying to match the argument list '(HDBC, unsigned char *, int, int, int, int, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(248,7): error C2664: 'SQLRETURN SQLExecDirectW(SQLHSTMT,SQLWCHAR *,SQLINTEGER)': cannot convert argument 2 from 'unsigned char *' to 'SQLWCHAR *'
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(248,44):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sqlucode.h(126,19):
      see declaration of 'SQLExecDirectW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Start.h(248,7):
      while trying to match the argument list '(HSTMT, unsigned char *, int)'
  
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3): error C2664: 'BOOL AppendMenuW(HMENU,UINT,UINT_PTR,LPCWSTR)': cannot convert argument 4 from 'const _Elem *' to 'LPCWSTR'
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3): error C2664:         with
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3): error C2664:         [
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3): error C2664:             _Elem=char
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3): error C2664:         ]
  (compiling source file 'Core.cpp')
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,89):
      Types pointed to are unrelated; conversion requires reinterpret_cast, C-style cast or parenthesized function-style cast
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h(7632,1):
      see declaration of 'AppendMenuW'
      G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Menu.h(54,3):
      while trying to match the argument list '(HMENU, long, UINT_PTR, const _Elem *)'
          with
          [
              _Elem=char
          ]
  
  IBuff.cpp
  IChar.cpp
  IItem.cpp
  Interface.cpp
  IQuest.cpp
  ISkill.cpp
  Memory.cpp
  sha256.cpp
  Tools.cpp
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Tools.cpp(498,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
G:\kal backup\XEAxMaste Package Modded By HighGamer\XEAxMaste Package\Sources\Core\Core\Tools.cpp(502,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
  Generating Code...
