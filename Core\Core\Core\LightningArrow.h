void __fastcall StormActivateShiny(IChar IPlayer, IChar Target)
{
	if (Target.IsValid() && IPlayer.IsValid() && Target.IsBuff(BuffNames::ShinyTarget))
	{
		if (Target.GetBuffValue(BuffNames::ShinyDelay) >= GetTickCount())
			return;

		if (Target.GetBuffValue(BuffNames::ShinyTarget) && Target.IsBuff(307))
		{
			IChar Caster((void*)Target.GetBuffValue(BuffNames::ShinyTarget));

			Target.UpdateBuff(BuffNames::ShinyDelay, BuffNames::BuffTime, GetTickCount() + 500);

			if (IPlayer.IsValid() && Caster.IsValid() && Target.IsValid())
			{
				if (!IPlayer.IsInRange(Target,300))
					return;

				if (!Caster.IsInRange(Target,300))
					return;

				int pSkill = Caster.GetSkillPointer(67);

				if (pSkill && Target.IsValid())
				{
					bool IsDMGEdit = false;
					int SkillDmg = IPlayer.GetDamage(67, Target.GetOffset());
					int OldSkillDmg = 0;
					if (SkillDmg) {
						OldSkillDmg = IPlayer.GetProperty(PlayerProperty::SkillsEdit);
						IPlayer.SetProperty(PlayerProperty::SkillsEdit, 67);
						IsDMGEdit = true;
					}

					ISkill xSkill((void*)pSkill);
					int nSkillGrade = xSkill.GetGrade();
					int Around = Target.GetObjectListAround(3);
					while (Around)
					{
						IChar Object((void*)CBaseList::Offset((void*)Around));

						if (Object.IsValid() && Caster.IsValid() && IPlayer.IsValid() && Target.IsValid() && (*(int(__thiscall **)(int, int, DWORD))(*(DWORD *)Caster.GetOffset() + 176))((int)Caster.GetOffset(), (int)Object.GetOffset(), 0))
						{
							if (!(Target.GetType() == 0 && CChar::IsGState((int)IPlayer.Offset, 128) && !CChar::IsGState((int)Object.Offset, 128)) && !(Target.GetType() == 0 && CChar::IsGState((int)Target.Offset, 256) && Object.GetID() != Target.GetID()) && Target.GetType() == Object.GetType()) {
								int nDmg = (Caster.GetMagic() * MLAMul) + (nSkillGrade * CTools::Rate(MLAMin, MLAMax));

								if (Object.GetType() == 0)
									nDmg = nDmg * MLAReduce / 100;

								Caster.OktayDamageArea(Object, nDmg, 67);

								if (Object.GetType() == 0 && Caster.GetID() != Object.GetID() && Object.GetID() != IPlayer.GetID())
									Object.AddFxToTarget("davi_ef129", 1, 0, 0);

								if (Object.GetType() == 1)
									Object.AddFxToTarget("davi_ef129", 1, 0, 0);
							}
						}

						Around = CBaseList::Pop((void*)Around);
					}

					if (IsDMGEdit)
						IPlayer.SetProperty(PlayerProperty::SkillsEdit, OldSkillDmg);
				}
			} else {
				if (Target.IsValid())
				{
					Target.CancelBuff(307);
					Target.UpdateBuff(BuffNames::ShinyTarget, BuffNames::BuffTime, 0);
				}
			}
		}
	}
}

void __fastcall ActivateShiny(IChar IPlayer, int pPacket, int pPos)
{
	int nTargetID = 0; char bType = 0; void *pTarget = 0;
	CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);

	if (bType >= 2)
		return;

	TargetFind myTarget(bType, 0, nTargetID);
		pTarget = myTarget.getTarget();

	IChar Target(pTarget);

	if (pTarget && IPlayer.IsValid() && Target.IsValid() && Target.IsBuff(BuffNames::ShinyTarget))
	{
		if (Target.GetBuffValue(BuffNames::ShinyDelay) >= GetTickCount())
			return;

		if (Target.GetBuffValue(BuffNames::ShinyTarget) && Target.IsBuff(307))
		{
			IChar Caster((void*)Target.GetBuffValue(BuffNames::ShinyTarget));
			Target.UpdateBuff(BuffNames::ShinyDelay, BuffNames::BuffTime, GetTickCount() + 500);

			if (IPlayer.IsValid() && Caster.IsValid() && Target.IsValid())
			{
				if (!IPlayer.IsInRange(Target,300))
					return;

				if (!Caster.IsInRange(Target,300))
					return;

				int pSkill = Caster.GetSkillPointer(67);

				if (pSkill && Target.IsValid())
				{
					bool IsDMGEdit = false;
					int OldSkillDmg = 0;
					int SkillDmg = IPlayer.GetDamage(67, Target.GetOffset());
					if (SkillDmg) {
						OldSkillDmg = IPlayer.GetProperty(PlayerProperty::SkillsEdit);
						IPlayer.SetProperty(PlayerProperty::SkillsEdit, 67);
						IsDMGEdit = true;
					}

					ISkill xSkill((void*)pSkill);
					int nSkillGrade = xSkill.GetGrade();
					int Around = Target.GetObjectListAround(3);

					while(Around)
					{
						IChar Object((void*)CBaseList::Offset((void*)Around));

						if (Object.IsValid() && Caster.IsValid() && IPlayer.IsValid() && Target.IsValid() && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)Caster.GetOffset() + 176))((int)Caster.GetOffset(), (int)Object.GetOffset(), 0))
						{
							if (!(Target.GetType() == 0 && CChar::IsGState((int)IPlayer.Offset, 128) && !CChar::IsGState((int)Object.Offset, 128)) && !(Target.GetType() == 0 && CChar::IsGState((int)Target.Offset, 256) && Object.GetID() != Target.GetID()))
							{
								if (Target.GetType() == Object.GetType()) {
									int nDmg = (Caster.GetMagic() * MLAMul) + (nSkillGrade * CTools::Rate(MLAMin, MLAMax));

									if (Object.GetType() == 0)
										nDmg = nDmg * MLAReduce / 100;

									Caster.OktayDamageArea(Object, nDmg, 67);

									if (Object.GetType() == 0 && Caster.GetID() != Object.GetID() && Object.GetID() != IPlayer.GetID())
										Object.AddFxToTarget("davi_ef129", 1, 0, 0);

									if (Object.GetType() == 1)
										Object.AddFxToTarget("davi_ef129", 1, 0, 0);
								}
							}
						}

						Around = CBaseList::Pop((void*)Around);
					}
					if (IsDMGEdit)
						IPlayer.SetProperty(PlayerProperty::SkillsEdit, OldSkillDmg);
				}
			} else {
				if (Target.IsValid())
				{
					Target.CancelBuff(307);
					Target.UpdateBuff(BuffNames::ShinyTarget, BuffNames::BuffTime, 0);
				}
			}
		}
	}
	
}

void __fastcall LightningArrow(IChar IPlayer, int pPacket, int pPos)
{
	int pSkill = IPlayer.GetSkillPointer(67);

	if (IPlayer.IsValid() && pSkill)
	{
		ISkill xSkill((void*)pSkill);
		int nTargetID = 0; char bType = 0; void *pTarget = 0;
		CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
		int nMana = IPlayer.GetLevel() * 4 + 120;

		TargetFind myTarget(bType, 0, nTargetID);
		pTarget = myTarget.getTarget();

		if (bType >= 2)
			return;

		IChar Target(pTarget);

		if (pTarget && IPlayer.IsValid() && Target.IsValid())
		{
			if (pTarget == IPlayer.GetOffset()){
				
				return;
			}

			if (IPlayer.GetCurMp() < nMana){
				
				return;
			}

			int nDmg = (IPlayer.GetMagic() * xSkill.GetGrade()) / 3;

			if (IPlayer.IsValid() && Target.IsValid())
			{
				if (!IPlayer.IsInRange(Target,300)){
					
					return;
				}
				
				if (strlen(AntiKsCheck) && ((std::string)AntiKsCheck == "true" || (std::string)AntiKsCheck == "True"))
				{
					if (Target.GetType() == 1 && IPlayer.GetType() == 0) {
						if (!AntiKs.count(Target.GetMobIndex()) && Target.GetMobTanker()) {
							IChar Tanker((void*)Target.GetMobTanker());

							if ((Tanker.IsParty() && Tanker.GetPartyID() != IPlayer.GetPartyID()) || (!Tanker.IsParty() && Tanker.GetPID() != IPlayer.GetPID()))
							{
								IPlayer.SystemMessage("[Anti ks] Our server doesn't tolerate ksing.", TEXTCOLOR_RED); 
								
								return;
							}
						}
					}
				}
				if (Target.IsBuff(307))
					StormActivateShiny(IPlayer,Target);

				Target.UpdateBuff(BuffNames::ShinyTarget, BuffNames::BuffTime, (int)IPlayer.GetOffset());
				IPlayer.SetDirection(Target);
				IPlayer.OktayDamageSingle(Target, nDmg, 67);
				Target.Buff(307,600,0);
				IPlayer.DecreaseMana(nMana);
			}
		}
		
	}
}

int __cdecl CMonsterMagicCreate(int Index, int Value, int Argument, int Arg, int Player, int Damage, int Time)
{
	IChar Object((void*)Player);

	if (Object.IsOnline()) {
		switch (Index) {

			//CThunderStorm
		case 228: {
			if (ConfigCalculations.count(143))
				Damage += ((Damage * ConfigCalculations.find(143)->second.Rate) / 100);
			break;
		}

				  //CIceStorm
		case 229: {
			if (ConfigCalculations.count(145))
				Damage += ((Damage * ConfigCalculations.find(145)->second.Rate) / 100);
			break;
		}

				  //CFireRain
		case 230: {
			if (ConfigCalculations.count(148))
				Damage += ((Damage * ConfigCalculations.find(148)->second.Rate) / 100);
			break;
		}

				  //poison cloud
		case 231: {
			if (ConfigCalculations.count(242))
				Damage += ((Damage * ConfigCalculations.find(242)->second.Rate) / 100);
			break;
		}
		}
	}

	int check = CMonsterMagic::Create(Index,Value,Argument,Arg,Player,Damage,Time);

	if (check)
	{
		if (Object.IsOnline() && Object.GetType() == 0) {
			if (Index == 228) {
				*(DWORD*)(check + 144) = 45236;
				*(DWORD*)(check + 124) = Player;
			}
		}
	}

	return check;
}

int __fastcall MagicTick(void *Monster, void *edx)
{
	IChar IMonster(Monster);
	int result = CMonster::IsRemoved(Monster);

	if (!result)
	{
		CChar::Lock(Monster);
		IChar IPlayer((void*)*(DWORD*)((int)Monster+484));

		if (*(DWORD*)((int)Monster + 468) < GetTickCount())
		{
			CChar::Unlock(Monster);
			return CSkill::MagicTick(Monster);
		}

		if (*(DWORD*)((int)Monster + 484))
		{
			if (CBase::IsDeleted(*(DWORD*)((int)Monster + 484)))
			{
				CChar::Unlock(Monster);
				return CSkill::MagicTick(Monster);
			}

			if (IPlayer.IsValid())
			{
				if (IPlayer.GetType() == 0)
				{
					int Index = IMonster.GetMobIndex();
					if (IMonster.GetMobIndex() == 231)
					{
						int Dmg = *(DWORD*)((int)Monster + 136);
						int Around = IMonster.GetObjectListAround(4);

						while(Around)
						{
							IChar Object((void*)CBaseList::Offset((void*)Around));

							if (Object.IsValid() && IPlayer.IsValid() && IMonster.GetOffset() != Object.GetOffset() && (*(int(__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
							{
								int Check = IPlayer.GetDamage(42, Object.GetOffset());
								if (Check)
									Dmg = Check;

								if (PVPConfigCalculations.count((IPlayer.GetClass() * 1000) + 42 + (Object.GetClass() * 100)))
									Dmg += ((Dmg * PVPConfigCalculations.find((IPlayer.GetClass() * 1000) + 42 + (Object.GetClass() * 100))->second.Rate) / 100);

								if (PVPConfigCalculations.count((IPlayer.GetClass() * 1000) + 42 + 500))
									Dmg += ((Dmg * PVPConfigCalculations.find((IPlayer.GetClass() * 1000) + 42 + 500)->second.Rate) / 100);

								if (Object.GetType() == 0)
									IPlayer.OktayDamageStorm(Object, Dmg);
								else
									IPlayer.OktayDamageStorm(Object, Dmg * 3);
							}

							Around = CBaseList::Pop((void*)Around);
						}
					} else {
						IPlayer.UpdateBuff(BuffNames::MageMICheck, BuffNames::BuffTime, 43);

						int Dmg = *(DWORD*)((int)Monster + 136);
						int Around = IMonster.GetObjectListAround(4);
						while(Around)
						{
							IChar Object((void*)CBaseList::Offset((void*)Around));

							if (Object.IsValid() && IPlayer.IsValid() && IMonster.GetOffset() != Object.GetOffset() && (*(int(__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0)) {
								int SkillID = 0;
								switch (Index) {
								case 228: SkillID = 43;	break;
								case 229: SkillID = 45; break;
								case 230: SkillID = 48; break;
								}

								if (SkillID) {
									int Check = IPlayer.GetDamage(SkillID, Object.GetOffset());
									if (Check)
										Dmg = Check;

									if (PVPConfigCalculations.count((IPlayer.GetClass() * 1000) + SkillID + (Object.GetClass() * 100)))
										Dmg += ((Dmg * PVPConfigCalculations.find((IPlayer.GetClass() * 1000) + SkillID + (Object.GetClass() * 100))->second.Rate) / 100);

									if (PVPConfigCalculations.count((IPlayer.GetClass() * 1000) + SkillID + 500))
										Dmg += ((Dmg * PVPConfigCalculations.find((IPlayer.GetClass() * 1000) + SkillID + 500)->second.Rate) / 100);
								}
								IPlayer.OktayDamageStorm(Object, Dmg);

								if (SkillID == 45) {
									int Skill = IPlayer.GetSkillPointer(45);
									if (Skill && *(DWORD *)((int)IPlayer.GetOffset() + 972)) {
										if((*(int(__thiscall **)(int, int, int))(*(DWORD *)Skill + 40))(Skill, (int)IPlayer.GetOffset(), (int)Object.GetOffset())){
											int Buff = CBuff::CreateBuff(0, *(DWORD *)((int)IPlayer.GetOffset() + 972) + 2, -20 * *(DWORD *)((int)IPlayer.GetOffset() + 972), Skill);
											if(Buff)
												(*(void(__thiscall **)(int, int))(*(DWORD *)(int)Object.GetOffset() + 180))((int)Object.GetOffset(), Buff);
										}
									}
								}
							}
							Around = CBaseList::Pop((void*)Around);
						}
					}

					if (IMonster.GetMobIndex() == 228 && *(DWORD*)((int)IMonster.GetOffset() + 144) == 45236)
					{
						IChar IPlayer((void*)*(DWORD*)((int)IMonster.GetOffset() + 124));

						if (IPlayer.IsValid())
						{
							int Around = IMonster.GetObjectListAround(3);
							while(Around)
							{
								IChar Object((void*)CBaseList::Offset((void*)Around));

								if (Object.IsValid() && IPlayer.IsValid() && IPlayer.GetOffset() != Object.GetOffset() && IMonster.GetOffset() != Object.GetOffset() && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
								{
									if (Object.IsBuff(307))
										StormActivateShiny(IPlayer,Object);
								}

								Around = CBaseList::Pop((void*)Around);
							}
						}
					}
				} else {
					if (IPlayer.GetType() == 1)
					{
						int Around = IPlayer.GetObjectListAround(4);

						while(Around)
						{
							IChar Object((void*)CBaseList::Offset((void*)Around));

							if (IPlayer.IsValid() && IPlayer.GetOffset() != Object.GetOffset() && Object.GetType() == 0)
								IPlayer.OktayDamageStorm(Object,CTools::Rate(750,1000));

							Around = CBaseList::Pop((void*)Around);
						}
					}
				}
			}
		}

		result = CChar::Unlock(Monster);
	}

	return result;
}