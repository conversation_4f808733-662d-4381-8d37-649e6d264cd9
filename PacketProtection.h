#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <map>
#include <random>
#include <chrono>

// Advanced Packet Protection System for KalOnline
// This system provides multiple layers of security to prevent packet manipulation

class PacketProtection {
public:
    // Encryption keys and tokens (made public for access)
    static std::map<int, uint32_t> socketKeys;           // Per-socket encryption keys
    static std::map<int, uint64_t> socketTokens;         // Per-socket authentication tokens
    static std::map<int, uint32_t> socketSequence;       // Per-socket sequence numbers
    static std::map<int, uint64_t> socketTimestamp;      // Per-socket last packet timestamp


    // Encryption/Decryption functions (made public for access)
    static uint32_t SimpleXOR(uint32_t data, uint32_t key);
    static uint64_t GenerateToken(uint32_t seed, uint64_t timestamp);
    static uint32_t CalculateChecksum(const char* data, size_t length);
    static bool ValidateTimestamp(uint64_t timestamp, uint64_t lastTimestamp);
    static bool ValidateSequence(uint32_t sequence, uint32_t lastSequence);
    // Client-side functions
    static bool InitializeClientProtection(int socket);
    static bool EncryptPacket(int socket, char* data, size_t length, unsigned char packetType);
    static uint32_t GenerateClientToken(int socket, unsigned char packetType);
    static uint32_t GenerateEnhancedToken(int socket, unsigned char packetType, size_t packetSize);

    // Server-side functions
    static bool InitializeServerProtection(int socket, uint32_t clientKey);
    static bool DecryptPacket(int socket, char* data, size_t length, unsigned char packetType);
    static bool ValidateClientToken(int socket, uint32_t token, unsigned char packetType);
    static bool ValidateServerSequence(int socket, unsigned char packetType);
    static bool ValidateServerTimestamp(int socket, unsigned char packetType);

    // Client-side validation functions
    static bool ValidateSequence(int socket, unsigned char packetType);
    static bool ValidateTimestamp(int socket, unsigned char packetType);

    // Common functions
    static void CleanupSocket(int socket);
    static uint64_t GetCurrentTimestamp();
    static uint32_t GenerateRandomKey();

    // Removed unused advanced protection features - keeping system simple
};

// Protection header structure that gets added to each packet
// Removed unused ProtectionHeader structure - keeping system simple

// Protection flags
#define PROTECTION_FLAG_ENCRYPTED   0x0001
#define PROTECTION_FLAG_COMPRESSED  0x0002
#define PROTECTION_FLAG_PRIORITY    0x0004
#define PROTECTION_FLAG_HEARTBEAT   0x0008

// Error codes
#define PROTECTION_SUCCESS          0
#define PROTECTION_ERROR_INVALID    -1
#define PROTECTION_ERROR_TIMEOUT    -2
#define PROTECTION_ERROR_SEQUENCE   -3
#define PROTECTION_ERROR_CHECKSUM   -4
#define PROTECTION_ERROR_TOKEN      -5
#define PROTECTION_ERROR_REPLAY     -6

// Protection configuration constants
#define MAX_PACKET_DELAY            5000    // Maximum packet delay in milliseconds
#define SEQUENCE_WINDOW             100     // Sequence number validation window
#define MAX_PACKETS_PER_SECOND      50      // Rate limiting threshold
#define SUSPICIOUS_THRESHOLD        10      // Suspicious activity threshold
#define PROTECTION_MAGIC            0x4B414C  // "KAL" magic number

// Macro for easy protection integration
#define PROTECT_PACKET(socket, data, length, type) \
    PacketProtection::EncryptPacket(socket, data, length, type)

#define VALIDATE_PACKET(socket, data, length, type) \
    PacketProtection::DecryptPacket(socket, data, length, type)

// Advanced anti-cheat features
class AntiCheat {
public:
    // Detect common packet manipulation techniques
    static bool DetectSpeedHack(int socket, uint64_t timestamp);
    static bool DetectPacketFlooding(int socket, unsigned char packetType);
    static bool DetectInvalidPacketOrder(int socket, unsigned char packetType);
    static bool DetectMemoryPatching(const char* criticalData, size_t length);

    // Behavioral analysis
    static void UpdatePlayerBehavior(int socket, unsigned char packetType);
    static bool AnalyzeSuspiciousActivity(int socket);

    // Static data members (made public for access)
    static std::map<int, std::vector<uint64_t>> packetTimings;
    static std::map<int, std::map<unsigned char, uint32_t>> packetCounts;
    static std::map<int, std::vector<unsigned char>> packetHistory;
};

// Utility functions for string and data manipulation
class ProtectionUtils {
public:
    static std::string BytesToHex(const unsigned char* data, size_t length);
    static std::vector<unsigned char> HexToBytes(const std::string& hex);
    static uint32_t CRC32(const char* data, size_t length);
    static std::string Base64Encode(const unsigned char* data, size_t length);
    static std::vector<unsigned char> Base64Decode(const std::string& encoded);

    // Secure random number generation
    static uint32_t SecureRandom();
    static void SecureRandomBytes(unsigned char* buffer, size_t length);
};

// Configuration structure for protection system
struct ProtectionConfig {
    bool enableEncryption = true;
    bool enableSequenceCheck = true;   // Enable sequence validation
    bool enableTimestampCheck = true;   // Enable timestamp validation
    bool enableChecksumValidation = false;  // Keep disabled for now
    bool enableAntiReplay = false;      // Keep disabled for now
    bool enableBehaviorAnalysis = true; // Enable behavior analysis (anti-cheat)

    uint32_t maxPacketDelay = MAX_PACKET_DELAY;
    uint32_t sequenceWindow = SEQUENCE_WINDOW;
    uint32_t maxPacketsPerSecond = MAX_PACKETS_PER_SECOND;
    uint32_t suspiciousThreshold = SUSPICIOUS_THRESHOLD;

    // Logging and debugging
    bool enableLogging = true;
    bool enableDebugOutput = true;
    std::string logFilePath = "./protection.log";
};

extern ProtectionConfig g_protectionConfig;