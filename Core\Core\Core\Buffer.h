/**
 * @file Buffer.h
 * @brief Improved RAII-compliant buffer class (legacy compatibility)
 *
 * This is an improved version of the original Buffer class that maintains
 * backward compatibility while adding RAII safety features and bounds checking.
 * For new code, consider using SecureBuffer.h instead.
 *
 * <AUTHOR> Development Team
 * @version 2.0
 */

#pragma once
#include <windows.h>
#include <memory>
#include <stdexcept>

/**
 * @brief Improved buffer class with RAII compliance and basic safety features
 *
 * This class maintains compatibility with existing code while adding:
 * - Exception safety
 * - Automatic memory cleanup
 * - Basic bounds checking
 * - Secure memory wiping
 */
class Buffer {
private:
    std::unique_ptr<char[]> m_packet;  ///< Smart pointer managing the buffer
    size_t m_size;                     ///< Size of the buffer
    bool m_secure_wipe;                ///< Whether to securely wipe on destruction

    /**
     * @brief Securely wipe memory contents
     */
    void secure_wipe() noexcept {
        if (m_packet && m_secure_wipe && m_size > 0) {
            SecureZeroMemory(m_packet.get(), m_size);
        }
    }

public:
    /**
     * @brief Constructor with size
     * @param size Size of buffer to allocate
     * @param secure_wipe Whether to securely wipe memory on destruction
     * @throws std::bad_alloc if allocation fails
     */
    explicit Buffer(int size, bool secure_wipe = true)
        : m_size(static_cast<size_t>(size)), m_secure_wipe(secure_wipe) {
        if (size <= 0) {
            throw std::invalid_argument("Buffer size must be positive");
        }

        m_packet = std::make_unique<char[]>(size);
        // Initialize to zero for security
        memset(m_packet.get(), 0, size);
    }

    /**
     * @brief Copy constructor
     * @param other Buffer to copy from
     */
    Buffer(const Buffer& other)
        : m_size(other.m_size), m_secure_wipe(other.m_secure_wipe) {
        if (other.m_packet && m_size > 0) {
            m_packet = std::make_unique<char[]>(m_size);
            memcpy(m_packet.get(), other.m_packet.get(), m_size);
        }
    }

    /**
     * @brief Move constructor
     * @param other Buffer to move from
     */
    Buffer(Buffer&& other) noexcept
        : m_packet(std::move(other.m_packet)),
          m_size(other.m_size),
          m_secure_wipe(other.m_secure_wipe) {
        other.m_size = 0;
    }

    /**
     * @brief Destructor - automatically cleans up memory
     */
    ~Buffer() noexcept {
        secure_wipe();
        // unique_ptr automatically deallocates
    }

    /**
     * @brief Copy assignment operator
     * @param other Buffer to copy from
     * @return Reference to this buffer
     */
    Buffer& operator=(const Buffer& other) {
        if (this != &other) {
            secure_wipe();

            m_size = other.m_size;
            m_secure_wipe = other.m_secure_wipe;

            if (other.m_packet && m_size > 0) {
                m_packet = std::make_unique<char[]>(m_size);
                memcpy(m_packet.get(), other.m_packet.get(), m_size);
            } else {
                m_packet.reset();
            }
        }
        return *this;
    }

    /**
     * @brief Move assignment operator
     * @param other Buffer to move from
     * @return Reference to this buffer
     */
    Buffer& operator=(Buffer&& other) noexcept {
        if (this != &other) {
            secure_wipe();

            m_packet = std::move(other.m_packet);
            m_size = other.m_size;
            m_secure_wipe = other.m_secure_wipe;

            other.m_size = 0;
        }
        return *this;
    }

    /**
     * @brief Get packet pointer (legacy compatibility)
     * @return Pointer to buffer data
     */
    char* getPacket() noexcept {
        return m_packet.get();
    }

    /**
     * @brief Get const packet pointer
     * @return Const pointer to buffer data
     */
    const char* getPacket() const noexcept {
        return m_packet.get();
    }

    /**
     * @brief Get buffer size
     * @return Size of buffer in bytes
     */
    size_t getSize() const noexcept {
        return m_size;
    }

    /**
     * @brief Check if buffer is valid
     * @return True if buffer is allocated, false otherwise
     */
    bool isValid() const noexcept {
        return m_packet != nullptr && m_size > 0;
    }

    /**
     * @brief Safe access with bounds checking
     * @param index Index to access
     * @return Reference to character at index
     * @throws std::out_of_range if index is out of bounds
     */
    char& at(size_t index) {
        if (index >= m_size) {
            throw std::out_of_range("Buffer index out of range");
        }
        return m_packet[index];
    }

    /**
     * @brief Safe const access with bounds checking
     * @param index Index to access
     * @return Const reference to character at index
     * @throws std::out_of_range if index is out of bounds
     */
    const char& at(size_t index) const {
        if (index >= m_size) {
            throw std::out_of_range("Buffer index out of range");
        }
        return m_packet[index];
    }

    /**
     * @brief Zero out buffer contents
     */
    void zero() noexcept {
        if (m_packet && m_size > 0) {
            memset(m_packet.get(), 0, m_size);
        }
    }

    /**
     * @brief Enable or disable secure wiping
     * @param enable Whether to enable secure wiping
     */
    void setSecureWipe(bool enable) noexcept {
        m_secure_wipe = enable;
    }
};