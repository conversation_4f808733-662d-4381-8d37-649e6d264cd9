 Engine

 Timestamp is 5eaa4904 (Wed Apr 29 23:41:56 2020)

 Preferred load address is 10000000

 Start         Length     Name                   Class
 0001:00000000 0001c998H .text                   CODE
 0001:0001c9a0 00001150H .text$x                 CODE
 0001:0001daf0 000004e8H .text$yc                CODE
 0001:0001dfe0 0000043eH .text$yd                CODE
 0002:00000000 00000288H .idata$5                DATA
 0002:00000288 00000004H .CRT$XCA                DATA
 0002:0000028c 00000004H .CRT$XCL                DATA
 0002:00000290 00000060H .CRT$XCU                DATA
 0002:000002f0 00000004H .CRT$XCZ                DATA
 0002:000002f4 00000004H .CRT$XIA                DATA
 0002:000002f8 00000004H .CRT$XIAA               DATA
 0002:000002fc 00000004H .CRT$XIZ                DATA
 0002:00000300 000063e0H .rdata                  DATA
 0002:000066e0 00000039H .rdata$debug            DATA
 0002:0000671c 000005a4H .rdata$r                DATA
 0002:00006cc0 00000114H .rdata$sxdata           DATA
 0002:00006dd4 00000004H .rtc$IAA                DATA
 0002:00006dd8 00000004H .rtc$IZZ                DATA
 0002:00006ddc 00000004H .rtc$TAA                DATA
 0002:00006de0 00000004H .rtc$TZZ                DATA
 0002:00006de8 0000144cH .xdata$x                DATA
 0002:00008234 00000078H .idata$2                DATA
 0002:000082ac 00000014H .idata$3                DATA
 0002:000082c0 00000288H .idata$4                DATA
 0002:00008548 000012d0H .idata$6                DATA
 0002:00009820 00006208H .edata                  DATA
 0003:00000000 00001aa0H .data                   DATA
 0003:00001aa0 00001544H .bss                    DATA

  Address         Publics by Value              Rva+Base       Lib:Object

 0000:00000000       __except_list              00000000     <absolute>
 0000:00000045       ___safe_se_handler_count   00000045     <absolute>
 0000:00009876       __ldused                   00009876     <absolute>
 0000:00009876       __fltused                  00009876     <absolute>
 0000:00000000       ___ImageBase               10000000     <linker-defined>
 0001:00000000       ??1bad_alloc@std@@UAE@XZ   10001000 f i base64.obj
 0001:00000010       ??_Gbad_alloc@std@@UAEPAXI@Z 10001010 f i base64.obj
 0001:00000010       ??_Ebad_alloc@std@@UAEPAXI@Z 10001010 f i  CIL library: CIL module
 0001:00000040       ?compare@?$char_traits@D@std@@SAHPBD0I@Z 10001040 f i base64.obj
 0001:000000c0       ?base64_decode@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@@Z 100010c0 f   base64.obj
 0001:000003b0       ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@PBD@Z 100013b0 f i base64.obj
 0001:000003f0       ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ 100013f0 f i base64.obj
 0001:00000420       ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@$$QAV12@@Z 10001420 f i base64.obj
 0001:00000490       ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@ID@Z 10001490 f i base64.obj
 0001:00000560       ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIPBDII@Z 10001560 f i base64.obj
 0001:00000600       ?_Myptr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ 10001600 f i base64.obj
 0001:00000610       ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@PBDI@Z 10001610 f i base64.obj
 0001:000006e0       ?_Grow@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE_NI_N@Z 100016e0 f i base64.obj
 0001:00000790       ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@ABV12@II@Z 10001790 f i base64.obj
 0001:00000870       ?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXII@Z 10001870 f i base64.obj
 0001:000009e0       ?erase@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@II@Z 100019e0 f i base64.obj
 0001:00000a60       ?allocate@?$allocator@D@std@@QAEPADI@Z 10001a60 f i base64.obj
 0001:00000ab0       ??0bad_alloc@std@@QAE@ABV01@@Z 10001ab0 f i base64.obj
 0001:00000ad0       ??3@YAXPAX0@Z              10001ad0 f i Buff.obj
 0001:00000ae0       ?hash_code@type_info@@QBEIXZ 10001ae0 f i Buff.obj
 0001:00000b40       ??0InterfaceManager@@QAE@XZ 10001b40 f i Buff.obj
 0001:00000c30       ?_GetInstance@InterfaceManager@@SAPAV1@XZ 10001c30 f i Buff.obj
 0001:00000cb0       ??1?$map@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@@std@@QAE@XZ 10001cb0 f i Buff.obj
 0001:00000d10       ?SetBuff@@YIHPAX0HH_JDHHHHHH@Z 10001d10 f   Buff.obj
 0001:00000db0       ??A?$map@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@@std@@QAEAAPAXABI@Z 10001db0 f i Buff.obj
 0001:00000e30       ?clear@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAEXXZ 10001e30 f i Buff.obj
 0001:00000e80       ??0?$Interface@VITools@@@@QAE@XZ 10001e80 f i Buff.obj
 0001:00000f00       ?_Erase@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@@Z 10001f00 f i Buff.obj
 0001:00000f40       ??1?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE@XZ 10001f40 f i Packets.obj
 0001:00000f40       ??1?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAE@XZ 10001f40 f i Buff.obj
 0001:00000f50       ?erase@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@0@Z 10001f50 f i Buff.obj
 0001:00000ff0       ?_Eqrange@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@IBE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABI@Z 10001ff0 f i Buff.obj
 0001:00001080       ?erase@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@@Z 10002080 f i Buff.obj
 0001:000012e0       ?_Lrotate@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@@Z 100022e0 f i Buff.obj
 0001:00001340       ?_Rrotate@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@@Z 10002340 f i Buff.obj
 0001:000013a0       ?_Max@?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@SAPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@PAU342@@Z 100023a0 f i Buff.obj
 0001:000013c0       ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 100023c0 f i Buff.obj
 0001:00001410       ?_Insert@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@PAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@@Z 10002410 f i Buff.obj
 0001:00001530       ?_Linsert@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAE?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@std@@_N@2@PAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@_N@Z 10002530 f i Buff.obj
 0001:00001600       ?_Insert@?$_Tree@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@IAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@2@_NPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@2@1@Z 10002600 f i Buff.obj
 0001:00001820       ??F?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10002820 f i Buff.obj
 0001:00001880       ??$_Buynode@U?$pair@$$CBIPAX@std@@@?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@1@$$QAU?$pair@$$CBIPAX@1@@Z 10002880 f i Buff.obj
 0001:000018f0       ??$_Distance2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@std@@I@std@@YAXV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@$0A@@std@@@std@@@0@0AAIUbidirectional_iterator_tag@0@@Z 100028f0 f i Buff.obj
 0001:00001960       ?Display@IChatbox@@QAEXV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 10002960 f   Chatbox.obj
 0001:00001a00       ?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 10002a00 f   Chatbox.obj
 0001:00001f10       ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z 10002f10 f i Chatbox.obj
 0001:00001f40       ?push_front@?$list@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 10002f40 f i Chatbox.obj
 0001:00001f80       ?pop_back@?$list@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXXZ 10002f80 f i Chatbox.obj
 0001:00001fd0       ?clear@?$list@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXXZ 10002fd0 f i Chatbox.obj
 0001:00002030       ?Create@?$Interface@VIChatbox@@@@SAXXZ 10003030 f i Chatbox.obj
 0001:00002140       ??0?$Interface@VIChatbox@@@@QAE@XZ 10003140 f i Chatbox.obj
 0001:000021c0       ??0?$Interface@VIGraphics@@@@QAE@XZ 100031c0 f i Chatbox.obj
 0001:00002240       ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ 10003240 f i Chatbox.obj
 0001:00002280       ?_Buynode@?$_List_val@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEPAU_Node@?$_List_nod@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@PAU342@0ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 10003280 f i Chatbox.obj
 0001:00002350       ?_Destroy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@IAEXPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0@Z 10003350 f i Chatbox.obj
 0001:00002390       ??$?8DU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBD@Z 10003390 f i Chatbox.obj
 0001:000023f0       ??$?9DU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@0@Z 100033f0 f i Chatbox.obj
 0001:00002440       ??1locale@std@@QAE@XZ      10003440 f i Engine.obj
 0001:00002470       ?InitializeADVAPI32@@YAXXZ 10003470 f   Engine.obj
 0001:00005f50       ___E__0__@0                10006f50 f   Engine.obj
 0001:00005f60       ___E__1__@0                10006f60 f   Engine.obj
 0001:00005f70       ___E__2__@0                10006f70 f   Engine.obj
 0001:00005f80       ___E__3__@0                10006f80 f   Engine.obj
 0001:00005f90       ___E__4__@0                10006f90 f   Engine.obj
 0001:00005fa0       ___E__5__@0                10006fa0 f   Engine.obj
 0001:00005fb0       ___E__6__@0                10006fb0 f   Engine.obj
 0001:00005fc0       ___E__7__@0                10006fc0 f   Engine.obj
 0001:00005fd0       ___E__8__@0                10006fd0 f   Engine.obj
 0001:00005fe0       ___E__9__@0                10006fe0 f   Engine.obj
 0001:00005ff0       ___E__10__@0               10006ff0 f   Engine.obj
 0001:00006000       ___E__11__@0               10007000 f   Engine.obj
 0001:00006010       ___E__12__@0               10007010 f   Engine.obj
 0001:00006020       ___E__13__@0               10007020 f   Engine.obj
 0001:00006030       ___E__14__@0               10007030 f   Engine.obj
 0001:00006040       ___E__15__@0               10007040 f   Engine.obj
 0001:00006050       ___E__16__@0               10007050 f   Engine.obj
 0001:00006060       ___E__17__@0               10007060 f   Engine.obj
 0001:00006070       ___E__18__@0               10007070 f   Engine.obj
 0001:00006080       ___E__19__@0               10007080 f   Engine.obj
 0001:00006090       ___E__20__@0               10007090 f   Engine.obj
 0001:000060a0       ___E__21__@0               100070a0 f   Engine.obj
 0001:000060b0       ___E__22__@0               100070b0 f   Engine.obj
 0001:000060c0       ___E__23__@0               100070c0 f   Engine.obj
 0001:000060d0       ___E__24__@0               100070d0 f   Engine.obj
 0001:000060e0       ___E__25__@0               100070e0 f   Engine.obj
 0001:000060f0       ___E__26__@0               100070f0 f   Engine.obj
 0001:00006100       ___E__27__@0               10007100 f   Engine.obj
 0001:00006110       ___E__28__@0               10007110 f   Engine.obj
 0001:00006120       ___E__29__@0               10007120 f   Engine.obj
 0001:00006130       ___E__30__@0               10007130 f   Engine.obj
 0001:00006140       ___E__31__@0               10007140 f   Engine.obj
 0001:00006150       ___E__32__@0               10007150 f   Engine.obj
 0001:00006160       ___E__33__@0               10007160 f   Engine.obj
 0001:00006170       ___E__34__@0               10007170 f   Engine.obj
 0001:00006180       ___E__35__@0               10007180 f   Engine.obj
 0001:00006190       ___E__36__@0               10007190 f   Engine.obj
 0001:000061a0       ___E__37__@0               100071a0 f   Engine.obj
 0001:000061b0       ___E__38__@0               100071b0 f   Engine.obj
 0001:000061c0       ___E__39__@0               100071c0 f   Engine.obj
 0001:000061d0       ___E__40__@0               100071d0 f   Engine.obj
 0001:000061e0       ___E__41__@0               100071e0 f   Engine.obj
 0001:000061f0       ___E__42__@0               100071f0 f   Engine.obj
 0001:00006200       ___E__43__@0               10007200 f   Engine.obj
 0001:00006210       ___E__44__@0               10007210 f   Engine.obj
 0001:00006220       ___E__45__@0               10007220 f   Engine.obj
 0001:00006230       ___E__46__@0               10007230 f   Engine.obj
 0001:00006240       ___E__47__@0               10007240 f   Engine.obj
 0001:00006250       ___E__48__@0               10007250 f   Engine.obj
 0001:00006260       ___E__49__@0               10007260 f   Engine.obj
 0001:00006270       ___E__50__@0               10007270 f   Engine.obj
 0001:00006280       ___E__51__@0               10007280 f   Engine.obj
 0001:00006290       ___E__52__@0               10007290 f   Engine.obj
 0001:000062a0       ___E__53__@0               100072a0 f   Engine.obj
 0001:000062b0       ___E__54__@0               100072b0 f   Engine.obj
 0001:000062c0       ___E__55__@0               100072c0 f   Engine.obj
 0001:000062d0       ___E__56__@0               100072d0 f   Engine.obj
 0001:000062e0       ___E__57__@0               100072e0 f   Engine.obj
 0001:000062f0       ___E__58__@0               100072f0 f   Engine.obj
 0001:00006300       ___E__59__@0               10007300 f   Engine.obj
 0001:00006310       ___E__60__@0               10007310 f   Engine.obj
 0001:00006320       ___E__61__@0               10007320 f   Engine.obj
 0001:00006330       ___E__62__@0               10007330 f   Engine.obj
 0001:00006340       ___E__63__@0               10007340 f   Engine.obj
 0001:00006350       ___E__64__@0               10007350 f   Engine.obj
 0001:00006360       ___E__65__@0               10007360 f   Engine.obj
 0001:00006370       ___E__66__@0               10007370 f   Engine.obj
 0001:00006380       ___E__67__@0               10007380 f   Engine.obj
 0001:00006390       ___E__68__@0               10007390 f   Engine.obj
 0001:000063a0       ___E__69__@0               100073a0 f   Engine.obj
 0001:000063b0       ___E__70__@0               100073b0 f   Engine.obj
 0001:000063c0       ___E__71__@0               100073c0 f   Engine.obj
 0001:000063d0       ___E__72__@0               100073d0 f   Engine.obj
 0001:000063e0       ___E__73__@0               100073e0 f   Engine.obj
 0001:000063f0       ___E__74__@0               100073f0 f   Engine.obj
 0001:00006400       ___E__75__@0               10007400 f   Engine.obj
 0001:00006410       ___E__76__@0               10007410 f   Engine.obj
 0001:00006420       ___E__77__@0               10007420 f   Engine.obj
 0001:00006430       ___E__78__@0               10007430 f   Engine.obj
 0001:00006440       ___E__79__@0               10007440 f   Engine.obj
 0001:00006450       ___E__80__@0               10007450 f   Engine.obj
 0001:00006460       ___E__81__@0               10007460 f   Engine.obj
 0001:00006470       ___E__82__@0               10007470 f   Engine.obj
 0001:00006480       ___E__83__@0               10007480 f   Engine.obj
 0001:00006490       ___E__84__@0               10007490 f   Engine.obj
 0001:000064a0       ___E__85__@0               100074a0 f   Engine.obj
 0001:000064b0       ___E__86__@0               100074b0 f   Engine.obj
 0001:000064c0       ___E__87__@0               100074c0 f   Engine.obj
 0001:000064d0       ___E__88__@0               100074d0 f   Engine.obj
 0001:000064e0       ___E__89__@0               100074e0 f   Engine.obj
 0001:000064f0       ___E__90__@0               100074f0 f   Engine.obj
 0001:00006500       ___E__91__@0               10007500 f   Engine.obj
 0001:00006510       ___E__92__@0               10007510 f   Engine.obj
 0001:00006520       ___E__93__@0               10007520 f   Engine.obj
 0001:00006530       ___E__94__@0               10007530 f   Engine.obj
 0001:00006540       ___E__95__@0               10007540 f   Engine.obj
 0001:00006550       ___E__96__@0               10007550 f   Engine.obj
 0001:00006560       ___E__97__@0               10007560 f   Engine.obj
 0001:00006570       ___E__98__@0               10007570 f   Engine.obj
 0001:00006580       ___E__99__@0               10007580 f   Engine.obj
 0001:00006590       ___E__100__@0              10007590 f   Engine.obj
 0001:000065a0       ___E__101__@0              100075a0 f   Engine.obj
 0001:000065b0       ___E__102__@0              100075b0 f   Engine.obj
 0001:000065c0       ___E__103__@0              100075c0 f   Engine.obj
 0001:000065d0       ___E__104__@0              100075d0 f   Engine.obj
 0001:000065e0       ___E__105__@0              100075e0 f   Engine.obj
 0001:000065f0       ___E__106__@0              100075f0 f   Engine.obj
 0001:00006600       ___E__107__@0              10007600 f   Engine.obj
 0001:00006610       ___E__108__@0              10007610 f   Engine.obj
 0001:00006620       ___E__109__@0              10007620 f   Engine.obj
 0001:00006630       ___E__110__@0              10007630 f   Engine.obj
 0001:00006640       ___E__111__@0              10007640 f   Engine.obj
 0001:00006650       ___E__112__@0              10007650 f   Engine.obj
 0001:00006660       ___E__113__@0              10007660 f   Engine.obj
 0001:00006670       ___E__114__@0              10007670 f   Engine.obj
 0001:00006680       ___E__115__@0              10007680 f   Engine.obj
 0001:00006690       ___E__116__@0              10007690 f   Engine.obj
 0001:000066a0       ___E__117__@0              100076a0 f   Engine.obj
 0001:000066b0       ___E__118__@0              100076b0 f   Engine.obj
 0001:000066c0       ___E__119__@0              100076c0 f   Engine.obj
 0001:000066d0       ___E__120__@0              100076d0 f   Engine.obj
 0001:000066e0       ___E__121__@0              100076e0 f   Engine.obj
 0001:000066f0       ___E__122__@0              100076f0 f   Engine.obj
 0001:00006700       ___E__123__@0              10007700 f   Engine.obj
 0001:00006710       ___E__124__@0              10007710 f   Engine.obj
 0001:00006720       ___E__125__@0              10007720 f   Engine.obj
 0001:00006730       ___E__126__@0              10007730 f   Engine.obj
 0001:00006740       ___E__127__@0              10007740 f   Engine.obj
 0001:00006750       ___E__128__@0              10007750 f   Engine.obj
 0001:00006760       ___E__129__@0              10007760 f   Engine.obj
 0001:00006770       ___E__130__@0              10007770 f   Engine.obj
 0001:00006780       ___E__131__@0              10007780 f   Engine.obj
 0001:00006790       ___E__132__@0              10007790 f   Engine.obj
 0001:000067a0       ___E__133__@0              100077a0 f   Engine.obj
 0001:000067b0       ___E__134__@0              100077b0 f   Engine.obj
 0001:000067c0       ___E__135__@0              100077c0 f   Engine.obj
 0001:000067d0       ___E__136__@0              100077d0 f   Engine.obj
 0001:000067e0       ___E__137__@0              100077e0 f   Engine.obj
 0001:000067f0       ___E__138__@0              100077f0 f   Engine.obj
 0001:00006800       ___E__139__@0              10007800 f   Engine.obj
 0001:00006810       ___E__140__@0              10007810 f   Engine.obj
 0001:00006820       ___E__141__@0              10007820 f   Engine.obj
 0001:00006830       ___E__142__@0              10007830 f   Engine.obj
 0001:00006840       ___E__143__@0              10007840 f   Engine.obj
 0001:00006850       ___E__144__@0              10007850 f   Engine.obj
 0001:00006860       ___E__145__@0              10007860 f   Engine.obj
 0001:00006870       ___E__146__@0              10007870 f   Engine.obj
 0001:00006880       ___E__147__@0              10007880 f   Engine.obj
 0001:00006890       ___E__148__@0              10007890 f   Engine.obj
 0001:000068a0       ___E__149__@0              100078a0 f   Engine.obj
 0001:000068b0       ___E__150__@0              100078b0 f   Engine.obj
 0001:000068c0       ___E__151__@0              100078c0 f   Engine.obj
 0001:000068d0       ___E__152__@0              100078d0 f   Engine.obj
 0001:000068e0       ___E__153__@0              100078e0 f   Engine.obj
 0001:000068f0       ___E__154__@0              100078f0 f   Engine.obj
 0001:00006900       ___E__155__@0              10007900 f   Engine.obj
 0001:00006910       ___E__156__@0              10007910 f   Engine.obj
 0001:00006920       ___E__157__@0              10007920 f   Engine.obj
 0001:00006930       ___E__158__@0              10007930 f   Engine.obj
 0001:00006940       ___E__159__@0              10007940 f   Engine.obj
 0001:00006950       ___E__160__@0              10007950 f   Engine.obj
 0001:00006960       ___E__161__@0              10007960 f   Engine.obj
 0001:00006970       ___E__162__@0              10007970 f   Engine.obj
 0001:00006980       ___E__163__@0              10007980 f   Engine.obj
 0001:00006990       ___E__164__@0              10007990 f   Engine.obj
 0001:000069a0       ___E__165__@0              100079a0 f   Engine.obj
 0001:000069b0       ___E__166__@0              100079b0 f   Engine.obj
 0001:000069c0       ___E__167__@0              100079c0 f   Engine.obj
 0001:000069d0       ___E__168__@0              100079d0 f   Engine.obj
 0001:000069e0       ___E__169__@0              100079e0 f   Engine.obj
 0001:000069f0       ___E__170__@0              100079f0 f   Engine.obj
 0001:00006a00       ___E__171__@0              10007a00 f   Engine.obj
 0001:00006a10       ___E__172__@0              10007a10 f   Engine.obj
 0001:00006a20       ___E__173__@0              10007a20 f   Engine.obj
 0001:00006a30       ___E__174__@0              10007a30 f   Engine.obj
 0001:00006a40       ___E__175__@0              10007a40 f   Engine.obj
 0001:00006a50       ___E__176__@0              10007a50 f   Engine.obj
 0001:00006a60       ___E__177__@0              10007a60 f   Engine.obj
 0001:00006a70       ___E__178__@0              10007a70 f   Engine.obj
 0001:00006a80       ___E__179__@0              10007a80 f   Engine.obj
 0001:00006a90       ___E__180__@0              10007a90 f   Engine.obj
 0001:00006aa0       ___E__181__@0              10007aa0 f   Engine.obj
 0001:00006ab0       ___E__182__@0              10007ab0 f   Engine.obj
 0001:00006ac0       ___E__183__@0              10007ac0 f   Engine.obj
 0001:00006ad0       ___E__184__@0              10007ad0 f   Engine.obj
 0001:00006ae0       ___E__185__@0              10007ae0 f   Engine.obj
 0001:00006af0       ___E__186__@0              10007af0 f   Engine.obj
 0001:00006b00       ___E__187__@0              10007b00 f   Engine.obj
 0001:00006b10       ___E__188__@0              10007b10 f   Engine.obj
 0001:00006b20       ___E__189__@0              10007b20 f   Engine.obj
 0001:00006b30       ___E__190__@0              10007b30 f   Engine.obj
 0001:00006b40       ___E__191__@0              10007b40 f   Engine.obj
 0001:00006b50       ___E__192__@0              10007b50 f   Engine.obj
 0001:00006b60       ___E__193__@0              10007b60 f   Engine.obj
 0001:00006b70       ___E__194__@0              10007b70 f   Engine.obj
 0001:00006b80       ___E__195__@0              10007b80 f   Engine.obj
 0001:00006b90       ___E__196__@0              10007b90 f   Engine.obj
 0001:00006ba0       ___E__197__@0              10007ba0 f   Engine.obj
 0001:00006bb0       ___E__198__@0              10007bb0 f   Engine.obj
 0001:00006bc0       ___E__199__@0              10007bc0 f   Engine.obj
 0001:00006bd0       ___E__200__@0              10007bd0 f   Engine.obj
 0001:00006be0       ___E__201__@0              10007be0 f   Engine.obj
 0001:00006bf0       ___E__202__@0              10007bf0 f   Engine.obj
 0001:00006c00       ___E__203__@0              10007c00 f   Engine.obj
 0001:00006c10       ___E__204__@0              10007c10 f   Engine.obj
 0001:00006c20       ___E__205__@0              10007c20 f   Engine.obj
 0001:00006c30       ___E__206__@0              10007c30 f   Engine.obj
 0001:00006c40       ___E__207__@0              10007c40 f   Engine.obj
 0001:00006c50       ___E__208__@0              10007c50 f   Engine.obj
 0001:00006c60       ___E__209__@0              10007c60 f   Engine.obj
 0001:00006c70       ___E__210__@0              10007c70 f   Engine.obj
 0001:00006c80       ___E__211__@0              10007c80 f   Engine.obj
 0001:00006c90       ___E__212__@0              10007c90 f   Engine.obj
 0001:00006ca0       ___E__213__@0              10007ca0 f   Engine.obj
 0001:00006cb0       ___E__214__@0              10007cb0 f   Engine.obj
 0001:00006cc0       ___E__215__@0              10007cc0 f   Engine.obj
 0001:00006cd0       ___E__216__@0              10007cd0 f   Engine.obj
 0001:00006ce0       ___E__217__@0              10007ce0 f   Engine.obj
 0001:00006cf0       ___E__218__@0              10007cf0 f   Engine.obj
 0001:00006d00       ___E__219__@0              10007d00 f   Engine.obj
 0001:00006d10       ___E__220__@0              10007d10 f   Engine.obj
 0001:00006d20       ___E__221__@0              10007d20 f   Engine.obj
 0001:00006d30       ___E__222__@0              10007d30 f   Engine.obj
 0001:00006d40       ___E__223__@0              10007d40 f   Engine.obj
 0001:00006d50       ___E__224__@0              10007d50 f   Engine.obj
 0001:00006d60       ___E__225__@0              10007d60 f   Engine.obj
 0001:00006d70       ___E__226__@0              10007d70 f   Engine.obj
 0001:00006d80       ___E__227__@0              10007d80 f   Engine.obj
 0001:00006d90       ___E__228__@0              10007d90 f   Engine.obj
 0001:00006da0       ___E__229__@0              10007da0 f   Engine.obj
 0001:00006db0       ___E__230__@0              10007db0 f   Engine.obj
 0001:00006dc0       ___E__231__@0              10007dc0 f   Engine.obj
 0001:00006dd0       ___E__232__@0              10007dd0 f   Engine.obj
 0001:00006de0       ___E__233__@0              10007de0 f   Engine.obj
 0001:00006df0       ___E__234__@0              10007df0 f   Engine.obj
 0001:00006e00       ___E__235__@0              10007e00 f   Engine.obj
 0001:00006e10       ___E__236__@0              10007e10 f   Engine.obj
 0001:00006e20       ___E__237__@0              10007e20 f   Engine.obj
 0001:00006e30       ___E__238__@0              10007e30 f   Engine.obj
 0001:00006e40       ___E__239__@0              10007e40 f   Engine.obj
 0001:00006e50       ___E__240__@0              10007e50 f   Engine.obj
 0001:00006e60       ___E__241__@0              10007e60 f   Engine.obj
 0001:00006e70       ___E__242__@0              10007e70 f   Engine.obj
 0001:00006e80       ___E__243__@0              10007e80 f   Engine.obj
 0001:00006e90       ___E__244__@0              10007e90 f   Engine.obj
 0001:00006ea0       ___E__245__@0              10007ea0 f   Engine.obj
 0001:00006eb0       ___E__246__@0              10007eb0 f   Engine.obj
 0001:00006ec0       ___E__247__@0              10007ec0 f   Engine.obj
 0001:00006ed0       ___E__248__@0              10007ed0 f   Engine.obj
 0001:00006ee0       ___E__249__@0              10007ee0 f   Engine.obj
 0001:00006ef0       ___E__250__@0              10007ef0 f   Engine.obj
 0001:00006f00       ___E__251__@0              10007f00 f   Engine.obj
 0001:00006f10       ___E__252__@0              10007f10 f   Engine.obj
 0001:00006f20       ___E__253__@0              10007f20 f   Engine.obj
 0001:00006f30       ___E__254__@0              10007f30 f   Engine.obj
 0001:00006f40       ___E__255__@0              10007f40 f   Engine.obj
 0001:00006f50       ___E__256__@0              10007f50 f   Engine.obj
 0001:00006f60       ___E__257__@0              10007f60 f   Engine.obj
 0001:00006f70       ___E__258__@0              10007f70 f   Engine.obj
 0001:00006f80       ___E__259__@0              10007f80 f   Engine.obj
 0001:00006f90       ___E__260__@0              10007f90 f   Engine.obj
 0001:00006fa0       ___E__261__@0              10007fa0 f   Engine.obj
 0001:00006fb0       ___E__262__@0              10007fb0 f   Engine.obj
 0001:00006fc0       ___E__263__@0              10007fc0 f   Engine.obj
 0001:00006fd0       ___E__264__@0              10007fd0 f   Engine.obj
 0001:00006fe0       ___E__265__@0              10007fe0 f   Engine.obj
 0001:00006ff0       ___E__266__@0              10007ff0 f   Engine.obj
 0001:00007000       ___E__267__@0              10008000 f   Engine.obj
 0001:00007010       ___E__268__@0              10008010 f   Engine.obj
 0001:00007020       ___E__269__@0              10008020 f   Engine.obj
 0001:00007030       ___E__270__@0              10008030 f   Engine.obj
 0001:00007040       ___E__271__@0              10008040 f   Engine.obj
 0001:00007050       ___E__272__@0              10008050 f   Engine.obj
 0001:00007060       ___E__273__@0              10008060 f   Engine.obj
 0001:00007070       ___E__274__@0              10008070 f   Engine.obj
 0001:00007080       ___E__275__@0              10008080 f   Engine.obj
 0001:00007090       ___E__276__@0              10008090 f   Engine.obj
 0001:000070a0       ___E__277__@0              100080a0 f   Engine.obj
 0001:000070b0       ___E__278__@0              100080b0 f   Engine.obj
 0001:000070c0       ___E__279__@0              100080c0 f   Engine.obj
 0001:000070d0       ___E__280__@0              100080d0 f   Engine.obj
 0001:000070e0       ___E__281__@0              100080e0 f   Engine.obj
 0001:000070f0       ___E__282__@0              100080f0 f   Engine.obj
 0001:00007100       ___E__283__@0              10008100 f   Engine.obj
 0001:00007110       ___E__284__@0              10008110 f   Engine.obj
 0001:00007120       ___E__285__@0              10008120 f   Engine.obj
 0001:00007130       ___E__286__@0              10008130 f   Engine.obj
 0001:00007140       ___E__287__@0              10008140 f   Engine.obj
 0001:00007150       ___E__288__@0              10008150 f   Engine.obj
 0001:00007160       ___E__289__@0              10008160 f   Engine.obj
 0001:00007170       ___E__290__@0              10008170 f   Engine.obj
 0001:00007180       ___E__291__@0              10008180 f   Engine.obj
 0001:00007190       ___E__292__@0              10008190 f   Engine.obj
 0001:000071a0       ___E__293__@0              100081a0 f   Engine.obj
 0001:000071b0       ___E__294__@0              100081b0 f   Engine.obj
 0001:000071c0       ___E__295__@0              100081c0 f   Engine.obj
 0001:000071d0       ___E__296__@0              100081d0 f   Engine.obj
 0001:000071e0       ___E__297__@0              100081e0 f   Engine.obj
 0001:000071f0       ___E__298__@0              100081f0 f   Engine.obj
 0001:00007200       ___E__299__@0              10008200 f   Engine.obj
 0001:00007210       ___E__300__@0              10008210 f   Engine.obj
 0001:00007220       ___E__301__@0              10008220 f   Engine.obj
 0001:00007230       ___E__302__@0              10008230 f   Engine.obj
 0001:00007240       ___E__303__@0              10008240 f   Engine.obj
 0001:00007250       ___E__304__@0              10008250 f   Engine.obj
 0001:00007260       ___E__305__@0              10008260 f   Engine.obj
 0001:00007270       ___E__306__@0              10008270 f   Engine.obj
 0001:00007280       ___E__307__@0              10008280 f   Engine.obj
 0001:00007290       ___E__308__@0              10008290 f   Engine.obj
 0001:000072a0       ___E__309__@0              100082a0 f   Engine.obj
 0001:000072b0       ___E__310__@0              100082b0 f   Engine.obj
 0001:000072c0       ___E__311__@0              100082c0 f   Engine.obj
 0001:000072d0       ___E__312__@0              100082d0 f   Engine.obj
 0001:000072e0       ___E__313__@0              100082e0 f   Engine.obj
 0001:000072f0       ___E__314__@0              100082f0 f   Engine.obj
 0001:00007300       ___E__315__@0              10008300 f   Engine.obj
 0001:00007310       ___E__316__@0              10008310 f   Engine.obj
 0001:00007320       ___E__317__@0              10008320 f   Engine.obj
 0001:00007330       ___E__318__@0              10008330 f   Engine.obj
 0001:00007340       ___E__319__@0              10008340 f   Engine.obj
 0001:00007350       ___E__320__@0              10008350 f   Engine.obj
 0001:00007360       ___E__321__@0              10008360 f   Engine.obj
 0001:00007370       ___E__322__@0              10008370 f   Engine.obj
 0001:00007380       ___E__323__@0              10008380 f   Engine.obj
 0001:00007390       ___E__324__@0              10008390 f   Engine.obj
 0001:000073a0       ___E__325__@0              100083a0 f   Engine.obj
 0001:000073b0       ___E__326__@0              100083b0 f   Engine.obj
 0001:000073c0       ___E__327__@0              100083c0 f   Engine.obj
 0001:000073d0       ___E__328__@0              100083d0 f   Engine.obj
 0001:000073e0       ___E__329__@0              100083e0 f   Engine.obj
 0001:000073f0       ___E__330__@0              100083f0 f   Engine.obj
 0001:00007400       ___E__331__@0              10008400 f   Engine.obj
 0001:00007410       ___E__332__@0              10008410 f   Engine.obj
 0001:00007420       ___E__333__@0              10008420 f   Engine.obj
 0001:00007430       ___E__334__@0              10008430 f   Engine.obj
 0001:00007440       ___E__335__@0              10008440 f   Engine.obj
 0001:00007450       ___E__336__@0              10008450 f   Engine.obj
 0001:00007460       ___E__337__@0              10008460 f   Engine.obj
 0001:00007470       ___E__338__@0              10008470 f   Engine.obj
 0001:00007480       ___E__339__@0              10008480 f   Engine.obj
 0001:00007490       ___E__340__@0              10008490 f   Engine.obj
 0001:000074a0       ___E__341__@0              100084a0 f   Engine.obj
 0001:000074b0       ___E__342__@0              100084b0 f   Engine.obj
 0001:000074c0       ___E__343__@0              100084c0 f   Engine.obj
 0001:000074d0       ___E__344__@0              100084d0 f   Engine.obj
 0001:000074e0       ___E__345__@0              100084e0 f   Engine.obj
 0001:000074f0       ___E__346__@0              100084f0 f   Engine.obj
 0001:00007500       ___E__347__@0              10008500 f   Engine.obj
 0001:00007510       ___E__348__@0              10008510 f   Engine.obj
 0001:00007520       ___E__349__@0              10008520 f   Engine.obj
 0001:00007530       ___E__350__@0              10008530 f   Engine.obj
 0001:00007540       ___E__351__@0              10008540 f   Engine.obj
 0001:00007550       ___E__352__@0              10008550 f   Engine.obj
 0001:00007560       ___E__353__@0              10008560 f   Engine.obj
 0001:00007570       ___E__354__@0              10008570 f   Engine.obj
 0001:00007580       ___E__355__@0              10008580 f   Engine.obj
 0001:00007590       ___E__356__@0              10008590 f   Engine.obj
 0001:000075a0       ___E__357__@0              100085a0 f   Engine.obj
 0001:000075b0       ___E__358__@0              100085b0 f   Engine.obj
 0001:000075c0       ___E__359__@0              100085c0 f   Engine.obj
 0001:000075d0       ___E__360__@0              100085d0 f   Engine.obj
 0001:000075e0       ___E__361__@0              100085e0 f   Engine.obj
 0001:000075f0       ___E__362__@0              100085f0 f   Engine.obj
 0001:00007600       ___E__363__@0              10008600 f   Engine.obj
 0001:00007610       ___E__364__@0              10008610 f   Engine.obj
 0001:00007620       ___E__365__@0              10008620 f   Engine.obj
 0001:00007630       ___E__366__@0              10008630 f   Engine.obj
 0001:00007640       ___E__367__@0              10008640 f   Engine.obj
 0001:00007650       ___E__368__@0              10008650 f   Engine.obj
 0001:00007660       ___E__369__@0              10008660 f   Engine.obj
 0001:00007670       ___E__370__@0              10008670 f   Engine.obj
 0001:00007680       ___E__371__@0              10008680 f   Engine.obj
 0001:00007690       ___E__372__@0              10008690 f   Engine.obj
 0001:000076a0       ___E__373__@0              100086a0 f   Engine.obj
 0001:000076b0       ___E__374__@0              100086b0 f   Engine.obj
 0001:000076c0       ___E__375__@0              100086c0 f   Engine.obj
 0001:000076d0       ___E__376__@0              100086d0 f   Engine.obj
 0001:000076e0       ___E__377__@0              100086e0 f   Engine.obj
 0001:000076f0       ___E__378__@0              100086f0 f   Engine.obj
 0001:00007700       ___E__379__@0              10008700 f   Engine.obj
 0001:00007710       ___E__380__@0              10008710 f   Engine.obj
 0001:00007720       ___E__381__@0              10008720 f   Engine.obj
 0001:00007730       ___E__382__@0              10008730 f   Engine.obj
 0001:00007740       ___E__383__@0              10008740 f   Engine.obj
 0001:00007750       ___E__384__@0              10008750 f   Engine.obj
 0001:00007760       ___E__385__@0              10008760 f   Engine.obj
 0001:00007770       ___E__386__@0              10008770 f   Engine.obj
 0001:00007780       ___E__387__@0              10008780 f   Engine.obj
 0001:00007790       ___E__388__@0              10008790 f   Engine.obj
 0001:000077a0       ___E__389__@0              100087a0 f   Engine.obj
 0001:000077b0       ___E__390__@0              100087b0 f   Engine.obj
 0001:000077c0       ___E__391__@0              100087c0 f   Engine.obj
 0001:000077d0       ___E__392__@0              100087d0 f   Engine.obj
 0001:000077e0       ___E__393__@0              100087e0 f   Engine.obj
 0001:000077f0       ___E__394__@0              100087f0 f   Engine.obj
 0001:00007800       ___E__395__@0              10008800 f   Engine.obj
 0001:00007810       ___E__396__@0              10008810 f   Engine.obj
 0001:00007820       ___E__397__@0              10008820 f   Engine.obj
 0001:00007830       ___E__398__@0              10008830 f   Engine.obj
 0001:00007840       ___E__399__@0              10008840 f   Engine.obj
 0001:00007850       ___E__400__@0              10008850 f   Engine.obj
 0001:00007860       ___E__401__@0              10008860 f   Engine.obj
 0001:00007870       ___E__402__@0              10008870 f   Engine.obj
 0001:00007880       ___E__403__@0              10008880 f   Engine.obj
 0001:00007890       ___E__404__@0              10008890 f   Engine.obj
 0001:000078a0       ___E__405__@0              100088a0 f   Engine.obj
 0001:000078b0       ___E__406__@0              100088b0 f   Engine.obj
 0001:000078c0       ___E__407__@0              100088c0 f   Engine.obj
 0001:000078d0       ___E__408__@0              100088d0 f   Engine.obj
 0001:000078e0       ___E__409__@0              100088e0 f   Engine.obj
 0001:000078f0       ___E__410__@0              100088f0 f   Engine.obj
 0001:00007900       ___E__411__@0              10008900 f   Engine.obj
 0001:00007910       ___E__412__@0              10008910 f   Engine.obj
 0001:00007920       ___E__413__@0              10008920 f   Engine.obj
 0001:00007930       ___E__414__@0              10008930 f   Engine.obj
 0001:00007940       ___E__415__@0              10008940 f   Engine.obj
 0001:00007950       ___E__416__@0              10008950 f   Engine.obj
 0001:00007960       ___E__417__@0              10008960 f   Engine.obj
 0001:00007970       ___E__418__@0              10008970 f   Engine.obj
 0001:00007980       ___E__419__@0              10008980 f   Engine.obj
 0001:00007990       ___E__420__@0              10008990 f   Engine.obj
 0001:000079a0       ___E__421__@0              100089a0 f   Engine.obj
 0001:000079b0       ___E__422__@0              100089b0 f   Engine.obj
 0001:000079c0       ___E__423__@0              100089c0 f   Engine.obj
 0001:000079d0       ___E__424__@0              100089d0 f   Engine.obj
 0001:000079e0       ___E__425__@0              100089e0 f   Engine.obj
 0001:000079f0       ___E__426__@0              100089f0 f   Engine.obj
 0001:00007a00       ___E__427__@0              10008a00 f   Engine.obj
 0001:00007a10       ___E__428__@0              10008a10 f   Engine.obj
 0001:00007a20       ___E__429__@0              10008a20 f   Engine.obj
 0001:00007a30       ___E__430__@0              10008a30 f   Engine.obj
 0001:00007a40       ___E__431__@0              10008a40 f   Engine.obj
 0001:00007a50       ___E__432__@0              10008a50 f   Engine.obj
 0001:00007a60       ___E__433__@0              10008a60 f   Engine.obj
 0001:00007a70       ___E__434__@0              10008a70 f   Engine.obj
 0001:00007a80       ___E__435__@0              10008a80 f   Engine.obj
 0001:00007a90       ___E__436__@0              10008a90 f   Engine.obj
 0001:00007aa0       ___E__437__@0              10008aa0 f   Engine.obj
 0001:00007ab0       ___E__438__@0              10008ab0 f   Engine.obj
 0001:00007ac0       ___E__439__@0              10008ac0 f   Engine.obj
 0001:00007ad0       ___E__440__@0              10008ad0 f   Engine.obj
 0001:00007ae0       ___E__441__@0              10008ae0 f   Engine.obj
 0001:00007af0       ___E__442__@0              10008af0 f   Engine.obj
 0001:00007b00       ___E__443__@0              10008b00 f   Engine.obj
 0001:00007b10       ___E__444__@0              10008b10 f   Engine.obj
 0001:00007b20       ___E__445__@0              10008b20 f   Engine.obj
 0001:00007b30       ___E__446__@0              10008b30 f   Engine.obj
 0001:00007b40       ___E__447__@0              10008b40 f   Engine.obj
 0001:00007b50       ___E__448__@0              10008b50 f   Engine.obj
 0001:00007b60       ___E__449__@0              10008b60 f   Engine.obj
 0001:00007b70       ___E__450__@0              10008b70 f   Engine.obj
 0001:00007b80       ___E__451__@0              10008b80 f   Engine.obj
 0001:00007b90       ___E__452__@0              10008b90 f   Engine.obj
 0001:00007ba0       ___E__453__@0              10008ba0 f   Engine.obj
 0001:00007bb0       ___E__454__@0              10008bb0 f   Engine.obj
 0001:00007bc0       ___E__455__@0              10008bc0 f   Engine.obj
 0001:00007bd0       ___E__456__@0              10008bd0 f   Engine.obj
 0001:00007be0       ___E__457__@0              10008be0 f   Engine.obj
 0001:00007bf0       ___E__458__@0              10008bf0 f   Engine.obj
 0001:00007c00       ___E__459__@0              10008c00 f   Engine.obj
 0001:00007c10       ___E__460__@0              10008c10 f   Engine.obj
 0001:00007c20       ___E__461__@0              10008c20 f   Engine.obj
 0001:00007c30       ___E__462__@0              10008c30 f   Engine.obj
 0001:00007c40       ___E__463__@0              10008c40 f   Engine.obj
 0001:00007c50       ___E__464__@0              10008c50 f   Engine.obj
 0001:00007c60       ___E__465__@0              10008c60 f   Engine.obj
 0001:00007c70       ___E__466__@0              10008c70 f   Engine.obj
 0001:00007c80       ___E__467__@0              10008c80 f   Engine.obj
 0001:00007c90       ___E__468__@0              10008c90 f   Engine.obj
 0001:00007ca0       ___E__469__@0              10008ca0 f   Engine.obj
 0001:00007cb0       ___E__470__@0              10008cb0 f   Engine.obj
 0001:00007cc0       ___E__471__@0              10008cc0 f   Engine.obj
 0001:00007cd0       ___E__472__@0              10008cd0 f   Engine.obj
 0001:00007ce0       ___E__473__@0              10008ce0 f   Engine.obj
 0001:00007cf0       ___E__474__@0              10008cf0 f   Engine.obj
 0001:00007d00       ___E__475__@0              10008d00 f   Engine.obj
 0001:00007d10       ___E__476__@0              10008d10 f   Engine.obj
 0001:00007d20       ___E__477__@0              10008d20 f   Engine.obj
 0001:00007d30       ___E__478__@0              10008d30 f   Engine.obj
 0001:00007d40       ___E__479__@0              10008d40 f   Engine.obj
 0001:00007d50       ___E__480__@0              10008d50 f   Engine.obj
 0001:00007d60       ___E__481__@0              10008d60 f   Engine.obj
 0001:00007d70       ___E__482__@0              10008d70 f   Engine.obj
 0001:00007d80       ___E__483__@0              10008d80 f   Engine.obj
 0001:00007d90       ___E__484__@0              10008d90 f   Engine.obj
 0001:00007da0       ___E__485__@0              10008da0 f   Engine.obj
 0001:00007db0       ___E__486__@0              10008db0 f   Engine.obj
 0001:00007dc0       ___E__487__@0              10008dc0 f   Engine.obj
 0001:00007dd0       ___E__488__@0              10008dd0 f   Engine.obj
 0001:00007de0       ___E__489__@0              10008de0 f   Engine.obj
 0001:00007df0       ___E__490__@0              10008df0 f   Engine.obj
 0001:00007e00       ___E__491__@0              10008e00 f   Engine.obj
 0001:00007e10       ___E__492__@0              10008e10 f   Engine.obj
 0001:00007e20       ___E__493__@0              10008e20 f   Engine.obj
 0001:00007e30       ___E__494__@0              10008e30 f   Engine.obj
 0001:00007e40       ___E__495__@0              10008e40 f   Engine.obj
 0001:00007e50       ___E__496__@0              10008e50 f   Engine.obj
 0001:00007e60       ___E__497__@0              10008e60 f   Engine.obj
 0001:00007e70       ___E__498__@0              10008e70 f   Engine.obj
 0001:00007e80       ___E__499__@0              10008e80 f   Engine.obj
 0001:00007e90       ___E__500__@0              10008e90 f   Engine.obj
 0001:00007ea0       ___E__501__@0              10008ea0 f   Engine.obj
 0001:00007eb0       ___E__502__@0              10008eb0 f   Engine.obj
 0001:00007ec0       ___E__503__@0              10008ec0 f   Engine.obj
 0001:00007ed0       ___E__504__@0              10008ed0 f   Engine.obj
 0001:00007ee0       ___E__505__@0              10008ee0 f   Engine.obj
 0001:00007ef0       ___E__506__@0              10008ef0 f   Engine.obj
 0001:00007f00       ___E__507__@0              10008f00 f   Engine.obj
 0001:00007f10       ___E__508__@0              10008f10 f   Engine.obj
 0001:00007f20       ___E__509__@0              10008f20 f   Engine.obj
 0001:00007f30       ___E__510__@0              10008f30 f   Engine.obj
 0001:00007f40       ___E__511__@0              10008f40 f   Engine.obj
 0001:00007f50       ___E__512__@0              10008f50 f   Engine.obj
 0001:00007f60       ___E__513__@0              10008f60 f   Engine.obj
 0001:00007f70       ___E__514__@0              10008f70 f   Engine.obj
 0001:00007f80       ___E__515__@0              10008f80 f   Engine.obj
 0001:00007f90       ___E__516__@0              10008f90 f   Engine.obj
 0001:00007fa0       ___E__517__@0              10008fa0 f   Engine.obj
 0001:00007fb0       ___E__518__@0              10008fb0 f   Engine.obj
 0001:00007fc0       ___E__519__@0              10008fc0 f   Engine.obj
 0001:00007fd0       ___E__520__@0              10008fd0 f   Engine.obj
 0001:00007fe0       ___E__521__@0              10008fe0 f   Engine.obj
 0001:00007ff0       ___E__522__@0              10008ff0 f   Engine.obj
 0001:00008000       ___E__523__@0              10009000 f   Engine.obj
 0001:00008010       ___E__524__@0              10009010 f   Engine.obj
 0001:00008020       ___E__525__@0              10009020 f   Engine.obj
 0001:00008030       ___E__526__@0              10009030 f   Engine.obj
 0001:00008040       ___E__527__@0              10009040 f   Engine.obj
 0001:00008050       ___E__528__@0              10009050 f   Engine.obj
 0001:00008060       ___E__529__@0              10009060 f   Engine.obj
 0001:00008070       ___E__530__@0              10009070 f   Engine.obj
 0001:00008080       ___E__531__@0              10009080 f   Engine.obj
 0001:00008090       ___E__532__@0              10009090 f   Engine.obj
 0001:000080a0       ___E__533__@0              100090a0 f   Engine.obj
 0001:000080b0       ___E__534__@0              100090b0 f   Engine.obj
 0001:000080c0       ___E__535__@0              100090c0 f   Engine.obj
 0001:000080d0       ___E__536__@0              100090d0 f   Engine.obj
 0001:000080e0       ___E__537__@0              100090e0 f   Engine.obj
 0001:000080f0       ___E__538__@0              100090f0 f   Engine.obj
 0001:00008100       ___E__539__@0              10009100 f   Engine.obj
 0001:00008110       ___E__540__@0              10009110 f   Engine.obj
 0001:00008120       ___E__541__@0              10009120 f   Engine.obj
 0001:00008130       ___E__542__@0              10009130 f   Engine.obj
 0001:00008140       ___E__543__@0              10009140 f   Engine.obj
 0001:00008150       ___E__544__@0              10009150 f   Engine.obj
 0001:00008160       ___E__545__@0              10009160 f   Engine.obj
 0001:00008170       ___E__546__@0              10009170 f   Engine.obj
 0001:00008180       ___E__547__@0              10009180 f   Engine.obj
 0001:00008190       ___E__548__@0              10009190 f   Engine.obj
 0001:000081a0       ___E__549__@0              100091a0 f   Engine.obj
 0001:000081b0       ___E__550__@0              100091b0 f   Engine.obj
 0001:000081c0       ___E__551__@0              100091c0 f   Engine.obj
 0001:000081d0       ___E__552__@0              100091d0 f   Engine.obj
 0001:000081e0       ___E__553__@0              100091e0 f   Engine.obj
 0001:000081f0       ___E__554__@0              100091f0 f   Engine.obj
 0001:00008200       ___E__555__@0              10009200 f   Engine.obj
 0001:00008210       ___E__556__@0              10009210 f   Engine.obj
 0001:00008220       ___E__557__@0              10009220 f   Engine.obj
 0001:00008230       ___E__558__@0              10009230 f   Engine.obj
 0001:00008240       ___E__559__@0              10009240 f   Engine.obj
 0001:00008250       ___E__560__@0              10009250 f   Engine.obj
 0001:00008260       ___E__561__@0              10009260 f   Engine.obj
 0001:00008270       ___E__562__@0              10009270 f   Engine.obj
 0001:00008280       ___E__563__@0              10009280 f   Engine.obj
 0001:00008290       ___E__564__@0              10009290 f   Engine.obj
 0001:000082a0       ___E__565__@0              100092a0 f   Engine.obj
 0001:000082b0       ___E__566__@0              100092b0 f   Engine.obj
 0001:000082c0       ___E__567__@0              100092c0 f   Engine.obj
 0001:000082d0       ___E__568__@0              100092d0 f   Engine.obj
 0001:000082e0       ___E__569__@0              100092e0 f   Engine.obj
 0001:000082f0       ___E__570__@0              100092f0 f   Engine.obj
 0001:00008300       ___E__571__@0              10009300 f   Engine.obj
 0001:00008310       ___E__572__@0              10009310 f   Engine.obj
 0001:00008320       ___E__573__@0              10009320 f   Engine.obj
 0001:00008330       ___E__574__@0              10009330 f   Engine.obj
 0001:00008340       ___E__575__@0              10009340 f   Engine.obj
 0001:00008350       ___E__576__@0              10009350 f   Engine.obj
 0001:00008360       ___E__577__@0              10009360 f   Engine.obj
 0001:00008370       ___E__578__@0              10009370 f   Engine.obj
 0001:00008380       ___E__579__@0              10009380 f   Engine.obj
 0001:00008390       ___E__580__@0              10009390 f   Engine.obj
 0001:000083a0       ___E__581__@0              100093a0 f   Engine.obj
 0001:000083b0       ___E__582__@0              100093b0 f   Engine.obj
 0001:000083c0       ___E__583__@0              100093c0 f   Engine.obj
 0001:000083d0       ___E__584__@0              100093d0 f   Engine.obj
 0001:000083e0       ___E__585__@0              100093e0 f   Engine.obj
 0001:000083f0       ___E__586__@0              100093f0 f   Engine.obj
 0001:00008400       ___E__587__@0              10009400 f   Engine.obj
 0001:00008410       ___E__588__@0              10009410 f   Engine.obj
 0001:00008420       ___E__589__@0              10009420 f   Engine.obj
 0001:00008430       ___E__590__@0              10009430 f   Engine.obj
 0001:00008440       ___E__591__@0              10009440 f   Engine.obj
 0001:00008450       ___E__592__@0              10009450 f   Engine.obj
 0001:00008460       ___E__593__@0              10009460 f   Engine.obj
 0001:00008470       ___E__594__@0              10009470 f   Engine.obj
 0001:00008480       ___E__595__@0              10009480 f   Engine.obj
 0001:00008490       ___E__596__@0              10009490 f   Engine.obj
 0001:000084a0       ___E__597__@0              100094a0 f   Engine.obj
 0001:000084b0       ___E__598__@0              100094b0 f   Engine.obj
 0001:000084c0       ___E__599__@0              100094c0 f   Engine.obj
 0001:000084d0       ___E__600__@0              100094d0 f   Engine.obj
 0001:000084e0       ___E__601__@0              100094e0 f   Engine.obj
 0001:000084f0       ___E__602__@0              100094f0 f   Engine.obj
 0001:00008500       ___E__603__@0              10009500 f   Engine.obj
 0001:00008510       ___E__604__@0              10009510 f   Engine.obj
 0001:00008520       ___E__605__@0              10009520 f   Engine.obj
 0001:00008530       ___E__606__@0              10009530 f   Engine.obj
 0001:00008540       ___E__607__@0              10009540 f   Engine.obj
 0001:00008550       ___E__608__@0              10009550 f   Engine.obj
 0001:00008560       ___E__609__@0              10009560 f   Engine.obj
 0001:00008570       ___E__610__@0              10009570 f   Engine.obj
 0001:00008580       ___E__611__@0              10009580 f   Engine.obj
 0001:00008590       ___E__612__@0              10009590 f   Engine.obj
 0001:000085a0       ___E__613__@0              100095a0 f   Engine.obj
 0001:000085b0       ___E__614__@0              100095b0 f   Engine.obj
 0001:000085c0       ___E__615__@0              100095c0 f   Engine.obj
 0001:000085d0       ___E__616__@0              100095d0 f   Engine.obj
 0001:000085e0       ___E__617__@0              100095e0 f   Engine.obj
 0001:000085f0       ___E__618__@0              100095f0 f   Engine.obj
 0001:00008600       ___E__619__@0              10009600 f   Engine.obj
 0001:00008610       ___E__620__@0              10009610 f   Engine.obj
 0001:00008620       ___E__621__@0              10009620 f   Engine.obj
 0001:00008630       ___E__622__@0              10009630 f   Engine.obj
 0001:00008640       ___E__623__@0              10009640 f   Engine.obj
 0001:00008650       ___E__624__@0              10009650 f   Engine.obj
 0001:00008660       ___E__625__@0              10009660 f   Engine.obj
 0001:00008670       ___E__626__@0              10009670 f   Engine.obj
 0001:00008680       ___E__627__@0              10009680 f   Engine.obj
 0001:00008690       ___E__628__@0              10009690 f   Engine.obj
 0001:000086a0       ___E__629__@0              100096a0 f   Engine.obj
 0001:000086b0       ___E__630__@0              100096b0 f   Engine.obj
 0001:000086c0       ___E__631__@0              100096c0 f   Engine.obj
 0001:000086d0       ___E__632__@0              100096d0 f   Engine.obj
 0001:000086e0       ___E__633__@0              100096e0 f   Engine.obj
 0001:000086f0       ___E__634__@0              100096f0 f   Engine.obj
 0001:00008700       ___E__635__@0              10009700 f   Engine.obj
 0001:00008710       ___E__636__@0              10009710 f   Engine.obj
 0001:00008720       ___E__637__@0              10009720 f   Engine.obj
 0001:00008730       ___E__638__@0              10009730 f   Engine.obj
 0001:00008740       ___E__639__@0              10009740 f   Engine.obj
 0001:00008750       ___E__640__@0              10009750 f   Engine.obj
 0001:00008760       ___E__641__@0              10009760 f   Engine.obj
 0001:00008770       ___E__642__@0              10009770 f   Engine.obj
 0001:00008780       ___E__643__@0              10009780 f   Engine.obj
 0001:00008790       ___E__644__@0              10009790 f   Engine.obj
 0001:000087a0       ___E__645__@0              100097a0 f   Engine.obj
 0001:000087b0       ___E__646__@0              100097b0 f   Engine.obj
 0001:000087c0       ___E__647__@0              100097c0 f   Engine.obj
 0001:000087d0       ___E__648__@0              100097d0 f   Engine.obj
 0001:000087e0       ___E__649__@0              100097e0 f   Engine.obj
 0001:000087f0       ___E__650__@0              100097f0 f   Engine.obj
 0001:00008800       ___E__651__@0              10009800 f   Engine.obj
 0001:00008810       ___E__652__@0              10009810 f   Engine.obj
 0001:00008820       ___E__653__@0              10009820 f   Engine.obj
 0001:00008830       ___E__654__@0              10009830 f   Engine.obj
 0001:00008840       ___E__655__@0              10009840 f   Engine.obj
 0001:00008850       ___E__656__@0              10009850 f   Engine.obj
 0001:00008860       ___E__657__@0              10009860 f   Engine.obj
 0001:00008870       ___E__658__@0              10009870 f   Engine.obj
 0001:00008880       ___E__659__@0              10009880 f   Engine.obj
 0001:00008890       ___E__660__@0              10009890 f   Engine.obj
 0001:000088a0       ___E__661__@0              100098a0 f   Engine.obj
 0001:000088b0       ___E__662__@0              100098b0 f   Engine.obj
 0001:000088c0       ___E__663__@0              100098c0 f   Engine.obj
 0001:000088d0       ___E__664__@0              100098d0 f   Engine.obj
 0001:000088e0       ___E__665__@0              100098e0 f   Engine.obj
 0001:000088f0       ___E__666__@0              100098f0 f   Engine.obj
 0001:00008900       ___E__667__@0              10009900 f   Engine.obj
 0001:00008910       ___E__668__@0              10009910 f   Engine.obj
 0001:00008920       ___E__669__@0              10009920 f   Engine.obj
 0001:00008930       ___E__670__@0              10009930 f   Engine.obj
 0001:00008940       ___E__671__@0              10009940 f   Engine.obj
 0001:00008950       ___E__672__@0              10009950 f   Engine.obj
 0001:00008960       ___E__673__@0              10009960 f   Engine.obj
 0001:00008970       ___E__674__@0              10009970 f   Engine.obj
 0001:00008980       ___E__675__@0              10009980 f   Engine.obj
 0001:00008990       ___E__676__@0              10009990 f   Engine.obj
 0001:000089a0       ___E__677__@0              100099a0 f   Engine.obj
 0001:000089b0       ___E__678__@0              100099b0 f   Engine.obj
 0001:000089c0       ___E__679__@0              100099c0 f   Engine.obj
 0001:000089d0       ___E__680__@0              100099d0 f   Engine.obj
 0001:000089e0       ___E__681__@0              100099e0 f   Engine.obj
 0001:000089f0       ___E__682__@0              100099f0 f   Engine.obj
 0001:00008a00       ___E__683__@0              10009a00 f   Engine.obj
 0001:00008a10       ___E__684__@0              10009a10 f   Engine.obj
 0001:00008a20       ___E__685__@0              10009a20 f   Engine.obj
 0001:00008a30       ___E__686__@0              10009a30 f   Engine.obj
 0001:00008a40       ___E__687__@0              10009a40 f   Engine.obj
 0001:00008a50       ___E__688__@0              10009a50 f   Engine.obj
 0001:00008a60       ___E__689__@0              10009a60 f   Engine.obj
 0001:00008a70       ___E__690__@0              10009a70 f   Engine.obj
 0001:00008a80       ___E__691__@0              10009a80 f   Engine.obj
 0001:00008a90       ___E__692__@0              10009a90 f   Engine.obj
 0001:00008aa0       ___E__693__@0              10009aa0 f   Engine.obj
 0001:00008ab0       ___E__694__@0              10009ab0 f   Engine.obj
 0001:00008ac0       ___E__695__@0              10009ac0 f   Engine.obj
 0001:00008ad0       ___E__696__@0              10009ad0 f   Engine.obj
 0001:00008ae0       ___E__697__@0              10009ae0 f   Engine.obj
 0001:00008af0       ___E__698__@0              10009af0 f   Engine.obj
 0001:00008b00       ___E__699__@0              10009b00 f   Engine.obj
 0001:00008b10       ___E__700__@0              10009b10 f   Engine.obj
 0001:00008b20       ___E__701__@0              10009b20 f   Engine.obj
 0001:00008b30       ___E__702__@0              10009b30 f   Engine.obj
 0001:00008b40       ___E__703__@0              10009b40 f   Engine.obj
 0001:00008b50       ___E__704__@0              10009b50 f   Engine.obj
 0001:00008b60       ___E__705__@0              10009b60 f   Engine.obj
 0001:00008b70       ___E__706__@0              10009b70 f   Engine.obj
 0001:00008b80       ___E__707__@0              10009b80 f   Engine.obj
 0001:00008b90       ___E__708__@0              10009b90 f   Engine.obj
 0001:00008ba0       ___E__709__@0              10009ba0 f   Engine.obj
 0001:00008bb0       ___E__710__@0              10009bb0 f   Engine.obj
 0001:00008bc0       ___E__711__@0              10009bc0 f   Engine.obj
 0001:00008bd0       ___E__712__@0              10009bd0 f   Engine.obj
 0001:00008be0       ___E__713__@0              10009be0 f   Engine.obj
 0001:00008bf0       ___E__714__@0              10009bf0 f   Engine.obj
 0001:00008c00       ___E__715__@0              10009c00 f   Engine.obj
 0001:00008c10       ___E__716__@0              10009c10 f   Engine.obj
 0001:00008c20       ___E__717__@0              10009c20 f   Engine.obj
 0001:00008c30       ___E__718__@0              10009c30 f   Engine.obj
 0001:00008c40       ___E__719__@0              10009c40 f   Engine.obj
 0001:00008c50       ___E__720__@0              10009c50 f   Engine.obj
 0001:00008c60       ___E__721__@0              10009c60 f   Engine.obj
 0001:00008c70       ___E__722__@0              10009c70 f   Engine.obj
 0001:00008c80       ___E__723__@0              10009c80 f   Engine.obj
 0001:00008c90       ___E__724__@0              10009c90 f   Engine.obj
 0001:00008ca0       ___E__725__@0              10009ca0 f   Engine.obj
 0001:00008cb0       ___E__726__@0              10009cb0 f   Engine.obj
 0001:00008cc0       ___E__727__@0              10009cc0 f   Engine.obj
 0001:00008cd0       ___E__728__@0              10009cd0 f   Engine.obj
 0001:00008ce0       ___E__729__@0              10009ce0 f   Engine.obj
 0001:00008cf0       ___E__730__@0              10009cf0 f   Engine.obj
 0001:00008d00       ___E__731__@0              10009d00 f   Engine.obj
 0001:00008d10       ___E__732__@0              10009d10 f   Engine.obj
 0001:00008d20       ___E__733__@0              10009d20 f   Engine.obj
 0001:00008d30       ___E__734__@0              10009d30 f   Engine.obj
 0001:00008d40       ___E__735__@0              10009d40 f   Engine.obj
 0001:00008d50       ___E__736__@0              10009d50 f   Engine.obj
 0001:00008d60       ___E__737__@0              10009d60 f   Engine.obj
 0001:00008d70       ___E__738__@0              10009d70 f   Engine.obj
 0001:00008d80       ___E__739__@0              10009d80 f   Engine.obj
 0001:00008d90       ___E__740__@0              10009d90 f   Engine.obj
 0001:00008da0       ___E__741__@0              10009da0 f   Engine.obj
 0001:00008db0       ___E__742__@0              10009db0 f   Engine.obj
 0001:00008dc0       ___E__743__@0              10009dc0 f   Engine.obj
 0001:00008dd0       ___E__744__@0              10009dd0 f   Engine.obj
 0001:00008de0       ___E__745__@0              10009de0 f   Engine.obj
 0001:00008df0       ___E__746__@0              10009df0 f   Engine.obj
 0001:00008e00       ___E__747__@0              10009e00 f   Engine.obj
 0001:00008e10       ___E__748__@0              10009e10 f   Engine.obj
 0001:00008e20       ___E__749__@0              10009e20 f   Engine.obj
 0001:00008e30       ___E__750__@0              10009e30 f   Engine.obj
 0001:00008e40       ___E__751__@0              10009e40 f   Engine.obj
 0001:00008e50       ___E__752__@0              10009e50 f   Engine.obj
 0001:00008e60       ___E__753__@0              10009e60 f   Engine.obj
 0001:00008e70       ___E__754__@0              10009e70 f   Engine.obj
 0001:00008e80       ___E__755__@0              10009e80 f   Engine.obj
 0001:00008e90       ___E__756__@0              10009e90 f   Engine.obj
 0001:00008ea0       ___E__757__@0              10009ea0 f   Engine.obj
 0001:00008eb0       ___E__758__@0              10009eb0 f   Engine.obj
 0001:00008ec0       ___E__759__@0              10009ec0 f   Engine.obj
 0001:00008ed0       ___E__760__@0              10009ed0 f   Engine.obj
 0001:00008ee0       ___E__761__@0              10009ee0 f   Engine.obj
 0001:00008ef0       ___E__762__@0              10009ef0 f   Engine.obj
 0001:00008f00       ___E__763__@0              10009f00 f   Engine.obj
 0001:00008f10       ___E__764__@0              10009f10 f   Engine.obj
 0001:00008f20       ___E__765__@0              10009f20 f   Engine.obj
 0001:00008f30       ___E__766__@0              10009f30 f   Engine.obj
 0001:00008f40       ___E__767__@0              10009f40 f   Engine.obj
 0001:00008f50       ___E__768__@0              10009f50 f   Engine.obj
 0001:00008f60       ___E__769__@0              10009f60 f   Engine.obj
 0001:00008f70       ___E__770__@0              10009f70 f   Engine.obj
 0001:00008f80       ___E__771__@0              10009f80 f   Engine.obj
 0001:00008f90       ___E__772__@0              10009f90 f   Engine.obj
 0001:00008fa0       ___E__773__@0              10009fa0 f   Engine.obj
 0001:00008fb0       ___E__774__@0              10009fb0 f   Engine.obj
 0001:00008fc0       ___E__775__@0              10009fc0 f   Engine.obj
 0001:00008fd0       ___E__776__@0              10009fd0 f   Engine.obj
 0001:00008fe0       ___E__777__@0              10009fe0 f   Engine.obj
 0001:00008ff0       ___E__778__@0              10009ff0 f   Engine.obj
 0001:00009000       ___E__779__@0              1000a000 f   Engine.obj
 0001:00009010       ___E__780__@0              1000a010 f   Engine.obj
 0001:00009020       ___E__781__@0              1000a020 f   Engine.obj
 0001:00009030       ___E__782__@0              1000a030 f   Engine.obj
 0001:00009040       ___E__783__@0              1000a040 f   Engine.obj
 0001:00009050       ___E__784__@0              1000a050 f   Engine.obj
 0001:00009060       ___E__785__@0              1000a060 f   Engine.obj
 0001:00009070       ___E__786__@0              1000a070 f   Engine.obj
 0001:00009080       ___E__787__@0              1000a080 f   Engine.obj
 0001:00009090       ___E__788__@0              1000a090 f   Engine.obj
 0001:000090a0       ___E__789__@0              1000a0a0 f   Engine.obj
 0001:000090b0       ___E__790__@0              1000a0b0 f   Engine.obj
 0001:000090c0       ___E__791__@0              1000a0c0 f   Engine.obj
 0001:000090d0       ___E__792__@0              1000a0d0 f   Engine.obj
 0001:000090e0       ___E__793__@0              1000a0e0 f   Engine.obj
 0001:000090f0       ___E__794__@0              1000a0f0 f   Engine.obj
 0001:00009100       ___E__795__@0              1000a100 f   Engine.obj
 0001:00009110       ___E__796__@0              1000a110 f   Engine.obj
 0001:00009120       ___E__797__@0              1000a120 f   Engine.obj
 0001:00009130       ___E__798__@0              1000a130 f   Engine.obj
 0001:00009140       ___E__799__@0              1000a140 f   Engine.obj
 0001:00009150       ___E__800__@0              1000a150 f   Engine.obj
 0001:00009160       ___E__801__@0              1000a160 f   Engine.obj
 0001:00009170       ___E__802__@0              1000a170 f   Engine.obj
 0001:00009180       ___E__803__@0              1000a180 f   Engine.obj
 0001:00009190       ___E__804__@0              1000a190 f   Engine.obj
 0001:000091a0       ___E__805__@0              1000a1a0 f   Engine.obj
 0001:000091b0       ?OnOk@@YIHPAX0@Z           1000a1b0 f   Engine.obj
 0001:00009240       ?OnCancel@@YIHPAX0@Z       1000a240 f   Engine.obj
 0001:000092d0       ?PressKey@@YAHHPAX@Z       1000a2d0 f   Engine.obj
 0001:00009450       ?MyKalItemShop1@@YIHPAX0PBDH@Z 1000a450 f   Engine.obj
 0001:00009530       ?MyKalItemShop2@@YIHPAX0PBDH@Z 1000a530 f   Engine.obj
 0001:00009610       ?MyKalItemShop3@@YIHPAX0PBDH@Z 1000a610 f   Engine.obj
 0001:00009700       ?CheckForDss@@YIXHPAX@Z    1000a700 f   Engine.obj
 0001:000097f0       ?MyArmorGetGrade@@YIHHPAX@Z 1000a7f0 f   Engine.obj
 0001:00009810       ?BofStatFix@@YAHHPAXPBD@Z  1000a810 f   Engine.obj
 0001:00009850       ?MakeTip@@YIXPAX0HHHDHH@Z  1000a850 f   Engine.obj
 0001:00009860       ?MySkillButton@@YAHH@Z     1000a860 f   Engine.obj
 0001:00009930       ?MyIsPlayerCheck@@YAHXZ    1000a930 f   Engine.obj
 0001:00009960       ?MyPlayerTick@@YIHHPAXI@Z  1000a960 f   Engine.obj
 0001:00009ad0       ?MyIsPlayerAttack@@YAHXZ   1000aad0 f   Engine.obj
 0001:00009af0       ?MySetTip@@YIPAXHPAXPBDKHH@Z 1000aaf0 f   Engine.obj
 0001:00009b50       ?SwitchTable@@YAXXZ        1000ab50 f   Engine.obj
 0001:00009c70       ?MD5Transform@MD5@@CAXQAKQAE@Z 1000ac70 f i Engine.obj
 0001:0000a320       ?Decode@MD5@@CAXPAKPAEI@Z  1000b320 f i Engine.obj
 0001:0000a360       ??0MD5@@QAE@XZ             1000b360 f i Engine.obj
 0001:0000a390       ?Update@MD5@@QAEXPAEI@Z    1000b390 f i Engine.obj
 0001:0000a440       ?Final@MD5@@QAEXXZ         1000b440 f i Engine.obj
 0001:0000a540       ?digestFile@MD5@@QAEPADPAD@Z 1000b540 f i Engine.obj
 0001:0000a6a0       ?PKHide@@YAHHHHHPBD@Z      1000b6a0 f   Engine.obj
 0001:0000a6d0       ?HideMyPKPWD@@YAXXZ        1000b6d0 f   Engine.obj
 0001:0000a760       ?DisableProtection@@YAXXZ  1000b760 f   Engine.obj
 0001:0000a830       ?ChecksumCheck@@YAXPAX@Z   1000b830 f   Engine.obj
 0001:0000b1b0       ??_D?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAEXXZ 1000c1b0 f i Engine.obj
 0001:0000b230       ??_D?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXXZ 1000c230 f i Engine.obj
 0001:0000b2a0       ?Disconnect@@YIHPAX0@Z     1000c2a0 f   Engine.obj
 0001:0000b2c0       ?Destroy@@YIPAXPAX0@Z      1000c2c0 f   Engine.obj
 0001:0000b2e0       ?GetNewCursorPos@@YAHH@Z   1000c2e0 f   Engine.obj
 0001:0000b2f0       ?SendPacket@@YIHPAX0PBDH@Z 1000c2f0 f   Engine.obj
 0001:0000b320       ?HonorName@@YAPADH@Z       1000c320 f   Engine.obj
 0001:0000b3a0       _DllMain@12                1000c3a0 f   Engine.obj
 0001:0000b6d0       ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 1000c6d0 f i Engine.obj
 0001:0000b790       ??0?$Interface@VIPackets@@@@QAE@XZ 1000c790 f i Engine.obj
 0001:0000b810       ??0?$Interface@VIProtect@@@@QAE@XZ 1000c810 f i Engine.obj
 0001:0000b890       ??0?$Interface@VIBuff@@@@QAE@XZ 1000c890 f i Engine.obj
 0001:0000b910       ??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z 1000c910 f i Engine.obj
 0001:0000b9f0       ??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z 1000c9f0 f i Engine.obj
 0001:0000bae0       ?close@?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAEXXZ 1000cae0 f i Engine.obj
 0001:0000bb50       ??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ 1000cb50 f i Engine.obj
 0001:0000bc10       ?_Lock@?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAEXXZ 1000cc10 f i Engine.obj
 0001:0000bc20       ?_Unlock@?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAEXXZ 1000cc20 f i Engine.obj
 0001:0000bc30       ?overflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHH@Z 1000cc30 f i Engine.obj
 0001:0000be30       ?pbackfail@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHH@Z 1000ce30 f i Engine.obj
 0001:0000bec0       ?underflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1000cec0 f i Engine.obj
 0001:0000bf10       ?uflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1000cf10 f i Engine.obj
 0001:0000c1b0       ?seekoff@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAE?AV?$fpos@H@2@_JHH@Z 1000d1b0 f i Engine.obj
 0001:0000c2b0       ?seekpos@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAE?AV?$fpos@H@2@V32@H@Z 1000d2b0 f i Engine.obj
 0001:0000c3a0       ?setbuf@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEPAV?$basic_streambuf@DU?$char_traits@D@std@@@2@PAD_J@Z 1000d3a0 f i Engine.obj
 0001:0000c430       ?sync@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1000d430 f i Engine.obj
 0001:0000c470       ?imbue@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEXABVlocale@2@@Z 1000d470 f i Engine.obj
 0001:0000c4b0       ??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z 1000d4b0 f i Engine.obj
 0001:0000c550       ?str@?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ 1000d550 f i Engine.obj
 0001:0000c570       ??1?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAE@XZ 1000d570 f i Engine.obj
 0001:0000c5e0       ?overflow@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MAEHH@Z 1000d5e0 f i Engine.obj
 0001:0000c7b0       ?pbackfail@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MAEHH@Z 1000d7b0 f i Engine.obj
 0001:0000c800       ?underflow@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MAEHXZ 1000d800 f i Engine.obj
 0001:0000c870       ?seekoff@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MAE?AV?$fpos@H@2@_JHH@Z 1000d870 f i Engine.obj
 0001:0000ca20       ?seekpos@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MAE?AV?$fpos@H@2@V32@H@Z 1000da20 f i Engine.obj
 0001:0000cb50       ??_E?$basic_ifstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1000db50 f i  CIL library: CIL module
 0001:0000cb50       ??_G?$basic_ifstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1000db50 f i Engine.obj
 0001:0000cbf0       ??_G?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1000dbf0 f i Engine.obj
 0001:0000cbf0       ??_E?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1000dbf0 f i  CIL library: CIL module
 0001:0000cc20       ??_G?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z 1000dc20 f i Engine.obj
 0001:0000cc20       ??_E?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z 1000dc20 f i  CIL library: CIL module
 0001:0000ccb0       ??_E?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z 1000dcb0 f i  CIL library: CIL module
 0001:0000ccb0       ??_G?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z 1000dcb0 f i Engine.obj
 0001:0000cce0       ?_Reserve@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@IAEXI@Z 1000dce0 f i Engine.obj
 0001:0000cd60       ?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PBDHH@Z 1000dd60 f i Engine.obj
 0001:0000ce60       ?_Endwrite@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IAE_NXZ 1000de60 f i Engine.obj
 0001:0000cfa0       ?_Reset_back@?$basic_filebuf@DU?$char_traits@D@std@@@std@@AAEXXZ 1000dfa0 f i Engine.obj
 0001:0000cfd0       ?_Set_back@?$basic_filebuf@DU?$char_traits@D@std@@@std@@AAEXXZ 1000dfd0 f i Engine.obj
 0001:0000d010       ?str@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ 1000e010 f i Engine.obj
 0001:0000d160       ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@PBDI@Z 1000e160 f i Engine.obj
 0001:0000d280       ?reserve@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXI@Z 1000e280 f i Engine.obj
 0001:0000d390       ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@ABV12@II@Z 1000e390 f i Engine.obj
 0001:0000d480       ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBDABV10@@Z 1000e480 f i Engine.obj
 0001:0000d550       ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QAV10@PBD@Z 1000e550 f i Engine.obj
 0001:0000d5a0       ??$use_facet@V?$codecvt@DDH@std@@@std@@YAABV?$codecvt@DDH@0@ABVlocale@0@@Z 1000e5a0 f i Engine.obj
 0001:0000d6a0       ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@PBD@Z 1000e6a0 f i Engine.obj
 0001:0000d6d0       ??$_Move@PAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAV12@@std@@YAPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAV10@00U_Nonscalar_ptr_iterator_tag@0@@Z 1000e6d0 f i Engine.obj
 0001:0000d750       ??$_Uninit_move@PAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V12@@std@@YAPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAV10@00AAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@0U_Nonscalar_ptr_iterator_tag@0@@Z 1000e750 f i Engine.obj
 0001:0000d810       ??_ELock@@UAEPAXI@Z        1000e810 f i  CIL library: CIL module
 0001:0000d810       ??_GLock@@UAEPAXI@Z        1000e810 f i Graphics.obj
 0001:0000d840       ?Create@?$Interface@VIGraphics@@@@SAXXZ 1000e840 f i Graphics.obj
 0001:0000d8d0       ?ProcessCommand@KCommand@Hooks@@YAHPBD@Z 1000e8d0 f   Hooks.obj
 0001:0000d920       ?WritePacketAutoCrc@KSocket@Hooks@@YAHEPBDZZ 1000e920 f   Hooks.obj
 0001:0000d9a0       ?OnRecv@KSocket@Hooks@@YAHPAUPacket@Engine@@@Z 1000e9a0 f   Hooks.obj
 0001:0000d9c0       ?Hook@IOnSend@@QAEXXZ      1000e9c0 f   OnSend.obj
 0001:0000da00       ?Create@?$Interface@VIOnSend@@@@SAXXZ 1000ea00 f i OnSend.obj
 0001:0000da70       ?isValidMD5@@YA_NV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 1000ea70 f   Packets.obj
 0001:0000dac0       ?Int2String@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z 1000eac0 f   Packets.obj
 0001:0000db90       ?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ 1000eb90 f   Packets.obj
 0001:0000de70       ??1?$map@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@@std@@QAE@XZ 1000ee70 f i Packets.obj
 0001:0000ded0       ?AsadalCheck@@YAHHH@Z      1000eed0 f   Packets.obj
 0001:0000dee0       ?ExplosiveBlowBoxCheck@@YAXPBDHH@Z 1000eee0 f   Packets.obj
 0001:0000df00       ?GeonsMsgCheck@@YAXPBDHH@Z 1000ef00 f   Packets.obj
 0001:0000df20       ?AbsorbMsgCheck@@YAXPBDHH@Z 1000ef20 f   Packets.obj
 0001:0000df40       ?DamageBoxCheck@@YIXHPAXHIPAD@Z 1000ef40 f   Packets.obj
 0001:0000df60       ?DuelCheck@@YAHPBD@Z       1000ef60 f   Packets.obj
 0001:0000e010       ?Hook@IPackets@@QAEXXZ     1000f010 f   Packets.obj
 0001:0000e230       ?engineCheck@@YA_NXZ       1000f230 f   Packets.obj
 0001:0000e3e0       ?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z 1000f3e0 f   Packets.obj
 0001:0000f420       ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ 10010420 f i Packets.obj
 0001:0000f490       ?Send@IPackets@@QAAHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 10010490 f   Packets.obj
 0001:0000f520       ?Analyze@IPackets@@QAE_NE@Z 10010520 f   Packets.obj
 0001:0000f5b0       ?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z 100105b0 f   Packets.obj
 0001:00013060       ??1?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@QAE@XZ 10014060 f i Packets.obj
 0001:00013090       ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@PBD@Z 10014090 f i Packets.obj
 0001:000130c0       ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@D@Z 100140c0 f i Packets.obj
 0001:00013110       ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ 10014110 f i Packets.obj
 0001:00013140       ?clear@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAEXXZ 10014140 f i Packets.obj
 0001:00013180       ?Create@?$Interface@VIPackets@@@@SAXXZ 10014180 f i Packets.obj
 0001:00013210       ?clear@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAEXXZ 10014210 f i Packets.obj
 0001:00013280       ??0?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAE@ABV01@@Z 10014280 f i Packets.obj
 0001:00013360       ?_Tidy@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@IAEXXZ 10014360 f i Packets.obj
 0001:00013360       ?_Tidy@?$vector@HV?$allocator@H@std@@@std@@IAEXXZ 10014360 f i Packets.obj
 0001:00013360       ??1?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAE@XZ 10014360 f i Variables.obj
 0001:00013360       ??1?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAE@XZ 10014360 f i Packets.obj
 0001:00013360       ??1?$vector@HV?$allocator@H@std@@@std@@QAE@XZ 10014360 f i Packets.obj
 0001:00013360       ?_Tidy@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@IAEXXZ 10014360 f i Variables.obj
 0001:00013390       ?push_back@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAEXABUConfigNPC@@@Z 10014390 f i Packets.obj
 0001:00013420       ?erase@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAE?AV?$_Vector_iterator@V?$_Vector_val@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@@2@V?$_Vector_const_iterator@V?$_Vector_val@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@@2@@Z 10014420 f i Packets.obj
 0001:00013460       ?clear@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAEXXZ 10014460 f i Packets.obj
 0001:000134a0       ?find@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@ABH@Z 100144a0 f i Packets.obj
 0001:00013500       ?erase@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEIABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 10014500 f i Packets.obj
 0001:00013550       ?clear@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEXXZ 10014550 f i Packets.obj
 0001:00013590       ??A?$map@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@@std@@QAEAAV?$vector@HV?$allocator@H@std@@@1@ABH@Z 10014590 f i Packets.obj
 0001:00013650       ?erase@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAEIABH@Z 10014650 f i Packets.obj
 0001:000136a0       ?find@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@ABH@Z 100146a0 f i Packets.obj
 0001:00013700       ?count@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QBEIABH@Z 10014700 f i Packets.obj
 0001:00013740       ??0?$Interface@VIOnSend@@@@QAE@XZ 10014740 f i Packets.obj
 0001:000137c0       ??0?$vector@HV?$allocator@H@std@@@std@@QAE@ABV01@@Z 100147c0 f i Packets.obj
 0001:00013880       ?push_back@?$vector@HV?$allocator@H@std@@@std@@QAEXABH@Z 10014880 f i Packets.obj
 0001:00013880       ?push_back@?$vector@HV?$allocator@H@std@@@std@@QAEX$$QAH@Z 10014880 f i Packets.obj
 0001:000138e0       ??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@XZ 100148e0 f i Packets.obj
 0001:000139a0       ??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@XZ 100149a0 f i Packets.obj
 0001:00013a60       ?close@?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ 10014a60 f i Packets.obj
 0001:00013ad0       ??1?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@QAE@XZ 10014ad0 f i Packets.obj
 0001:00013b00       ??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 10014b00 f i  CIL library: CIL module
 0001:00013b00       ??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 10014b00 f i Packets.obj
 0001:00013b90       ?find_first_not_of@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIPBDII@Z 10014b90 f i Packets.obj
 0001:00013c00       ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV12@ABV12@II@Z 10014c00 f i Packets.obj
 0001:00013cb0       ?_Erase@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@@Z 10014cb0 f i Packets.obj
 0001:00013cf0       ??1?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAE@XZ 10014cf0 f i Packets.obj
 0001:00013d00       ?_Reserve@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@IAEXI@Z 10014d00 f i Packets.obj
 0001:00013d80       ??0?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE@ABV01@@Z 10014d80 f i Packets.obj
 0001:00013e50       ?erase@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@0@Z 10014e50 f i Packets.obj
 0001:00013f10       ?_Erase@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@@Z 10014f10 f i Packets.obj
 0001:00013f80       ?erase@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@2@V32@0@Z 10014f80 f i Packets.obj
 0001:00014050       ?_Erase@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@@Z 10015050 f i Packets.obj
 0001:000140c0       ??1?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAE@XZ 100150c0 f i Packets.obj
 0001:000140d0       ?erase@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@0@Z 100150d0 f i Packets.obj
 0001:000141a0       ??1?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE@XZ 100151a0 f i Packets.obj
 0001:000141b0       ?_Reserve@?$vector@HV?$allocator@H@std@@@std@@IAEXI@Z 100151b0 f i Packets.obj
 0001:00014210       ??1sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAE@XZ 10015210 f i Packets.obj
 0001:00014280       ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV12@PB_WI@Z 10015280 f i Packets.obj
 0001:00014390       ?erase@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV12@II@Z 10015390 f i Packets.obj
 0001:00014410       ?_Grow@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE_NI_N@Z 10015410 f i Packets.obj
 0001:00014460       ?erase@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@@2@V32@0@Z 10015460 f i Packets.obj
 0001:00014500       ?allocate@?$allocator@H@std@@QAEPAHI@Z 10015500 f i Packets.obj
 0001:00014560       ?reserve@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAEXI@Z 10015560 f i Packets.obj
 0001:00014680       ?allocate@?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@QAEPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@I@Z 10015680 f i Engine.obj
 0001:00014680       ?allocate@?$allocator@UConfigNPC@@@std@@QAEPAUConfigNPC@@I@Z 10015680 f i Packets.obj
 0001:000146e0       ?erase@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@@Z 100156e0 f i Packets.obj
 0001:00014970       ?_Copy@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEXABV12@@Z 10015970 f i Packets.obj
 0001:000149e0       ?_Eqrange@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAE?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABH@Z 100159e0 f i Packets.obj
 0001:00014a70       ?erase@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@2@V32@@Z 10015a70 f i Packets.obj
 0001:00014cf0       ?_Eqrange@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IBE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 10015cf0 f i Packets.obj
 0001:00014cf0       ?_Eqrange@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IAE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 10015cf0 f i Packets.obj
 0001:00014e90       ?erase@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@@Z 10015e90 f i Packets.obj
 0001:00015110       ?_Eqrange@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IBE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABH@Z 10016110 f i Packets.obj
 0001:00015110       ?_Eqrange@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IAE?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@std@@V12@@2@ABH@Z 10016110 f i Packets.obj
 0001:00015190       ?reserve@?$vector@HV?$allocator@H@std@@@std@@QAEXI@Z 10016190 f i Packets.obj
 0001:00015210       ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10016210 f i Packets.obj
 0001:00015260       ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10016260 f i Packets.obj
 0001:000152b0       ??1_Sentry_base@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAE@XZ 100162b0 f i Packets.obj
 0001:000152d0       ?_Copy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEXII@Z 100162d0 f i Packets.obj
 0001:00015450       ?erase@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@@2@V32@@Z 10016450 f i Packets.obj
 0001:000156b0       ?_Copy@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@PAU342@0@Z 100166b0 f i Packets.obj
 0001:00015770       ?_Lrotate@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@@Z 10016770 f i Packets.obj
 0001:000157d0       ?_Rrotate@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@@Z 100167d0 f i Packets.obj
 0001:00015830       ?_Max@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@SAPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@PAU342@@Z 10016830 f i Packets.obj
 0001:00015850       ?_Lrotate@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@@Z 10016850 f i Packets.obj
 0001:000158a0       ?_Rrotate@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@@Z 100168a0 f i Packets.obj
 0001:000158f0       ?_Max@?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@SAPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@PAU342@@Z 100168f0 f i Packets.obj
 0001:00015910       ?_Erase@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@@Z 10016910 f i Packets.obj
 0001:00015970       ?_Lrotate@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@@Z 10016970 f i Packets.obj
 0001:000159c0       ?_Rrotate@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@@Z 100169c0 f i Packets.obj
 0001:00015a10       ?_Max@?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@SAPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@PAU342@@Z 10016a10 f i Packets.obj
 0001:00015a30       ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10016a30 f i Packets.obj
 0001:00015a80       ?allocate@?$allocator@_W@std@@QAEPA_WI@Z 10016a80 f i Packets.obj
 0001:00015ae0       ?_Lrotate@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@@Z 10016ae0 f i Packets.obj
 0001:00015b30       ?_Rrotate@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@IAEXPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@@Z 10016b30 f i Packets.obj
 0001:00015b80       ?_Max@?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@SAPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@PAU342@@Z 10016b80 f i Packets.obj
 0001:00015ba0       ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10016ba0 f i Packets.obj
 0001:00015bf0       ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QAV10@ABV10@@Z 10016bf0 f i Packets.obj
 0001:00015c30       ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QAV10@0@Z 10016c30 f i Packets.obj
 0001:00015ca0       ??$transform@V?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@P6AHH@Z@std@@YA?AV?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@0@V10@00P6AHH@Z@Z 10016ca0 f i Packets.obj
 0001:00015cd0       ??$?9DU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBD@Z 10016cd0 f i Packets.obj
 0001:00015d30       ??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z 10016d30 f i Packets.obj
 0001:00015f80       ??$?8DU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA_NPBDABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@@Z 10016f80 f i Packets.obj
 0001:00015fe0       ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBD$$QAV10@@Z 10016fe0 f i Packets.obj
 0001:00016030       ??$_Buynode@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z 10017030 f i Packets.obj
 0001:00016110       ?_Linsert@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@@std@@_N@2@PAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@_N@Z 10017110 f i Packets.obj
 0001:000161e0       ?_Linsert@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@std@@_N@2@PAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@_N@Z 100171e0 f i Packets.obj
 0001:000162c0       ?_Linsert@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAE?AU?$pair@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@std@@_N@2@PAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@_N@Z 100172c0 f i Packets.obj
 0001:00016450       ?_Insert@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@PAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@@Z 10017450 f i Packets.obj
 0001:00016580       ?insert@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@IABV12@II@Z 10017580 f i Packets.obj
 0001:000166e0       ?insert@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@IPBDI@Z 100176e0 f i Packets.obj
 0001:00016820       ?_Insert@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@IAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@@2@_NPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@2@1@Z 10017820 f i Packets.obj
 0001:00016a50       ?_Insert@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@@2@_NPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@1@Z 10017a50 f i Packets.obj
 0001:00016cb0       ?_Insert@?$_Tree@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IAE?AV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@2@_NPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@2@1@Z 10017cb0 f i Packets.obj
 0001:00016f10       ?_Linsert@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAE?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@std@@_N@2@PAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@_N@Z 10017f10 f i Packets.obj
 0001:00016ff0       ?_Insert@?$_Tree@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@IAE?AV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@2@_NPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@2@1@Z 10017ff0 f i Packets.obj
 0001:00017230       ??F?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10018230 f i Packets.obj
 0001:00017290       ??F?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10018290 f i Packets.obj
 0001:000172f0       ??F?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 100182f0 f i Packets.obj
 0001:00017350       ??F?$_Tree_unchecked_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@U_Iterator_base0@2@@std@@QAEAAV01@XZ 10018350 f i Packets.obj
 0001:000173b0       ??$_Construct@V?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXV?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@0Uforward_iterator_tag@1@@Z 100183b0 f i Packets.obj
 0001:00017530       ??$_Transform@PADV?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@P6AHH@Z@std@@YA?AV?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAD0V10@P6AHH@Z@Z 10018530 f i Packets.obj
 0001:00017570       ??$_Random_shuffle@PAHH@std@@YAXPAH00@Z 10018570 f i Packets.obj
 0001:00017600       ??$_Buynode@U?$pair@HPAD@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HPAD@1@@Z 10018600 f i Packets.obj
 0001:000176f0       ??$_Buynode@U?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z 100186f0 f i Packets.obj
 0001:000177d0       ??$_Buynode@AAH@?$_Tree_val@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@1@AAH@Z 100187d0 f i Packets.obj
 0001:00017840       ??$_Buynode@AAPBD@?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@1@AAPBD@Z 10018840 f i Packets.obj
 0001:00017930       ??$_Distance2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@std@@I@std@@YAXV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@@0@0AAIUbidirectional_iterator_tag@0@@Z 10018930 f i Packets.obj
 0001:000179a0       ??$_Buynode@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@1@@Z 100189a0 f i Packets.obj
 0001:00017a70       ??$_Distance2@V?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@std@@I@std@@YAXV?$_Tree_const_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@0@0AAIUbidirectional_iterator_tag@0@@Z 10018a70 f i Packets.obj
 0001:00017a70       ??$_Distance2@V?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@std@@I@std@@YAXV?$_Tree_iterator@V?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@@0@0AAIUbidirectional_iterator_tag@0@@Z 10018a70 f i Packets.obj
 0001:00017ae0       ??$_Uninit_copy@V?$_Vector_const_iterator@V?$_Vector_val@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@@std@@PAUConfigNPC@@V?$allocator@UConfigNPC@@@2@@std@@YAPAUConfigNPC@@V?$_Vector_const_iterator@V?$_Vector_val@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@@0@0PAU1@AAV?$allocator@UConfigNPC@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z 10018ae0 f i Packets.obj
 0001:00017b20       ??$_Uninit_copy@V?$_Vector_const_iterator@V?$_Vector_val@HV?$allocator@H@std@@@std@@@std@@PAHV?$allocator@H@2@@std@@YAPAHV?$_Vector_const_iterator@V?$_Vector_val@HV?$allocator@H@std@@@std@@@0@0PAHAAV?$allocator@H@0@U_Nonscalar_ptr_iterator_tag@0@@Z 10018b20 f i Packets.obj
 0001:00017b50       ??$_Uninit_move@PAUConfigNPC@@PAU1@V?$allocator@UConfigNPC@@@std@@U1@@std@@YAPAUConfigNPC@@PAU1@00AAV?$allocator@UConfigNPC@@@0@0U_Nonscalar_ptr_iterator_tag@0@@Z 10018b50 f i Packets.obj
 0001:00017b80       ?ConnectionFix@@YAHHPBDHHH@Z 10018b80 f   Protect.obj
 0001:00017ba0       ?MailFix@@YAHPAXPBDPAEPAH@Z 10018ba0 f   Protect.obj
 0001:00017bc0       ?FixGetFileAttributesA@@YGKPBD@Z 10018bc0 f   Protect.obj
 0001:00017bd0       ?MyAuction@@YGHH@Z         10018bd0 f   Protect.obj
 0001:00017c00       ?QuestCheck@@YAHHH@Z       10018c00 f   Protect.obj
 0001:00017c10       ?MacAddress@@YIXHPAXDI@Z   10018c10 f   Protect.obj
 0001:00017ce0       ?StartCheck@@YAHHHHHH@Z    10018ce0 f   Protect.obj
 0001:00017d00       ?ColorCrashFix@@YIPAIPAIPAX1KHHH@Z 10018d00 f   Protect.obj
 0001:00017d20       ?LoadDLL@@YIXPAX0PADPAUHINSTANCE__@@@Z 10018d20 f   Protect.obj
 0001:00017fa0       ?LoadDLL2@@YIXPAX0PA_WPAUHINSTANCE__@@@Z 10018fa0 f   Protect.obj
 0001:00018220       ?Disable@IProtect@@UAE_NXZ 10019220 f   Protect.obj
 0001:00018490       ?Create@?$Interface@VIProtect@@@@SAXXZ 10019490 f i Protect.obj
 0001:00018510       ??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z 10019510 f i Protect.obj
 0001:00018600       ?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PB_WHH@Z 10019600 f i Protect.obj
 0001:00018700       ??$_Transform@PADV?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@P6ADD@Z@std@@YA?AV?$_String_iterator@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAD0V10@P6ADD@Z@Z 10019700 f i Protect.obj
 0001:00018760       ?GetCaller@ITools@@UAEKI@Z 10019760 f   Tools.obj
 0001:000187a0       ?GetModuleInfo@ITools@@UAEPAU_IMAGE_NT_HEADERS@@PAUHINSTANCE__@@@Z 100197a0 f   Tools.obj
 0001:000187b0       ?GetExecutableFromPath@ITools@@UAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V23@@Z 100197b0 f   Tools.obj
 0001:00018850       ?GenerateSize@ITools@@UAAIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 10019850 f   Tools.obj
 0001:000188e0       ?GenerateSize@ITools@@UAEIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z 100198e0 f   Tools.obj
 0001:00018a70       ?Compile@ITools@@UAAXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 10019a70 f   Tools.obj
 0001:00018b00       ?Compile@ITools@@UAEXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z 10019b00 f   Tools.obj
 0001:00018d30       ?ParseData@ITools@@UAAPADPAD0ZZ 10019d30 f   Tools.obj
 0001:00018ea0       ?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z 10019ea0 f   Tools.obj
 0001:000191e0       ?SetMemoryEx@ITools@@UAEXPAXPBDI@Z 1001a1e0 f   Tools.obj
 0001:000191f0       ?SetMemoryEx@ITools@@UAEXKPBDI@Z 1001a1f0 f   Tools.obj
 0001:00019200       ?MemcpyEx@ITools@@UAEPAXPAX0I@Z 1001a200 f   Tools.obj
 0001:00019260       ?MemcpyExS@ITools@@UAEPAXPAX0I@Z 1001a260 f   Tools.obj
 0001:000192a0       ?MemcpyExD@ITools@@UAEPAXPAX0I@Z 1001a2a0 f   Tools.obj
 0001:000192e0       ?FillMemoryEx@ITools@@UAEXPAXEI@Z 1001a2e0 f   Tools.obj
 0001:000192e0       ?FillMemoryEx@ITools@@UAEXKEI@Z 1001a2e0 f   Tools.obj
 0001:00019320       ?Compile@ITools@@UAAPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 1001a320 f   Tools.obj
 0001:000193b0       ?Compile@ITools@@UAEPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z 1001a3b0 f   Tools.obj
 0001:000195e0       ?Intercept@ITools@@UAEKEPAX0I@Z 1001a5e0 f   Tools.obj
 0001:00019670       ?Create@?$Interface@VITools@@@@SAXXZ 1001a670 f i Tools.obj
 0001:000196f0       ?find_last_of@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIPBDII@Z 1001a6f0 f i Tools.obj
 0001:00019780       ?sha256@@YAIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 1001a780 f   Variables.obj
 0001:000197e0       ?modifyBuff@@YA_NHHH@Z     1001a7e0 f   Variables.obj
 0001:00019870       ?insertBuff@@YAXHHH@Z      1001a870 f   Variables.obj
 0001:000198f0       ?removeBuff@@YAXHH@Z       1001a8f0 f   Variables.obj
 0001:000199c0       ?buffTimer@@YAXXZ          1001a9c0 f   Variables.obj
 0001:00019b20       ??0?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAE@ABV01@@Z 1001ab20 f i Variables.obj
 0001:00019bf0       ?push_back@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAEXABUBuffConfig@@@Z 1001abf0 f i Variables.obj
 0001:00019ca0       ?erase@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAE?AV?$_Vector_iterator@V?$_Vector_val@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@@2@V?$_Vector_const_iterator@V?$_Vector_val@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@@2@@Z 1001aca0 f i Variables.obj
 0001:00019cf0       ?_Reserve@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@IAEXI@Z 1001acf0 f i Variables.obj
 0001:00019d70       ?reserve@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAEXI@Z 1001ad70 f i Variables.obj
 0001:00019e80       ?allocate@?$allocator@UBuffConfig@@@std@@QAEPAUBuffConfig@@I@Z 1001ae80 f i Variables.obj
 0001:00019ee0       ??$_Uninit_copy@V?$_Vector_const_iterator@V?$_Vector_val@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@@std@@PAUBuffConfig@@V?$allocator@UBuffConfig@@@2@@std@@YAPAUBuffConfig@@V?$_Vector_const_iterator@V?$_Vector_val@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@@0@0PAU1@AAV?$allocator@UBuffConfig@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z 1001aee0 f i Variables.obj
 0001:00019f20       ??$_Uninit_move@PAUBuffConfig@@PAU1@V?$allocator@UBuffConfig@@@std@@U1@@std@@YAPAUBuffConfig@@PAU1@00AAV?$allocator@UBuffConfig@@@0@0U_Nonscalar_ptr_iterator_tag@0@@Z 1001af20 f i Variables.obj
 0001:00019f45       ??1_Fac_node@std@@QAE@XZ   1001af45 f i msvcprt:locale0_implib.obj
 0001:00019f5b       ?_Facet_Register@facet@locale@std@@CAXPAV123@@Z 1001af5b f   msvcprt:locale0_implib.obj
 0001:00019f85       ??1_Fac_tidy_reg_t@std@@QAE@XZ 1001af85 f i msvcprt:locale0_implib.obj
 0001:00019fd6       ?imbue@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEXABVlocale@2@@Z 1001afd6 f   msvcprt:MSVCP100.dll
 0001:00019fdc       ?sync@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1001afdc f   msvcprt:MSVCP100.dll
 0001:00019fe2       ?setbuf@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEPAV12@PAD_J@Z 1001afe2 f   msvcprt:MSVCP100.dll
 0001:00019fe8       ?xsputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JPBD_J@Z 1001afe8 f   msvcprt:MSVCP100.dll
 0001:00019fee       ?xsgetn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JPAD_J@Z 1001afee f   msvcprt:MSVCP100.dll
 0001:00019ff4       ?uflow@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1001aff4 f   msvcprt:MSVCP100.dll
 0001:00019ffa       ?showmanyc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JXZ 1001affa f   msvcprt:MSVCP100.dll
 0001:0001a000       ?_Unlock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UAEXXZ 1001b000 f   msvcprt:MSVCP100.dll
 0001:0001a006       ?_Lock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UAEXXZ 1001b006 f   msvcprt:MSVCP100.dll
 0001:0001a00c       ??_U@YAPAXI@Z              1001b00c f   msvcprt:newaop_s.obj
 0001:0001a017       @__security_check_cookie@4 1001b017 f   MSVCRT:secchk.obj
 0001:0001a026       ?what@exception@std@@UBEPBDXZ 1001b026 f   MSVCRT:MSVCR100.dll
 0001:0001a02c       ??0exception@std@@QAE@ABV01@@Z 1001b02c f   MSVCRT:MSVCR100.dll
 0001:0001a032       __onexit                   1001b032 f   MSVCRT:atonexit.obj
 0001:0001a0d3       _atexit                    1001b0d3 f   MSVCRT:atonexit.obj
 0001:0001a0ea       ??3@YAXPAX@Z               1001b0ea f   MSVCRT:MSVCR100.dll
 0001:0001a0f0       ??_Etype_info@@UAEPAXI@Z   1001b0f0 f i MSVCRT:ti_inst.obj
 0001:0001a13c       ??2@YAPAXI@Z               1001b13c f   MSVCRT:MSVCR100.dll
 0001:0001a142       ___clean_type_info_names   1001b142 f   MSVCRT:tncleanup.obj
 0001:0001a14e       ??1bad_cast@std@@UAE@XZ    1001b14e f   MSVCRT:MSVCR100.dll
 0001:0001a154       ??0bad_cast@std@@QAE@ABV01@@Z 1001b154 f   MSVCRT:MSVCR100.dll
 0001:0001a1a8       __CRT_INIT@12              1001b1a8 f   MSVCRT:crtdll.obj
 0001:0001a4c8       __DllMainCRTStartup@12     1001b4c8 f   MSVCRT:crtdll.obj
 0001:0001a4ec       ___CxxFrameHandler3        1001b4ec f   MSVCRT:MSVCR100.dll
 0001:0001a4f2       __EH_prolog3               1001b4f2 f   MSVCRT:ehprolg3.obj
 0001:0001a525       __EH_epilog3               1001b525 f   MSVCRT:ehprolg3.obj
 0001:0001a539       ___report_gsfailure        1001b539 f   MSVCRT:gs_report.obj
 0001:0001a640       __unlock                   1001b640 f   MSVCRT:MSVCR100.dll
 0001:0001a646       ___dllonexit               1001b646 f   MSVCRT:MSVCR100.dll
 0001:0001a64c       __lock                     1001b64c f   MSVCRT:MSVCR100.dll
 0001:0001a660       __SEH_prolog4              1001b660 f   MSVCRT:sehprolg4.obj
 0001:0001a6a5       __SEH_epilog4              1001b6a5 f   MSVCRT:sehprolg4.obj
 0001:0001a6b9       __except_handler4          1001b6b9 f   MSVCRT:chandler4gs.obj
 0001:0001a6de       ?__ArrayUnwind@@YGXPAXIHP6EX0@Z@Z 1001b6de f   MSVCRT:ehvecdtr.obj
 0001:0001a73c       ??_M@YGXPAXIHP6EX0@Z@Z     1001b73c f   MSVCRT:ehvecdtr.obj
 0001:0001a7a0       ___clean_type_info_names_internal 1001b7a0 f   MSVCRT:MSVCR100.dll
 0001:0001a7a6       __RTC_Initialize           1001b7a6 f   MSVCRT:_initsect_.obj
 0001:0001a7cc       __RTC_Terminate            1001b7cc f   MSVCRT:_initsect_.obj
 0001:0001a800       __ValidateImageBase        1001b800 f   MSVCRT:pesect.obj
 0001:0001a840       __FindPESection            1001b840 f   MSVCRT:pesect.obj
 0001:0001a890       __IsNonwritableInCurrentImage 1001b890 f   MSVCRT:pesect.obj
 0001:0001a94c       __initterm                 1001b94c f   MSVCRT:MSVCR100.dll
 0001:0001a952       __initterm_e               1001b952 f   MSVCRT:MSVCR100.dll
 0001:0001a958       __amsg_exit                1001b958 f   MSVCRT:MSVCR100.dll
 0001:0001a95e       ___CppXcptFilter           1001b95e f   MSVCRT:MSVCR100.dll
 0001:0001a964       ___security_init_cookie    1001b964 f   MSVCRT:gs_support.obj
 0001:0001aa00       __crt_debugger_hook        1001ba00 f   MSVCRT:MSVCR100.dll
 0001:0001aa06       __except_handler4_common   1001ba06 f   MSVCRT:MSVCR100.dll
 0001:0001aa0c       ?terminate@@YAXXZ          1001ba0c f   MSVCRT:MSVCR100.dll
 0001:0001aa12       ?_type_info_dtor_internal_method@type_info@@QAEXXZ 1001ba12 f   MSVCRT:MSVCR100.dll
 0001:0001aa12       ??1type_info@@UAE@XZ       1001ba12 f   MSVCRT:MSVCR100.dll
 0001:0001aa20       _DetourCodeFromPointer@8   1001ba20 f   detours:detours.obj
 0001:0001aa40       ?detour_skip_jmp@@YAPAEPAEPAPAX@Z 1001ba40 f i detours:detours.obj
 0001:0001ac70       _DetourTransactionBegin@0  1001bc70 f   detours:detours.obj
 0001:0001ad20       _DetourTransactionAbort@0  1001bd20 f   detours:detours.obj
 0001:0001aed0       _DetourTransactionCommit@0 1001bed0 f   detours:detours.obj
 0001:0001aee0       _DetourTransactionCommitEx@4 1001bee0 f   detours:detours.obj
 0001:0001b2b0       ?detour_gen_jmp_immediate@@YAPAEPAE0@Z 1001c2b0 f i detours:detours.obj
 0001:0001b2f0       ?detour_gen_brk@@YAPAEPAE0@Z 1001c2f0 f i detours:detours.obj
 0001:0001b4c0       _DetourAttach@8            1001c4c0 f   detours:detours.obj
 0001:0001b4e0       _DetourAttachEx@20         1001c4e0 f   detours:detours.obj
 0001:0001b8e0       ?detour_does_code_end_function@@YAHPAE@Z 1001c8e0 f i detours:detours.obj
 0001:0001b9e0       ?detour_is_code_filler@@YAKPAE@Z 1001c9e0 f i detours:detours.obj
 0001:0001bee0       _DetourDetach@8            1001cee0 f   detours:detours.obj
 0001:0001c0b0       _DetourCopyInstruction@20  1001d0b0 f   detours:disasm.obj
 0001:0001c0f0       ??0CDetourDis@@QAE@PAPAEPAJ@Z 1001d0f0 f   detours:disasm.obj
 0001:0001c180       ?CopyInstruction@CDetourDis@@QAEPAEPAE0@Z 1001d180 f   detours:disasm.obj
 0001:0001c1e0       ?CopyBytes@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d1e0 f   detours:disasm.obj
 0001:0001c3c0       ?CopyBytesPrefix@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d3c0 f   detours:disasm.obj
 0001:0001c410       ?CopyBytesJump@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d410 f   detours:disasm.obj
 0001:0001c4f0       ?AdjustTarget@CDetourDis@@IAEPAEPAE0JJJ@Z 1001d4f0 f   detours:disasm.obj
 0001:0001c660       ?Invalid@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d660 f   detours:disasm.obj
 0001:0001c680       ?Copy0F@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d680 f   detours:disasm.obj
 0001:0001c6d0       ?Copy66@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d6d0 f   detours:disasm.obj
 0001:0001c700       ?Copy67@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d700 f   detours:disasm.obj
 0001:0001c730       ?CopyF6@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d730 f   detours:disasm.obj
 0001:0001c790       ?CopyF7@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d790 f   detours:disasm.obj
 0001:0001c7f0       ?CopyFF@CDetourDis@@IAEPAEPBUCOPYENTRY@1@PAE1@Z 1001d7f0 f   detours:disasm.obj
 0001:0001c898       _memset                    1001d898 f   MSVCRT:MSVCR100.dll
 0001:0001c89e       _memcpy                    1001d89e f   MSVCRT:MSVCR100.dll
 0001:0001c8b0       __CxxThrowException@8      1001d8b0 f   MSVCRT:MSVCR100.dll
 0001:0001c8c0       __chkstk                   1001d8c0 f   MSVCRT:chkstk.obj
 0001:0001c8c0       __alloca_probe             1001d8c0     MSVCRT:chkstk.obj
 0001:0001c8f0       __allmul                   1001d8f0 f   MSVCRT:llmul.obj
 0001:0001c930       __aulldiv                  1001d930 f   MSVCRT:ulldiv.obj
 0002:00000000       __imp__RegOpenKeyExW@20    10020000     advapi32:ADVAPI32.dll
 0002:00000004       __imp__RegQueryValueExW@24 10020004     advapi32:ADVAPI32.dll
 0002:00000008       \177ADVAPI32_NULL_THUNK_DATA 10020008     advapi32:ADVAPI32.dll
 0002:0000000c       __imp__GetEnvironmentVariableA@12 1002000c     kernel32:KERNEL32.dll
 0002:00000010       __imp__InterlockedExchange@8 10020010     kernel32:KERNEL32.dll
 0002:00000014       __imp__GetProcAddress@8    10020014     kernel32:KERNEL32.dll
 0002:00000018       __imp__LoadLibraryA@4      10020018     kernel32:KERNEL32.dll
 0002:0000001c       __imp__InterlockedExchangeAdd@8 1002001c     kernel32:KERNEL32.dll
 0002:00000020       __imp__VirtualProtect@16   10020020     kernel32:KERNEL32.dll
 0002:00000024       __imp__GetCurrentProcessId@0 10020024     kernel32:KERNEL32.dll
 0002:00000028       __imp__InitializeCriticalSection@4 10020028     kernel32:KERNEL32.dll
 0002:0000002c       __imp__LeaveCriticalSection@4 1002002c     kernel32:KERNEL32.dll
 0002:00000030       __imp__EnterCriticalSection@4 10020030     kernel32:KERNEL32.dll
 0002:00000034       __imp__DeleteCriticalSection@4 10020034     kernel32:KERNEL32.dll
 0002:00000038       __imp__GetComputerNameA@8  10020038     kernel32:KERNEL32.dll
 0002:0000003c       __imp__GetConsoleWindow@0  1002003c     kernel32:KERNEL32.dll
 0002:00000040       __imp__GetVolumeInformationA@32 10020040     kernel32:KERNEL32.dll
 0002:00000044       __imp__GetPrivateProfileStringA@24 10020044     kernel32:KERNEL32.dll
 0002:00000048       __imp__GetModuleFileNameA@12 10020048     kernel32:KERNEL32.dll
 0002:0000004c       __imp__SetLastError@4      1002004c     kernel32:KERNEL32.dll
 0002:00000050       __imp__VirtualAlloc@16     10020050     kernel32:KERNEL32.dll
 0002:00000054       __imp__Sleep@4             10020054     kernel32:KERNEL32.dll
 0002:00000058       __imp__GetTickCount@0      10020058     kernel32:KERNEL32.dll
 0002:0000005c       __imp__FreeLibrary@4       1002005c     kernel32:KERNEL32.dll
 0002:00000060       __imp__GetLastError@0      10020060     kernel32:KERNEL32.dll
 0002:00000064       __imp__VirtualFree@12      10020064     kernel32:KERNEL32.dll
 0002:00000068       __imp__GetCurrentProcess@0 10020068     kernel32:KERNEL32.dll
 0002:0000006c       __imp__EncodePointer@4     1002006c     kernel32:KERNEL32.dll
 0002:00000070       __imp__DecodePointer@4     10020070     kernel32:KERNEL32.dll
 0002:00000074       __imp__InterlockedCompareExchange@12 10020074     kernel32:KERNEL32.dll
 0002:00000078       __imp__TerminateProcess@8  10020078     kernel32:KERNEL32.dll
 0002:0000007c       __imp__UnhandledExceptionFilter@4 1002007c     kernel32:KERNEL32.dll
 0002:00000080       __imp__SetUnhandledExceptionFilter@4 10020080     kernel32:KERNEL32.dll
 0002:00000084       __imp__IsDebuggerPresent@0 10020084     kernel32:KERNEL32.dll
 0002:00000088       __imp__QueryPerformanceCounter@4 10020088     kernel32:KERNEL32.dll
 0002:0000008c       __imp__GetCurrentThreadId@0 1002008c     kernel32:KERNEL32.dll
 0002:00000090       __imp__GetSystemTimeAsFileTime@4 10020090     kernel32:KERNEL32.dll
 0002:00000094       __imp__VirtualQuery@12     10020094     kernel32:KERNEL32.dll
 0002:00000098       __imp__ResumeThread@4      10020098     kernel32:KERNEL32.dll
 0002:0000009c       __imp__FlushInstructionCache@12 1002009c     kernel32:KERNEL32.dll
 0002:000000a0       __imp__SetThreadContext@8  100200a0     kernel32:KERNEL32.dll
 0002:000000a4       __imp__GetThreadContext@8  100200a4     kernel32:KERNEL32.dll
 0002:000000a8       \177KERNEL32_NULL_THUNK_DATA 100200a8     kernel32:KERNEL32.dll
 0002:000000ac       __imp_?unshift@?$codecvt@DDH@std@@QBEHAAHPAD1AAPAD@Z 100200ac     msvcprt:MSVCP100.dll
 0002:000000b0       __imp_??0?$basic_iostream@DU?$char_traits@D@std@@@std@@QAE@PAV?$basic_streambuf@DU?$char_traits@D@std@@@1@@Z 100200b0     msvcprt:MSVCP100.dll
 0002:000000b4       __imp_??0?$basic_istream@DU?$char_traits@D@std@@@std@@QAE@PAV?$basic_streambuf@DU?$char_traits@D@std@@@1@_N@Z 100200b4     msvcprt:MSVCP100.dll
 0002:000000b8       __imp_??0?$basic_ios@DU?$char_traits@D@std@@@std@@IAE@XZ 100200b8     msvcprt:MSVCP100.dll
 0002:000000bc       __imp_?_Pninc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IAEPADXZ 100200bc     msvcprt:MSVCP100.dll
 0002:000000c0       __imp_?setg@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IAEXPAD00@Z 100200c0     msvcprt:MSVCP100.dll
 0002:000000c4       __imp_??0?$basic_streambuf@DU?$char_traits@D@std@@@std@@IAE@XZ 100200c4     msvcprt:MSVCP100.dll
 0002:000000c8       __imp_?out@?$codecvt@DDH@std@@QBEHAAHPBD1AAPBDPAD3AAPAD@Z 100200c8     msvcprt:MSVCP100.dll
 0002:000000cc       __imp_?in@?$codecvt@DDH@std@@QBEHAAHPBD1AAPBDPAD3AAPAD@Z 100200cc     msvcprt:MSVCP100.dll
 0002:000000d0       __imp_??1?$basic_iostream@DU?$char_traits@D@std@@@std@@UAE@XZ 100200d0     msvcprt:MSVCP100.dll
 0002:000000d4       __imp_??1?$basic_istream@DU?$char_traits@D@std@@@std@@UAE@XZ 100200d4     msvcprt:MSVCP100.dll
 0002:000000d8       __imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QAEAAV01@H@Z 100200d8     msvcprt:MSVCP100.dll
 0002:000000dc       __imp_?setstate@?$basic_ios@DU?$char_traits@D@std@@@std@@QAEXH_N@Z 100200dc     msvcprt:MSVCP100.dll
 0002:000000e0       __imp_??1?$basic_ios@DU?$char_traits@D@std@@@std@@UAE@XZ 100200e0     msvcprt:MSVCP100.dll
 0002:000000e4       __imp_?imbue@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEXABVlocale@2@@Z 100200e4     msvcprt:MSVCP100.dll
 0002:000000e8       __imp_?sync@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEHXZ 100200e8     msvcprt:MSVCP100.dll
 0002:000000ec       __imp_?setbuf@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEPAV12@PAD_J@Z 100200ec     msvcprt:MSVCP100.dll
 0002:000000f0       __imp_?xsputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JPBD_J@Z 100200f0     msvcprt:MSVCP100.dll
 0002:000000f4       __imp_?xsgetn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JPAD_J@Z 100200f4     msvcprt:MSVCP100.dll
 0002:000000f8       __imp_?uflow@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAEHXZ 100200f8     msvcprt:MSVCP100.dll
 0002:000000fc       __imp_?showmanyc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MAE_JXZ 100200fc     msvcprt:MSVCP100.dll
 0002:00000100       __imp_?_Unlock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UAEXXZ 10020100     msvcprt:MSVCP100.dll
 0002:00000104       __imp_?_Lock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UAEXXZ 10020104     msvcprt:MSVCP100.dll
 0002:00000108       __imp_??1?$basic_streambuf@DU?$char_traits@D@std@@@std@@UAE@XZ 10020108     msvcprt:MSVCP100.dll
 0002:0000010c       __imp_?always_noconv@codecvt_base@std@@QBE_NXZ 1002010c     msvcprt:MSVCP100.dll
 0002:00000110       __imp_?_Decref@facet@locale@std@@QAEPAV123@XZ 10020110     msvcprt:MSVCP100.dll
 0002:00000114       __imp_?_Incref@facet@locale@std@@QAEXXZ 10020114     msvcprt:MSVCP100.dll
 0002:00000118       __imp_??Bid@locale@std@@QAEIXZ 10020118     msvcprt:MSVCP100.dll
 0002:0000011c       __imp_?uncaught_exception@std@@YA_NXZ 1002011c     msvcprt:MSVCP100.dll
 0002:00000120       __imp_?sputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QAE_JPBD_J@Z 10020120     msvcprt:MSVCP100.dll
 0002:00000124       __imp_?_Osfx@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAEXXZ 10020124     msvcprt:MSVCP100.dll
 0002:00000128       __imp_?clear@?$basic_ios@DU?$char_traits@D@std@@@std@@QAEXH_N@Z 10020128     msvcprt:MSVCP100.dll
 0002:0000012c       __imp_?sputc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QAEHD@Z 1002012c     msvcprt:MSVCP100.dll
 0002:00000130       __imp_?flush@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAEAAV12@XZ 10020130     msvcprt:MSVCP100.dll
 0002:00000134       __imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QAEAAV01@P6AAAV01@AAV01@@Z@Z 10020134     msvcprt:MSVCP100.dll
 0002:00000138       __imp_?endl@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@1@AAV21@@Z 10020138     msvcprt:MSVCP100.dll
 0002:0000013c       __imp_?_Fiopen@std@@YAPAU_iobuf@@PB_WHH@Z 1002013c     msvcprt:MSVCP100.dll
 0002:00000140       __imp_?getloc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QBE?AVlocale@2@XZ 10020140     msvcprt:MSVCP100.dll
 0002:00000144       __imp_?_Init@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IAEXXZ 10020144     msvcprt:MSVCP100.dll
 0002:00000148       __imp_?_Getcat@?$codecvt@DDH@std@@SAIPAPBVfacet@locale@2@PBV42@@Z 10020148     msvcprt:MSVCP100.dll
 0002:0000014c       __imp_??_7?$basic_istream@DU?$char_traits@D@std@@@std@@6B@ 1002014c     msvcprt:MSVCP100.dll
 0002:00000150       __imp_?id@?$codecvt@DDH@std@@2V0locale@2@A 10020150     msvcprt:MSVCP100.dll
 0002:00000154       __imp_?_Fiopen@std@@YAPAU_iobuf@@PBDHH@Z 10020154     msvcprt:MSVCP100.dll
 0002:00000158       __imp_?_Getgloballocale@locale@std@@CAPAV_Locimp@12@XZ 10020158     msvcprt:MSVCP100.dll
 0002:0000015c       __imp_??0_Lockit@std@@QAE@H@Z 1002015c     msvcprt:MSVCP100.dll
 0002:00000160       __imp_??1_Lockit@std@@QAE@XZ 10020160     msvcprt:MSVCP100.dll
 0002:00000164       __imp_?_BADOFF@std@@3_JB   10020164     msvcprt:MSVCP100.dll
 0002:00000168       __imp_?_Xlength_error@std@@YAXPBD@Z 10020168     msvcprt:MSVCP100.dll
 0002:0000016c       __imp_?_Xout_of_range@std@@YAXPBD@Z 1002016c     msvcprt:MSVCP100.dll
 0002:00000170       \177MSVCP100_NULL_THUNK_DATA 10020170     msvcprt:MSVCP100.dll
 0002:00000174       __imp___unlock_file        10020174     MSVCRT:MSVCR100.dll
 0002:00000178       __imp__ungetc              10020178     MSVCRT:MSVCR100.dll
 0002:0000017c       __imp__fgetpos             1002017c     MSVCRT:MSVCR100.dll
 0002:00000180       __imp___fseeki64           10020180     MSVCRT:MSVCR100.dll
 0002:00000184       __imp__fflush              10020184     MSVCRT:MSVCR100.dll
 0002:00000188       __imp__fgetc               10020188     MSVCRT:MSVCR100.dll
 0002:0000018c       __imp___beginthread        1002018c     MSVCRT:MSVCR100.dll
 0002:00000190       __imp__fsetpos             10020190     MSVCRT:MSVCR100.dll
 0002:00000194       __imp__fgets               10020194     MSVCRT:MSVCR100.dll
 0002:00000198       __imp__fopen               10020198     MSVCRT:MSVCR100.dll
 0002:0000019c       __imp__setvbuf             1002019c     MSVCRT:MSVCR100.dll
 0002:000001a0       __imp___lock_file          100201a0     MSVCRT:MSVCR100.dll
 0002:000001a4       __imp__fread               100201a4     MSVCRT:MSVCR100.dll
 0002:000001a8       __imp__memcpy_s            100201a8     MSVCRT:MSVCR100.dll
 0002:000001ac       __imp__fwrite              100201ac     MSVCRT:MSVCR100.dll
 0002:000001b0       __imp__fclose              100201b0     MSVCRT:MSVCR100.dll
 0002:000001b4       __imp__exit                100201b4     MSVCRT:MSVCR100.dll
 0002:000001b8       __imp__abort               100201b8     MSVCRT:MSVCR100.dll
 0002:000001bc       __imp__tolower             100201bc     MSVCRT:MSVCR100.dll
 0002:000001c0       __imp_??_V@YAXPAX@Z        100201c0     MSVCRT:MSVCR100.dll
 0002:000001c4       __imp__rand                100201c4     MSVCRT:MSVCR100.dll
 0002:000001c8       __imp__srand               100201c8     MSVCRT:MSVCR100.dll
 0002:000001cc       __imp__toupper             100201cc     MSVCRT:MSVCR100.dll
 0002:000001d0       __imp___time64             100201d0     MSVCRT:MSVCR100.dll
 0002:000001d4       __imp____CxxFrameHandler3  100201d4     MSVCRT:MSVCR100.dll
 0002:000001d8       __imp___unlock             100201d8     MSVCRT:MSVCR100.dll
 0002:000001dc       __imp____dllonexit         100201dc     MSVCRT:MSVCR100.dll
 0002:000001e0       __imp___lock               100201e0     MSVCRT:MSVCR100.dll
 0002:000001e4       __imp___onexit             100201e4     MSVCRT:MSVCR100.dll
 0002:000001e8       __imp____clean_type_info_names_internal 100201e8     MSVCRT:MSVCR100.dll
 0002:000001ec       __imp_?name@type_info@@QBEPBDPAU__type_info_node@@@Z 100201ec     MSVCRT:MSVCR100.dll
 0002:000001ec       __imp_?_name_internal_method@type_info@@QBEPBDPAU__type_info_node@@@Z 100201ec     MSVCRT:MSVCR100.dll
 0002:000001f0       __imp___malloc_crt         100201f0     MSVCRT:MSVCR100.dll
 0002:000001f4       __imp__free                100201f4     MSVCRT:MSVCR100.dll
 0002:000001f8       __imp___encoded_null       100201f8     MSVCRT:MSVCR100.dll
 0002:000001fc       __imp___initterm           100201fc     MSVCRT:MSVCR100.dll
 0002:00000200       __imp___initterm_e         10020200     MSVCRT:MSVCR100.dll
 0002:00000204       __imp___amsg_exit          10020204     MSVCRT:MSVCR100.dll
 0002:00000208       __imp____CppXcptFilter     10020208     MSVCRT:MSVCR100.dll
 0002:0000020c       __imp___crt_debugger_hook  1002020c     MSVCRT:MSVCR100.dll
 0002:00000210       __imp___except_handler4_common 10020210     MSVCRT:MSVCR100.dll
 0002:00000214       __imp_?terminate@@YAXXZ    10020214     MSVCRT:MSVCR100.dll
 0002:00000218       __imp_?_type_info_dtor_internal_method@type_info@@QAEXXZ 10020218     MSVCRT:MSVCR100.dll
 0002:0000021c       __imp___endthread          1002021c     MSVCRT:MSVCR100.dll
 0002:00000220       __imp_??0bad_cast@std@@QAE@ABV01@@Z 10020220     MSVCRT:MSVCR100.dll
 0002:00000224       __imp_??0bad_cast@std@@QAE@PBD@Z 10020224     MSVCRT:MSVCR100.dll
 0002:00000228       __imp_??1bad_cast@std@@UAE@XZ 10020228     MSVCRT:MSVCR100.dll
 0002:0000022c       __imp__sprintf             1002022c     MSVCRT:MSVCR100.dll
 0002:00000230       __imp__fputc               10020230     MSVCRT:MSVCR100.dll
 0002:00000234       __imp___stricmp            10020234     MSVCRT:MSVCR100.dll
 0002:00000238       __imp__atoi                10020238     MSVCRT:MSVCR100.dll
 0002:0000023c       __imp_??2@YAPAXI@Z         1002023c     MSVCRT:MSVCR100.dll
 0002:00000240       __imp_??3@YAXPAX@Z         10020240     MSVCRT:MSVCR100.dll
 0002:00000244       __imp__isalnum             10020244     MSVCRT:MSVCR100.dll
 0002:00000248       __imp__memchr              10020248     MSVCRT:MSVCR100.dll
 0002:0000024c       __imp__memmove             1002024c     MSVCRT:MSVCR100.dll
 0002:00000250       __imp_??0exception@std@@QAE@ABV01@@Z 10020250     MSVCRT:MSVCR100.dll
 0002:00000254       __imp_??0exception@std@@QAE@ABQBD@Z 10020254     MSVCRT:MSVCR100.dll
 0002:00000258       __imp_??1exception@std@@UAE@XZ 10020258     MSVCRT:MSVCR100.dll
 0002:0000025c       __imp_?what@exception@std@@UBEPBDXZ 1002025c     MSVCRT:MSVCR100.dll
 0002:00000260       __imp__memcpy              10020260     MSVCRT:MSVCR100.dll
 0002:00000264       __imp__memset              10020264     MSVCRT:MSVCR100.dll
 0002:00000268       __imp___CxxThrowException@8 10020268     MSVCRT:MSVCR100.dll
 0002:0000026c       \177MSVCR100_NULL_THUNK_DATA 1002026c     MSVCRT:MSVCR100.dll
 0002:00000270       __imp__ShellExecuteA@24    10020270     shell32:SHELL32.dll
 0002:00000274       \177SHELL32_NULL_THUNK_DATA 10020274     shell32:SHELL32.dll
 0002:00000278       __imp__GetWindowThreadProcessId@8 10020278     user32:USER32.dll
 0002:0000027c       __imp__GetCursorPos@4      1002027c     user32:USER32.dll
 0002:00000280       __imp__MessageBoxA@16      10020280     user32:USER32.dll
 0002:00000284       \177USER32_NULL_THUNK_DATA 10020284     user32:USER32.dll
 0002:00000288       ___xc_a                    10020288     MSVCRT:cinitexe.obj
 0002:000002f0       ___xc_z                    100202f0     MSVCRT:cinitexe.obj
 0002:000002f4       ___xi_a                    100202f4     MSVCRT:cinitexe.obj
 0002:000002fc       ___xi_z                    100202fc     MSVCRT:cinitexe.obj
 0002:00000320       ??_7type_info@@6B@         10020320     MSVCRT:ti_inst.obj
 0002:00000324       __pDefaultRawDllMain       10020324     MSVCRT:crtdll.obj
 0002:00000324       __pRawDllMain              10020324     MSVCRT:crtdll.obj
 0002:00000330       ?s_rbModRm@CDetourDis@@1QBEB 10020330     detours:disasm.obj
 0002:00000430       ?s_rceCopyTable@CDetourDis@@1QBUCOPYENTRY@1@B 10020430     detours:disasm.obj
 0002:00000c38       ?s_rceCopyTable0F@CDetourDis@@1QBUCOPYENTRY@1@B 10020c38     detours:disasm.obj
 0002:00001440       ??_C@_0EB@OJNBEEGM@ZYXWVUTSRQPONMLKJIHGFEDCBAabcdef@ 10021440     base64.obj
 0002:00001484       ??_C@_0BA@JFNIOLAK@string?5too?5long?$AA@ 10021484     base64.obj
 0002:00001494       ??_C@_0BI@CFPLBAOH@invalid?5string?5position?$AA@ 10021494     base64.obj
 0002:000014b0       ??_7bad_alloc@std@@6B@     100214b0     base64.obj
 0002:000014b8       ??_C@_0BM@PAPJHAGI@invalid?5map?1set?$DMT?$DO?5iterator?$AA@ 100214b8     Buff.obj
 0002:000014d4       ??_C@_0BE@JONHPENG@map?1set?$DMT?$DO?5too?5long?$AA@ 100214d4     Buff.obj
 0002:000014e8       ??_C@_01CLKCMJKC@?5?$AA@   100214e8     Chatbox.obj
 0002:000014ec       ??_C@_03KCHOJKKI@set?$AA@  100214ec     Chatbox.obj
 0002:000014f0       ??_C@_04CLCEDBPF@time?$AA@ 100214f0     Chatbox.obj
 0002:000014f8       ??_C@_06MNPOGNLM@hideme?$AA@ 100214f8     Chatbox.obj
 0002:00001500       ??_C@_06HDHONAHI@hideui?$AA@ 10021500     Chatbox.obj
 0002:00001508       ??_C@_07DCDAPOHN@hidefog?$AA@ 10021508     Chatbox.obj
 0002:00001510       ??_C@_0M@EEJLCOJH@hideterrain?$AA@ 10021510     Chatbox.obj
 0002:0000151c       ??_C@_09DPLCJINN@hidemodel?$AA@ 1002151c     Chatbox.obj
 0002:00001528       ??_C@_09KGJHGPAB@hidewater?$AA@ 10021528     Chatbox.obj
 0002:00001534       ??_C@_04PCJFHION@help?$AA@ 10021534     Chatbox.obj
 0002:0000153c       ??_C@_0BD@FOEDKPNF@?5?5?5?1set?5time?5?$FLnum?$FN?$AA@ 1002153c     Chatbox.obj
 0002:00001550       ??_C@_0P@BPAMPDFG@?5?5?5?1set?5hideme?$AA@ 10021550     Chatbox.obj
 0002:00001560       ??_C@_0P@KBIMEOJC@?5?5?5?1set?5hideui?$AA@ 10021560     Chatbox.obj
 0002:00001570       ??_C@_0BA@HCDNAHIF@?5?5?5?1set?5hidefog?$AA@ 10021570     Chatbox.obj
 0002:00001580       ??_C@_0BC@LOJDAOAB@?5?5?5?1set?5hidewater?$AA@ 10021580     Chatbox.obj
 0002:00001594       ??_C@_0BC@CHLGPJNN@?5?5?5?1set?5hidemodel?$AA@ 10021594     Chatbox.obj
 0002:000015a8       ??_C@_0BE@HOCOGHFN@?5?5?5?1set?5hideterrain?$AA@ 100215a8     Chatbox.obj
 0002:000015bc       ??_C@_0BB@MOGOBHAF@list?$DMT?$DO?5too?5long?$AA@ 100215bc     Chatbox.obj
 0002:000015d0       ??_C@_0P@DNOJKFOD@?4?2advapi32?4dll?$AA@ 100215d0     Engine.obj
 0002:000015e0       ??_C@_0L@IOPNHGBO@A_SHAFinal?$AA@ 100215e0     Engine.obj
 0002:000015ec       ??_C@_09CKOAJFBH@A_SHAInit?$AA@ 100215ec     Engine.obj
 0002:000015f8       ??_C@_0M@MIGBGAJO@A_SHAUpdate?$AA@ 100215f8     Engine.obj
 0002:00001604       ??_C@_0BF@JJPEBFB@AbortSystemShutdownA?$AA@ 10021604     Engine.obj
 0002:0000161c       ??_C@_0BF@BFAHPEIG@AbortSystemShutdownW?$AA@ 1002161c     Engine.obj
 0002:00001634       ??_C@_0M@NFEPJBAB@AccessCheck?$AA@ 10021634     Engine.obj
 0002:00001640       ??_C@_0BK@OHCBLFDC@AccessCheckAndAuditAlarmA?$AA@ 10021640     Engine.obj
 0002:0000165c       ??_C@_0BK@PLLJAAOF@AccessCheckAndAuditAlarmW?$AA@ 1002165c     Engine.obj
 0002:00001678       ??_C@_0BC@HGJCLED@AccessCheckByType?$AA@ 10021678     Engine.obj
 0002:0000168c       ??_C@_0CA@HKIFAHBC@AccessCheckByTypeAndAuditAlarmA?$AA@ 1002168c     Engine.obj
 0002:000016ac       ??_C@_0CA@GGBNLCMF@AccessCheckByTypeAndAuditAlarmW?$AA@ 100216ac     Engine.obj
 0002:000016cc       ??_C@_0BM@FPMEOAIK@AccessCheckByTypeResultList?$AA@ 100216cc     Engine.obj
 0002:000016e8       ??_C@_0CK@MIBDGCMJ@AccessCheckByTypeResultListAndAu@ 100216e8     Engine.obj
 0002:00001714       ??_C@_0DC@MNKLACOI@AccessCheckByTypeResultListAndAu@ 10021714     Engine.obj
 0002:00001748       ??_C@_0DC@NBDDLHDP@AccessCheckByTypeResultListAndAu@ 10021748     Engine.obj
 0002:0000177c       ??_C@_0CK@NEILNHBO@AccessCheckByTypeResultListAndAu@ 1002177c     Engine.obj
 0002:000017a8       ??_C@_0BE@IPKPLIM@AddAccessAllowedAce?$AA@ 100217a8     Engine.obj
 0002:000017bc       ??_C@_0BG@CMOOEIPM@AddAccessAllowedAceEx?$AA@ 100217bc     Engine.obj
 0002:000017d4       ??_C@_0BK@ELMDKJLC@AddAccessAllowedObjectAce?$AA@ 100217d4     Engine.obj
 0002:000017f0       ??_C@_0BD@EIGAJLOG@AddAccessDeniedAce?$AA@ 100217f0     Engine.obj
 0002:00001804       ??_C@_0BF@POEOOKBD@AddAccessDeniedAceEx?$AA@ 10021804     Engine.obj
 0002:0000181c       ??_C@_0BJ@NJPJHOMD@AddAccessDeniedObjectAce?$AA@ 1002181c     Engine.obj
 0002:00001838       ??_C@_06OILBHEO@AddAce?$AA@ 10021838     Engine.obj
 0002:00001840       ??_C@_0BC@NJCLNIDJ@AddAuditAccessAce?$AA@ 10021840     Engine.obj
 0002:00001854       ??_C@_0BE@BHDLLCKC@AddAuditAccessAceEx?$AA@ 10021854     Engine.obj
 0002:00001868       ??_C@_0BI@JFAGPPFL@AddAuditAccessObjectAce?$AA@ 10021868     Engine.obj
 0002:00001880       ??_C@_0BC@OGEIEAMP@AddConditionalAce?$AA@ 10021880     Engine.obj
 0002:00001894       ??_C@_0BA@IAPOPGMD@AddMandatoryAce?$AA@ 10021894     Engine.obj
 0002:000018a4       ??_C@_0BI@IMJKPDAC@AddUsersToEncryptedFile?$AA@ 100218a4     Engine.obj
 0002:000018bc       ??_C@_0BK@IHDFPBGL@AddUsersToEncryptedFileEx?$AA@ 100218bc     Engine.obj
 0002:000018d8       ??_C@_0BC@PFJMKPA@AdjustTokenGroups?$AA@ 100218d8     Engine.obj
 0002:000018ec       ??_C@_0BG@CGCLCMLG@AdjustTokenPrivileges?$AA@ 100218ec     Engine.obj
 0002:00001904       ??_C@_0BJ@ECIKFEOL@AllocateAndInitializeSid?$AA@ 10021904     Engine.obj
 0002:00001920       ??_C@_0BI@KMFHHBON@AllocateLocallyUniqueId?$AA@ 10021920     Engine.obj
 0002:00001938       ??_C@_0BG@CJAGODMN@AreAllAccessesGranted?$AA@ 10021938     Engine.obj
 0002:00001950       ??_C@_0BG@MPCNPNLP@AreAnyAccessesGranted?$AA@ 10021950     Engine.obj
 0002:00001968       ??_C@_0CB@FNLKDJJG@AuditComputeEffectivePolicyBySid@ 10021968     Engine.obj
 0002:0000198c       ??_C@_0CD@HFKGCIC@AuditComputeEffectivePolicyByTok@ 1002198c     Engine.obj
 0002:000019b0       ??_C@_0BJ@HBIFCNPP@AuditEnumerateCategories?$AA@ 100219b0     Engine.obj
 0002:000019cc       ??_C@_0BM@CJKEODPG@AuditEnumeratePerUserPolicy?$AA@ 100219cc     Engine.obj
 0002:000019e8       ??_C@_0BM@JHBJKFB@AuditEnumerateSubCategories?$AA@ 100219e8     Engine.obj
 0002:00001a04       ??_C@_09ENBGMGPK@AuditFree?$AA@ 10021a04     Engine.obj
 0002:00001a10       ??_C@_0CG@DODCJIOM@AuditLookupCategoryGuidFromCateg@ 10021a10     Engine.obj
 0002:00001a38       ??_C@_0CG@CDHCCMOK@AuditLookupCategoryIdFromCategor@ 10021a38     Engine.obj
 0002:00001a60       ??_C@_0BJ@BMGICHNF@AuditLookupCategoryNameA?$AA@ 10021a60     Engine.obj
 0002:00001a7c       ??_C@_0BJ@PAJCAC@AuditLookupCategoryNameW?$AA@ 10021a7c     Engine.obj
 0002:00001a98       ??_C@_0BM@DBOFFDBG@AuditLookupSubCategoryNameA?$AA@ 10021a98     Engine.obj
 0002:00001ab4       ??_C@_0BM@CNHNOGMB@AuditLookupSubCategoryNameW?$AA@ 10021ab4     Engine.obj
 0002:00001ad0       ??_C@_0BG@KFIABCJI@AuditQueryGlobalSaclA?$AA@ 10021ad0     Engine.obj
 0002:00001ae8       ??_C@_0BG@LJBIKHEP@AuditQueryGlobalSaclW?$AA@ 10021ae8     Engine.obj
 0002:00001b00       ??_C@_0BI@EPDFCBNP@AuditQueryPerUserPolicy?$AA@ 10021b00     Engine.obj
 0002:00001b18       ??_C@_0BD@KBBIEIGP@AuditQuerySecurity?$AA@ 10021b18     Engine.obj
 0002:00001b2c       ??_C@_0BH@HKDGHHMK@AuditQuerySystemPolicy?$AA@ 10021b2c     Engine.obj
 0002:00001b44       ??_C@_0BE@ENCOIDAK@AuditSetGlobalSaclA?$AA@ 10021b44     Engine.obj
 0002:00001b58       ??_C@_0BE@FBLGDGNN@AuditSetGlobalSaclW?$AA@ 10021b58     Engine.obj
 0002:00001b6c       ??_C@_0BG@ILEKICDL@AuditSetPerUserPolicy?$AA@ 10021b6c     Engine.obj
 0002:00001b84       ??_C@_0BB@BGNDAPBG@AuditSetSecurity?$AA@ 10021b84     Engine.obj
 0002:00001b98       ??_C@_0BF@GENPCLDD@AuditSetSystemPolicy?$AA@ 10021b98     Engine.obj
 0002:00001bb0       ??_C@_0BA@GILFMDNH@BackupEventLogA?$AA@ 10021bb0     Engine.obj
 0002:00001bc0       ??_C@_0BA@HECNHGAA@BackupEventLogW?$AA@ 10021bc0     Engine.obj
 0002:00001bd0       ??_C@_0BN@NFPBIILI@BuildExplicitAccessWithNameA?$AA@ 10021bd0     Engine.obj
 0002:00001bf0       ??_C@_0BN@MJGJDNGP@BuildExplicitAccessWithNameW?$AA@ 10021bf0     Engine.obj
 0002:00001c10       ??_C@_0CI@MIPMKCOF@BuildImpersonateExplicitAccessWi@ 10021c10     Engine.obj
 0002:00001c38       ??_C@_0CI@NEGEBHDC@BuildImpersonateExplicitAccessWi@ 10021c38     Engine.obj
 0002:00001c60       ??_C@_0BJ@BJHPEFMA@BuildImpersonateTrusteeA?$AA@ 10021c60     Engine.obj
 0002:00001c7c       ??_C@_0BJ@FOHPABH@BuildImpersonateTrusteeW?$AA@ 10021c7c     Engine.obj
 0002:00001c98       ??_C@_0BJ@GINODENB@BuildSecurityDescriptorA?$AA@ 10021c98     Engine.obj
 0002:00001cb4       ??_C@_0BJ@HEEGIBAG@BuildSecurityDescriptorW?$AA@ 10021cb4     Engine.obj
 0002:00001cd0       ??_C@_0BG@ELPCADDP@BuildTrusteeWithNameA?$AA@ 10021cd0     Engine.obj
 0002:00001ce8       ??_C@_0BG@FHGKLGOI@BuildTrusteeWithNameW?$AA@ 10021ce8     Engine.obj
 0002:00001d00       ??_C@_0CA@KKCDHMOK@BuildTrusteeWithObjectsAndNameA?$AA@ 10021d00     Engine.obj
 0002:00001d20       ??_C@_0CA@LGLLMJDN@BuildTrusteeWithObjectsAndNameW?$AA@ 10021d20     Engine.obj
 0002:00001d40       ??_C@_0BP@ILBGGFJN@BuildTrusteeWithObjectsAndSidA?$AA@ 10021d40     Engine.obj
 0002:00001d60       ??_C@_0BP@JHIONAEK@BuildTrusteeWithObjectsAndSidW?$AA@ 10021d60     Engine.obj
 0002:00001d80       ??_C@_0BF@FEPBKING@BuildTrusteeWithSidA?$AA@ 10021d80     Engine.obj
 0002:00001d98       ??_C@_0BF@EIGJBNAB@BuildTrusteeWithSidW?$AA@ 10021d98     Engine.obj
 0002:00001db0       ??_C@_0BH@NJCOFKBG@CancelOverlappedAccess?$AA@ 10021db0     Engine.obj
 0002:00001dc8       ??_C@_0BG@LJCGGEN@ChangeServiceConfig2A?$AA@ 10021dc8     Engine.obj
 0002:00001de0       ??_C@_0BG@BHAKNDJK@ChangeServiceConfig2W?$AA@ 10021de0     Engine.obj
 0002:00001df8       ??_C@_0BF@EMHADHPM@ChangeServiceConfigA?$AA@ 10021df8     Engine.obj
 0002:00001e10       ??_C@_0BF@FAOIICCL@ChangeServiceConfigW?$AA@ 10021e10     Engine.obj
 0002:00001e28       ??_C@_0BF@KEDGKLKA@CheckTokenMembership?$AA@ 10021e28     Engine.obj
 0002:00001e40       ??_C@_0P@CBEAAAPH@ClearEventLogA?$AA@ 10021e40     Engine.obj
 0002:00001e50       ??_C@_0P@DNNILFCA@ClearEventLogW?$AA@ 10021e50     Engine.obj
 0002:00001e60       ??_C@_0BE@PKHPOFOL@CloseCodeAuthzLevel?$AA@ 10021e60     Engine.obj
 0002:00001e74       ??_C@_0BG@GFFPLEHE@CloseEncryptedFileRaw?$AA@ 10021e74     Engine.obj
 0002:00001e8c       ??_C@_0O@CMPADMMI@CloseEventLog?$AA@ 10021e8c     Engine.obj
 0002:00001e9c       ??_C@_0BD@DLOBKKPI@CloseServiceHandle?$AA@ 10021e9c     Engine.obj
 0002:00001eb0       ??_C@_0BM@KJGFGMIO@CloseThreadWaitChainSession?$AA@ 10021eb0     Engine.obj
 0002:00001ecc       ??_C@_0L@KNAOONPE@CloseTrace?$AA@ 10021ecc     Engine.obj
 0002:00001ed8       ??_C@_0BN@GLDIOODC@CommandLineFromMsiDescriptor?$AA@ 10021ed8     Engine.obj
 0002:00001ef8       ??_C@_0CF@OJABNGCJ@ComputeAccessTokenFromCodeAuthzL@ 10021ef8     Engine.obj
 0002:00001f20       ??_C@_0P@OACFAAAJ@ControlService?$AA@ 10021f20     Engine.obj
 0002:00001f30       ??_C@_0BC@FLJPAAIL@ControlServiceExA?$AA@ 10021f30     Engine.obj
 0002:00001f44       ??_C@_0BC@EHAHLFFM@ControlServiceExW?$AA@ 10021f44     Engine.obj
 0002:00001f58       ??_C@_0O@PIGKHLML@ControlTraceA?$AA@ 10021f58     Engine.obj
 0002:00001f68       ??_C@_0O@OEPCMOBM@ControlTraceW?$AA@ 10021f68     Engine.obj
 0002:00001f78       ??_C@_0CD@GDGCNDEC@ConvertAccessToSecurityDescripto@ 10021f78     Engine.obj
 0002:00001f9c       ??_C@_0CD@HPPKGGJF@ConvertAccessToSecurityDescripto@ 10021f9c     Engine.obj
 0002:00001fc0       ??_C@_0BP@BNMBAHAI@ConvertSDToStringSDRootDomainA?$AA@ 10021fc0     Engine.obj
 0002:00001fe0       ??_C@_0BP@BFJLCNP@ConvertSDToStringSDRootDomainW?$AA@ 10021fe0     Engine.obj
 0002:00002000       ??_C@_0CD@MPMEOGDG@ConvertSecurityDescriptorToAcces@ 10022000     Engine.obj
 0002:00002024       ??_C@_0CI@PJLHKDLM@ConvertSecurityDescriptorToAcces@ 10022024     Engine.obj
 0002:0000204c       ??_C@_0CI@OFCPBGGL@ConvertSecurityDescriptorToAcces@ 1002204c     Engine.obj
 0002:00002074       ??_C@_0CD@NDFMFDOB@ConvertSecurityDescriptorToAcces@ 10022074     Engine.obj
 0002:00002098       ??_C@_0DF@LACIGFCK@ConvertSecurityDescriptorToStrin@ 10022098     Engine.obj
 0002:000020d0       ??_C@_0DF@KMLANAPN@ConvertSecurityDescriptorToStrin@ 100220d0     Engine.obj
 0002:00002108       ??_C@_0BH@JIDFKHLF@ConvertSidToStringSidA?$AA@ 10022108     Engine.obj
 0002:00002120       ??_C@_0BH@IEKNBCGC@ConvertSidToStringSidW?$AA@ 10022120     Engine.obj
 0002:00002138       ??_C@_0BL@NJPOKHID@ConvertStringSDToSDDomainA?$AA@ 10022138     Engine.obj
 0002:00002154       ??_C@_0BL@MFGGBCFE@ConvertStringSDToSDDomainW?$AA@ 10022154     Engine.obj
 0002:00002170       ??_C@_0BP@KKFBKMAI@ConvertStringSDToSDRootDomainA?$AA@ 10022170     Engine.obj
 0002:00002190       ??_C@_0BP@LGMJBJNP@ConvertStringSDToSDRootDomainW?$AA@ 10022190     Engine.obj
 0002:000021b0       ??_C@_0DF@FDEDMKDH@ConvertStringSecurityDescriptorT@ 100221b0     Engine.obj
 0002:000021e8       ??_C@_0DF@EPNLHPOA@ConvertStringSecurityDescriptorT@ 100221e8     Engine.obj
 0002:00002220       ??_C@_0BH@FDLGNCJ@ConvertStringSidToSidA?$AA@ 10022220     Engine.obj
 0002:00002238       ??_C@_0BH@BJKDNIPO@ConvertStringSidToSidW?$AA@ 10022238     Engine.obj
 0002:00002250       ??_C@_0CK@GJKPAGBH@ConvertToAutoInheritPrivateObjec@ 10022250     Engine.obj
 0002:0000227c       ??_C@_07CAPOKIKL@CopySid?$AA@ 1002227c     Engine.obj
 0002:00002284       ??_C@_0BF@EDPAEHII@CreateCodeAuthzLevel?$AA@ 10022284     Engine.obj
 0002:0000229c       ??_C@_0BM@CNBDECGJ@CreatePrivateObjectSecurity?$AA@ 1002229c     Engine.obj
 0002:000022b8       ??_C@_0BO@LNFKGBJE@CreatePrivateObjectSecurityEx?$AA@ 100222b8     Engine.obj
 0002:000022d8       ??_C@_0DD@KJLPDPAM@CreatePrivateObjectSecurityWithM@ 100222d8     Engine.obj
 0002:0000230c       ??_C@_0BF@FDFOBPKF@CreateProcessAsUserA?$AA@ 1002230c     Engine.obj
 0002:00002324       ??_C@_0BF@EPMGKKHC@CreateProcessAsUserW?$AA@ 10022324     Engine.obj
 0002:0000233c       ??_C@_0BI@CDLEDEMD@CreateProcessWithLogonW?$AA@ 1002233c     Engine.obj
 0002:00002354       ??_C@_0BI@NNMIOLAP@CreateProcessWithTokenW?$AA@ 10022354     Engine.obj
 0002:0000236c       ??_C@_0BG@BDMCGKB@CreateRestrictedToken?$AA@ 1002236c     Engine.obj
 0002:00002384       ??_C@_0P@EOCCPLHH@CreateServiceA?$AA@ 10022384     Engine.obj
 0002:00002394       ??_C@_0P@FCLKEOKA@CreateServiceW?$AA@ 10022394     Engine.obj
 0002:000023a4       ??_C@_0BG@FLKBNGL@CreateTraceInstanceId?$AA@ 100223a4     Engine.obj
 0002:000023bc       ??_C@_0BD@INGGIJLP@CreateWellKnownSid?$AA@ 100223bc     Engine.obj
 0002:000023d0       ??_C@_0BG@KLEGCKKD@CredBackupCredentials?$AA@ 100223d0     Engine.obj
 0002:000023e8       ??_C@_0M@EKEGHADD@CredDeleteA?$AA@ 100223e8     Engine.obj
 0002:000023f4       ??_C@_0M@FGNOMFOE@CredDeleteW?$AA@ 100223f4     Engine.obj
 0002:00002400       ??_C@_0CA@CNLFNE@CredEncryptAndMarshalBinaryBlob?$AA@ 10022400     Engine.obj
 0002:00002420       ??_C@_0P@CBNNMHGB@CredEnumerateA?$AA@ 10022420     Engine.obj
 0002:00002430       ??_C@_0P@DNEFHCLG@CredEnumerateW?$AA@ 10022430     Engine.obj
 0002:00002440       ??_C@_0BI@MEFALKBK@CredFindBestCredentialA?$AA@ 10022440     Engine.obj
 0002:00002458       ??_C@_0BI@NIMIAPMN@CredFindBestCredentialW?$AA@ 10022458     Engine.obj
 0002:00002470       ??_C@_08BKGEDPKH@CredFree?$AA@ 10022470     Engine.obj
 0002:0000247c       ??_C@_0BE@CKEBINAF@CredGetSessionTypes?$AA@ 1002247c     Engine.obj
 0002:00002490       ??_C@_0BD@BAKDFJMD@CredGetTargetInfoA?$AA@ 10022490     Engine.obj
 0002:000024a4       ??_C@_0BD@MDLOMBE@CredGetTargetInfoW?$AA@ 100224a4     Engine.obj
 0002:000024b8       ??_C@_0BL@OJPCEOMF@CredIsMarshaledCredentialA?$AA@ 100224b8     Engine.obj
 0002:000024d4       ??_C@_0BL@PFGKPLBC@CredIsMarshaledCredentialW?$AA@ 100224d4     Engine.obj
 0002:000024f0       ??_C@_0BB@DNDEIOPL@CredIsProtectedA?$AA@ 100224f0     Engine.obj
 0002:00002504       ??_C@_0BB@CBKMDLCM@CredIsProtectedW?$AA@ 10022504     Engine.obj
 0002:00002518       ??_C@_0BH@DDIBGMBI@CredMarshalCredentialA?$AA@ 10022518     Engine.obj
 0002:00002530       ??_C@_0BH@CPBJNJMP@CredMarshalCredentialW?$AA@ 10022530     Engine.obj
 0002:00002548       ??_C@_0BC@MGAFNGND@CredProfileLoaded?$AA@ 10022548     Engine.obj
 0002:0000255c       ??_C@_0BE@LIHBEDCH@CredProfileUnloaded?$AA@ 1002255c     Engine.obj
 0002:00002570       ??_C@_0N@HCIDHPFL@CredProtectA?$AA@ 10022570     Engine.obj
 0002:00002580       ??_C@_0N@GOBLMKIM@CredProtectW?$AA@ 10022580     Engine.obj
 0002:00002590       ??_C@_09GFONNKEP@CredReadA?$AA@ 10022590     Engine.obj
 0002:0000259c       ??_C@_0BG@EDGBOAHE@CredReadByTokenHandle?$AA@ 1002259c     Engine.obj
 0002:000025b4       ??_C@_0BL@BGNLHAJN@CredReadDomainCredentialsA?$AA@ 100225b4     Engine.obj
 0002:000025d0       ??_C@_0BL@KEDMFEK@CredReadDomainCredentialsW?$AA@ 100225d0     Engine.obj
 0002:000025ec       ??_C@_09HJHFGPJI@CredReadW?$AA@ 100225ec     Engine.obj
 0002:000025f8       ??_C@_0M@GFMLFHEB@CredRenameA?$AA@ 100225f8     Engine.obj
 0002:00002604       ??_C@_0M@HJFDOCJG@CredRenameW?$AA@ 10022604     Engine.obj
 0002:00002610       ??_C@_0BH@PJJMGNNI@CredRestoreCredentials?$AA@ 10022610     Engine.obj
 0002:00002628       ??_C@_0BJ@MBOAFDLJ@CredUnmarshalCredentialA?$AA@ 10022628     Engine.obj
 0002:00002644       ??_C@_0BJ@NNHIOGGO@CredUnmarshalCredentialW?$AA@ 10022644     Engine.obj
 0002:00002660       ??_C@_0P@DHKENJJA@CredUnprotectA?$AA@ 10022660     Engine.obj
 0002:00002670       ??_C@_0P@CLDMGMEH@CredUnprotectW?$AA@ 10022670     Engine.obj
 0002:00002680       ??_C@_0L@BMKCCAHJ@CredWriteA?$AA@ 10022680     Engine.obj
 0002:0000268c       ??_C@_0BM@CFHPHMDG@CredWriteDomainCredentialsA?$AA@ 1002268c     Engine.obj
 0002:000026a8       ??_C@_0BM@DJOHMJOB@CredWriteDomainCredentialsW?$AA@ 100226a8     Engine.obj
 0002:000026c4       ??_C@_0L@DKJFKO@CredWriteW?$AA@ 100226c4     Engine.obj
 0002:000026d0       ??_C@_0BH@DPKKIEBF@CredpConvertCredential?$AA@ 100226d0     Engine.obj
 0002:000026e8       ??_C@_0BO@EOOJJCAD@CredpConvertOneCredentialSize?$AA@ 100226e8     Engine.obj
 0002:00002708       ??_C@_0BH@HKJJGHNI@CredpConvertTargetInfo?$AA@ 10022708     Engine.obj
 0002:00002720       ??_C@_0BG@GDBOADDI@CredpDecodeCredential?$AA@ 10022720     Engine.obj
 0002:00002738       ??_C@_0BG@LOGDODMG@CredpEncodeCredential?$AA@ 10022738     Engine.obj
 0002:00002750       ??_C@_0BC@FICCPBDN@CredpEncodeSecret?$AA@ 10022750     Engine.obj
 0002:00002764       ??_C@_0BF@ILOJAFDL@CryptAcquireContextA?$AA@ 10022764     Engine.obj
 0002:0000277c       ??_C@_0BF@JHHBLAOM@CryptAcquireContextW?$AA@ 1002277c     Engine.obj
 0002:00002794       ??_C@_0BD@JPCJHHNI@CryptContextAddRef?$AA@ 10022794     Engine.obj
 0002:000027a8       ??_C@_0BA@IEJOIHIJ@CryptCreateHash?$AA@ 100227a8     Engine.obj
 0002:000027b8       ??_C@_0N@JGGKHKPO@CryptDecrypt?$AA@ 100227b8     Engine.obj
 0002:000027c8       ??_C@_0P@KONILCFO@CryptDeriveKey?$AA@ 100227c8     Engine.obj
 0002:000027d8       ??_C@_0BB@INPNJGML@CryptDestroyHash?$AA@ 100227d8     Engine.obj
 0002:000027ec       ??_C@_0BA@FJKBDIMN@CryptDestroyKey?$AA@ 100227ec     Engine.obj
 0002:000027fc       ??_C@_0BD@NENCDLLD@CryptDuplicateHash?$AA@ 100227fc     Engine.obj
 0002:00002810       ??_C@_0BC@MFDBFHLB@CryptDuplicateKey?$AA@ 10022810     Engine.obj
 0002:00002824       ??_C@_0N@FIMHCOJK@CryptEncrypt?$AA@ 10022824     Engine.obj
 0002:00002834       ??_C@_0BI@NFNNDLAO@CryptEnumProviderTypesA?$AA@ 10022834     Engine.obj
 0002:0000284c       ??_C@_0BI@MJEFIONJ@CryptEnumProviderTypesW?$AA@ 1002284c     Engine.obj
 0002:00002864       ??_C@_0BE@FGAIAMPB@CryptEnumProvidersA?$AA@ 10022864     Engine.obj
 0002:00002878       ??_C@_0BE@EKJALJCG@CryptEnumProvidersW?$AA@ 10022878     Engine.obj
 0002:0000288c       ??_C@_0P@PLHBLCCF@CryptExportKey?$AA@ 1002288c     Engine.obj
 0002:0000289c       ??_C@_0M@LEBPENDF@CryptGenKey?$AA@ 1002289c     Engine.obj
 0002:000028a8       ??_C@_0P@HGPEDCEI@CryptGenRandom?$AA@ 100228a8     Engine.obj
 0002:000028b8       ??_C@_0BJ@CBPJNEIF@CryptGetDefaultProviderA?$AA@ 100228b8     Engine.obj
 0002:000028d4       ??_C@_0BJ@DNGBGBFC@CryptGetDefaultProviderW?$AA@ 100228d4     Engine.obj
 0002:000028f0       ??_C@_0BC@BNEOPJAJ@CryptGetHashParam?$AA@ 100228f0     Engine.obj
 0002:00002904       ??_C@_0BB@FHEMKDCG@CryptGetKeyParam?$AA@ 10022904     Engine.obj
 0002:00002918       ??_C@_0BC@KAIPIFFG@CryptGetProvParam?$AA@ 10022918     Engine.obj
 0002:0000292c       ??_C@_0BA@OBMCEIAO@CryptGetUserKey?$AA@ 1002292c     Engine.obj
 0002:0000293c       ??_C@_0O@DAIMODJH@CryptHashData?$AA@ 1002293c     Engine.obj
 0002:0000294c       ??_C@_0BE@FFADEJH@CryptHashSessionKey?$AA@ 1002294c     Engine.obj
 0002:00002960       ??_C@_0P@HBKFFJF@CryptImportKey?$AA@ 10022960     Engine.obj
 0002:00002970       ??_C@_0BE@FBIJLPPP@CryptReleaseContext?$AA@ 10022970     Engine.obj
 0002:00002984       ??_C@_0BC@GJPADFEF@CryptSetHashParam?$AA@ 10022984     Engine.obj
 0002:00002998       ??_C@_0BB@FIFCHFKF@CryptSetKeyParam?$AA@ 10022998     Engine.obj
 0002:000029ac       ??_C@_0BC@NEDBEJBK@CryptSetProvParam?$AA@ 100229ac     Engine.obj
 0002:000029c0       ??_C@_0BC@NFDLIJKO@CryptSetProviderA?$AA@ 100229c0     Engine.obj
 0002:000029d4       ??_C@_0BE@JIACKPCD@CryptSetProviderExA?$AA@ 100229d4     Engine.obj
 0002:000029e8       ??_C@_0BE@IEJKBKPE@CryptSetProviderExW?$AA@ 100229e8     Engine.obj
 0002:000029fc       ??_C@_0BC@MJKDDMHJ@CryptSetProviderW?$AA@ 100229fc     Engine.obj
 0002:00002a10       ??_C@_0P@JNEKOFOB@CryptSignHashA?$AA@ 10022a10     Engine.obj
 0002:00002a20       ??_C@_0P@IBNCFADG@CryptSignHashW?$AA@ 10022a20     Engine.obj
 0002:00002a30       ??_C@_0BG@LMDODOM@CryptVerifySignatureA?$AA@ 10022a30     Engine.obj
 0002:00002a48       ??_C@_0BG@BHFLFGDL@CryptVerifySignatureW?$AA@ 10022a48     Engine.obj
 0002:00002a60       ??_C@_0N@HFBOCPJL@DecryptFileA?$AA@ 10022a60     Engine.obj
 0002:00002a70       ??_C@_0N@GJIGJKEM@DecryptFileW?$AA@ 10022a70     Engine.obj
 0002:00002a80       ??_C@_09HDMIEACH@DeleteAce?$AA@ 10022a80     Engine.obj
 0002:00002a8c       ??_C@_0O@KGGKBBLF@DeleteService?$AA@ 10022a8c     Engine.obj
 0002:00002a9c       ??_C@_0BG@LPHFFLAA@DeregisterEventSource?$AA@ 10022a9c     Engine.obj
 0002:00002ab4       ??_C@_0BN@ODNPIIP@DestroyPrivateObjectSecurity?$AA@ 10022ab4     Engine.obj
 0002:00002ad4       ??_C@_0BM@DFPBFFPM@DuplicateEncryptionInfoFile?$AA@ 10022ad4     Engine.obj
 0002:00002af0       ??_C@_0P@OPDGIJLP@DuplicateToken?$AA@ 10022af0     Engine.obj
 0002:00002b00       ??_C@_0BB@GGIONKBA@DuplicateTokenEx?$AA@ 10022b00     Engine.obj
 0002:00002b14       ??_C@_0BH@HMKPIEEL@ElfBackupEventLogFileA?$AA@ 10022b14     Engine.obj
 0002:00002b2c       ??_C@_0BH@GADHDBJM@ElfBackupEventLogFileW?$AA@ 10022b2c     Engine.obj
 0002:00002b44       ??_C@_0BA@PBJOFHME@ElfChangeNotify?$AA@ 10022b44     Engine.obj
 0002:00002b54       ??_C@_0BG@PEOLMPFB@ElfClearEventLogFileA?$AA@ 10022b54     Engine.obj
 0002:00002b6c       ??_C@_0BG@OIHDHKIG@ElfClearEventLogFileW?$AA@ 10022b6c     Engine.obj
 0002:00002b84       ??_C@_0BB@GDOLPCJE@ElfCloseEventLog?$AA@ 10022b84     Engine.obj
 0002:00002b98       ??_C@_0BJ@JCGMGEJP@ElfDeregisterEventSource?$AA@ 10022b98     Engine.obj
 0002:00002bb4       ??_C@_0BB@PEJJBCNE@ElfFlushEventLog?$AA@ 10022bb4     Engine.obj
 0002:00002bc8       ??_C@_0BD@JADLINCJ@ElfNumberOfRecords?$AA@ 10022bc8     Engine.obj
 0002:00002bdc       ??_C@_0BA@LCKFAGBP@ElfOldestRecord?$AA@ 10022bdc     Engine.obj
 0002:00002bec       ??_C@_0BH@FKDFOAHE@ElfOpenBackupEventLogA?$AA@ 10022bec     Engine.obj
 0002:00002c04       ??_C@_0BH@EGKNFFKD@ElfOpenBackupEventLogW?$AA@ 10022c04     Engine.obj
 0002:00002c1c       ??_C@_0BB@MHCIADG@ElfOpenEventLogA?$AA@ 10022c1c     Engine.obj
 0002:00002c30       ??_C@_0BB@BAOKDFOB@ElfOpenEventLogW?$AA@ 10022c30     Engine.obj
 0002:00002c44       ??_C@_0BB@JFLLLKDD@ElfReadEventLogA?$AA@ 10022c44     Engine.obj
 0002:00002c58       ??_C@_0BB@IJCDAPOE@ElfReadEventLogW?$AA@ 10022c58     Engine.obj
 0002:00002c6c       ??_C@_0BI@FKKPCDJA@ElfRegisterEventSourceA?$AA@ 10022c6c     Engine.obj
 0002:00002c84       ??_C@_0BI@EGDHJGEH@ElfRegisterEventSourceW?$AA@ 10022c84     Engine.obj
 0002:00002c9c       ??_C@_0BA@DOOICFAM@ElfReportEventA?$AA@ 10022c9c     Engine.obj
 0002:00002cac       ??_C@_0BJ@HLODIIKB@ElfReportEventAndSourceW?$AA@ 10022cac     Engine.obj
 0002:00002cc8       ??_C@_0BA@CCHAJANL@ElfReportEventW?$AA@ 10022cc8     Engine.obj
 0002:00002cd8       ??_C@_0M@PJLONHCH@EnableTrace?$AA@ 10022cd8     Engine.obj
 0002:00002ce4       ??_C@_0O@FDMFLAHJ@EnableTraceEx?$AA@ 10022ce4     Engine.obj
 0002:00002cf4       ??_C@_0P@MEPKFIFJ@EnableTraceEx2?$AA@ 10022cf4     Engine.obj
 0002:00002d04       ??_C@_0N@NJBKFCBA@EncryptFileA?$AA@ 10022d04     Engine.obj
 0002:00002d14       ??_C@_0N@MFICOHMH@EncryptFileW?$AA@ 10022d14     Engine.obj
 0002:00002d24       ??_C@_0BF@ONEKLOMK@EncryptedFileKeyInfo?$AA@ 10022d24     Engine.obj
 0002:00002d3c       ??_C@_0BC@FOCBINAE@EncryptionDisable?$AA@ 10022d3c     Engine.obj
 0002:00002d50       ??_C@_0BH@OHLJEFCG@EnumDependentServicesA?$AA@ 10022d50     Engine.obj
 0002:00002d68       ??_C@_0BH@PLCBPAPB@EnumDependentServicesW?$AA@ 10022d68     Engine.obj
 0002:00002d80       ??_C@_0BC@IINEBPFF@EnumServiceGroupW?$AA@ 10022d80     Engine.obj
 0002:00002d94       ??_C@_0BE@LBDFDOKH@EnumServicesStatusA?$AA@ 10022d94     Engine.obj
 0002:00002da8       ??_C@_0BG@BMMFFGEL@EnumServicesStatusExA?$AA@ 10022da8     Engine.obj
 0002:00002dc0       ??_C@_0BG@FNODJM@EnumServicesStatusExW?$AA@ 10022dc0     Engine.obj
 0002:00002dd8       ??_C@_0BE@KNKNILHA@EnumServicesStatusW?$AA@ 10022dd8     Engine.obj
 0002:00002dec       ??_C@_0BE@LDGIMPEO@EnumerateTraceGuids?$AA@ 10022dec     Engine.obj
 0002:00002e00       ??_C@_0BG@PEJGLCBH@EnumerateTraceGuidsEx?$AA@ 10022e00     Engine.obj
 0002:00002e18       ??_C@_0P@MKAPCLI@EqualDomainSid?$AA@ 10022e18     Engine.obj
 0002:00002e28       ??_C@_0P@IGIIDBBF@EqualPrefixSid?$AA@ 10022e28     Engine.obj
 0002:00002e38       ??_C@_08FDBMHLGI@EqualSid?$AA@ 10022e38     Engine.obj
 0002:00002e44       ??_C@_0BD@OJOMPEDG@EventAccessControl?$AA@ 10022e44     Engine.obj
 0002:00002e58       ??_C@_0BB@LNKGILLC@EventAccessQuery?$AA@ 10022e58     Engine.obj
 0002:00002e6c       ??_C@_0BC@JGDJHAHB@EventAccessRemove?$AA@ 10022e6c     Engine.obj
 0002:00002e80       ??_C@_0BH@HMPLKEEO@EventActivityIdControl?$AA@ 10022e80     Engine.obj
 0002:00002e98       ??_C@_0N@HMGPMPIB@EventEnabled?$AA@ 10022e98     Engine.obj
 0002:00002ea8       ??_C@_0BF@IOOBFCAF@EventProviderEnabled?$AA@ 10022ea8     Engine.obj
 0002:00002ec0       ??_C@_0O@BAMBGLIE@EventRegister?$AA@ 10022ec0     Engine.obj
 0002:00002ed0       ??_C@_0BA@FAKLIPIC@EventUnregister?$AA@ 10022ed0     Engine.obj
 0002:00002ee0       ??_C@_0L@JMEHOADM@EventWrite?$AA@ 10022ee0     Engine.obj
 0002:00002eec       ??_C@_0BG@JLOMKEKA@EventWriteEndScenario?$AA@ 10022eec     Engine.obj
 0002:00002f04       ??_C@_0N@ECEOLLBF@EventWriteEx?$AA@ 10022f04     Engine.obj
 0002:00002f14       ??_C@_0BI@EECEDHK@EventWriteStartScenario?$AA@ 10022f14     Engine.obj
 0002:00002f2c       ??_C@_0BB@IGOBINII@EventWriteString?$AA@ 10022f2c     Engine.obj
 0002:00002f40       ??_C@_0BD@PKLHJPHD@EventWriteTransfer?$AA@ 10022f40     Engine.obj
 0002:00002f54       ??_C@_0BG@OEKMKGMM@FileEncryptionStatusA?$AA@ 10022f54     Engine.obj
 0002:00002f6c       ??_C@_0BG@PIDEBDBL@FileEncryptionStatusW?$AA@ 10022f6c     Engine.obj
 0002:00002f84       ??_C@_0BB@EANFOEKD@FindFirstFreeAce?$AA@ 10022f84     Engine.obj
 0002:00002f98       ??_C@_0O@BPCNIBCC@FlushEfsCache?$AA@ 10022f98     Engine.obj
 0002:00002fa8       ??_C@_0M@GADFDFEM@FlushTraceA?$AA@ 10022fa8     Engine.obj
 0002:00002fb4       ??_C@_0M@HMKNIAJL@FlushTraceW?$AA@ 10022fb4     Engine.obj
 0002:00002fc0       ??_C@_0BJ@HOHFCNEH@FreeEncryptedFileKeyInfo?$AA@ 10022fc0     Engine.obj
 0002:00002fdc       ??_C@_0BK@KKAGAIGA@FreeEncryptedFileMetadata?$AA@ 10022fdc     Engine.obj
 0002:00002ff8       ??_C@_0CC@LDKPLPOA@FreeEncryptionCertificateHashLis@ 10022ff8     Engine.obj
 0002:0000301c       ??_C@_0BH@MIJODPKM@FreeInheritedFromArray?$AA@ 1002301c     Engine.obj
 0002:00003034       ??_C@_07JIBMLCGK@FreeSid?$AA@ 10023034     Engine.obj
 0002:0000303c       ??_C@_0BP@FCBKCNIB@GetAccessPermissionsForObjectA?$AA@ 1002303c     Engine.obj
 0002:0000305c       ??_C@_0BP@EOICJIFG@GetAccessPermissionsForObjectW?$AA@ 1002305c     Engine.obj
 0002:0000307c       ??_C@_06MGOHGGFD@GetAce?$AA@ 1002307c     Engine.obj
 0002:00003084       ??_C@_0BC@JNNADOPA@GetAclInformation?$AA@ 10023084     Engine.obj
 0002:00003098       ??_C@_0BO@IPPGKLKO@GetAuditedPermissionsFromAclA?$AA@ 10023098     Engine.obj
 0002:000030b8       ??_C@_0BO@JDGOBOHJ@GetAuditedPermissionsFromAclW?$AA@ 100230b8     Engine.obj
 0002:000030d8       ??_C@_0BF@FODJMJAD@GetCurrentHwProfileA?$AA@ 100230d8     Engine.obj
 0002:000030f0       ??_C@_0BF@ECKBHMNE@GetCurrentHwProfileW?$AA@ 100230f0     Engine.obj
 0002:00003108       ??_C@_0BL@KLLOMAFL@GetEffectiveRightsFromAclA?$AA@ 10023108     Engine.obj
 0002:00003124       ??_C@_0BL@LHCGHFIM@GetEffectiveRightsFromAclW?$AA@ 10023124     Engine.obj
 0002:00003140       ??_C@_0BJ@DLCPNNJA@GetEncryptedFileMetadata?$AA@ 10023140     Engine.obj
 0002:0000315c       ??_C@_0BH@OPDOCNNK@GetEventLogInformation?$AA@ 1002315c     Engine.obj
 0002:00003174       ??_C@_0BL@OOBEBAAI@GetExplicitEntriesFromAclA?$AA@ 10023174     Engine.obj
 0002:00003190       ??_C@_0BL@PCIMKFNP@GetExplicitEntriesFromAclW?$AA@ 10023190     Engine.obj
 0002:000031ac       ??_C@_0BB@JIAOINHD@GetFileSecurityA?$AA@ 100231ac     Engine.obj
 0002:000031c0       ??_C@_0BB@IEJGDIKE@GetFileSecurityW?$AA@ 100231c0     Engine.obj
 0002:000031d4       ??_C@_0BO@MHBEMHMA@GetInformationCodeAuthzLevelW?$AA@ 100231d4     Engine.obj
 0002:000031f4       ??_C@_0BP@OMDANBIO@GetInformationCodeAuthzPolicyW?$AA@ 100231f4     Engine.obj
 0002:00003214       ??_C@_0BG@LHAOMMMJ@GetInheritanceSourceA?$AA@ 10023214     Engine.obj
 0002:0000322c       ??_C@_0BG@KLJGHJBO@GetInheritanceSourceW?$AA@ 1002322c     Engine.obj
 0002:00003244       ??_C@_0BI@DOBPCBKO@GetKernelObjectSecurity?$AA@ 10023244     Engine.obj
 0002:0000325c       ??_C@_0N@HBKDHKNL@GetLengthSid?$AA@ 1002325c     Engine.obj
 0002:0000326c       ??_C@_0BP@GNEJLKOC@GetLocalManagedApplicationData?$AA@ 1002326c     Engine.obj
 0002:0000328c       ??_C@_0BM@CNHCNHOP@GetLocalManagedApplications?$AA@ 1002328c     Engine.obj
 0002:000032a8       ??_C@_0CA@MFNHPGHA@GetManagedApplicationCategories?$AA@ 100232a8     Engine.obj
 0002:000032c8       ??_C@_0BH@LBAMPPM@GetManagedApplications?$AA@ 100232c8     Engine.obj
 0002:000032e0       ??_C@_0BE@DIEKOEMD@GetMultipleTrusteeA?$AA@ 100232e0     Engine.obj
 0002:000032f4       ??_C@_0BN@DDACCFPI@GetMultipleTrusteeOperationA?$AA@ 100232f4     Engine.obj
 0002:00003314       ??_C@_0BN@CPJKJACP@GetMultipleTrusteeOperationW?$AA@ 10023314     Engine.obj
 0002:00003334       ??_C@_0BE@CENCFBBE@GetMultipleTrusteeW?$AA@ 10023334     Engine.obj
 0002:00003348       ??_C@_0BG@NNOBGLHD@GetNamedSecurityInfoA?$AA@ 10023348     Engine.obj
 0002:00003360       ??_C@_0BI@OCJAJPPP@GetNamedSecurityInfoExA?$AA@ 10023360     Engine.obj
 0002:00003378       ??_C@_0BI@POAICKCI@GetNamedSecurityInfoExW?$AA@ 10023378     Engine.obj
 0002:00003390       ??_C@_0BG@MBHJNOKE@GetNamedSecurityInfoW?$AA@ 10023390     Engine.obj
 0002:000033a8       ??_C@_0BL@HLAMFCHD@GetNumberOfEventLogRecords?$AA@ 100233a8     Engine.obj
 0002:000033c4       ??_C@_0BI@DELAMBGK@GetOldestEventLogRecord?$AA@ 100233c4     Engine.obj
 0002:000033dc       ??_C@_0BL@KHIKAPKH@GetOverlappedAccessResults?$AA@ 100233dc     Engine.obj
 0002:000033f8       ??_C@_0BJ@IPBHKICD@GetPrivateObjectSecurity?$AA@ 100233f8     Engine.obj
 0002:00003414       ??_C@_0BN@MIKBLMEK@GetSecurityDescriptorControl?$AA@ 10023414     Engine.obj
 0002:00003434       ??_C@_0BK@IDBLHDPI@GetSecurityDescriptorDacl?$AA@ 10023434     Engine.obj
 0002:00003450       ??_C@_0BL@GECPKABA@GetSecurityDescriptorGroup?$AA@ 10023450     Engine.obj
 0002:0000346c       ??_C@_0BM@GMFKKGMK@GetSecurityDescriptorLength?$AA@ 1002346c     Engine.obj
 0002:00003488       ??_C@_0BL@NGDAALJK@GetSecurityDescriptorOwner?$AA@ 10023488     Engine.obj
 0002:000034a4       ??_C@_0BP@PCKBIDKA@GetSecurityDescriptorRMControl?$AA@ 100234a4     Engine.obj
 0002:000034c4       ??_C@_0BK@FBNLDIGK@GetSecurityDescriptorSacl?$AA@ 100234c4     Engine.obj
 0002:000034e0       ??_C@_0BA@JKIAABFB@GetSecurityInfo?$AA@ 100234e0     Engine.obj
 0002:000034f0       ??_C@_0BD@IGJHBJNP@GetSecurityInfoExA?$AA@ 100234f0     Engine.obj
 0002:00003504       ??_C@_0BD@JKAPKMAI@GetSecurityInfoExW?$AA@ 10023504     Engine.obj
 0002:00003518       ??_C@_0BH@CLHIMOME@GetServiceDisplayNameA?$AA@ 10023518     Engine.obj
 0002:00003530       ??_C@_0BH@DHOAHLBD@GetServiceDisplayNameW?$AA@ 10023530     Engine.obj
 0002:00003548       ??_C@_0BD@LOHJOAGO@GetServiceKeyNameA?$AA@ 10023548     Engine.obj
 0002:0000355c       ??_C@_0BD@KCOBFFLJ@GetServiceKeyNameW?$AA@ 1002355c     Engine.obj
 0002:00003570       ??_C@_0BK@FIHGNING@GetSidIdentifierAuthority?$AA@ 10023570     Engine.obj
 0002:0000358c       ??_C@_0BF@FLPMMBAC@GetSidLengthRequired?$AA@ 1002358c     Engine.obj
 0002:000035a4       ??_C@_0BD@LMGFHOAE@GetSidSubAuthority?$AA@ 100235a4     Engine.obj
 0002:000035b8       ??_C@_0BI@LPFJALDI@GetSidSubAuthorityCount?$AA@ 100235b8     Engine.obj
 0002:000035d0       ??_C@_0BD@DEILOCNH@GetThreadWaitChain?$AA@ 100235d0     Engine.obj
 0002:000035e4       ??_C@_0BE@JIDLIKOL@GetTokenInformation?$AA@ 100235e4     Engine.obj
 0002:000035f8       ??_C@_0BE@DNOHFPHI@GetTraceEnableFlags?$AA@ 100235f8     Engine.obj
 0002:0000360c       ??_C@_0BE@JCHMKLLJ@GetTraceEnableLevel?$AA@ 1002360c     Engine.obj
 0002:00003620       ??_C@_0BF@OFCGPMKL@GetTraceLoggerHandle?$AA@ 10023620     Engine.obj
 0002:00003638       ??_C@_0BA@JHACOHDM@GetTrusteeFormA?$AA@ 10023638     Engine.obj
 0002:00003648       ??_C@_0BA@ILJKFCOL@GetTrusteeFormW?$AA@ 10023648     Engine.obj
 0002:00003658       ??_C@_0BA@MCAIMNEB@GetTrusteeNameA?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BA@NOJAHIJG@GetTrusteeNameW?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BA@JCKGLPLN@GetTrusteeTypeA?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BA@IODOAKGK@GetTrusteeTypeW?$AA@ ********     Engine.obj
 0002:********       ??_C@_0N@EEKCKEJG@GetUserNameA?$AA@ ********     Engine.obj
 0002:000036a8       ??_C@_0N@FIDKBBEB@GetUserNameW?$AA@ 100236a8     Engine.obj
 0002:000036b8       ??_C@_0BL@HKPCPDHB@GetWindowsAccountDomainSid?$AA@ 100236b8     Engine.obj
 0002:000036d4       ??_C@_0BG@CEJDMDIG@I_QueryTagInformation?$AA@ 100236d4     Engine.obj
 0002:000036ec       ??_C@_0BK@FNCHLGII@I_ScGetCurrentGroupStateW?$AA@ 100236ec     Engine.obj
 0002:********       ??_C@_0BG@NHLHCEMH@I_ScIsSecurityProcess?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BG@DFKNPCFA@I_ScPnPGetServiceName?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BH@HMKALKPL@I_ScQueryServiceConfig?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BD@NOKKJECF@I_ScSendPnPMessage?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BC@PIIPMBGG@I_ScSendTSMessage?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BE@LODPKCDO@I_ScSetServiceBitsA?$AA@ ********     Engine.obj
 0002:0000378c       ??_C@_0BE@KCKHBHOJ@I_ScSetServiceBitsW?$AA@ 1002378c     Engine.obj
 0002:000037a0       ??_C@_0BH@NBIGPDIE@I_ScValidatePnPService?$AA@ 100237a0     Engine.obj
 0002:000037b8       ??_C@_0BI@MMAKMACC@IdentifyCodeAuthzLevelW?$AA@ 100237b8     Engine.obj
 0002:000037d0       ??_C@_0BK@BAFCLCAL@ImpersonateAnonymousToken?$AA@ 100237d0     Engine.obj
 0002:000037ec       ??_C@_0BI@HBGIAPJI@ImpersonateLoggedOnUser?$AA@ 100237ec     Engine.obj
 0002:00003804       ??_C@_0BL@NCGNIMEC@ImpersonateNamedPipeClient?$AA@ 10023804     Engine.obj
 0002:00003820       ??_C@_0BA@OBEAAJIA@ImpersonateSelf?$AA@ 10023820     Engine.obj
 0002:00003830       ??_C@_0O@GKCEJBAA@InitializeAcl?$AA@ 10023830     Engine.obj
 0002:00003840       ??_C@_0BN@FNLANPJJ@InitializeSecurityDescriptor?$AA@ 10023840     Engine.obj
 0002:00003860       ??_C@_0O@FFHKABMK@InitializeSid?$AA@ 10023860     Engine.obj
 0002:00003870       ??_C@_0BC@OLFLOANC@InitiateShutdownA?$AA@ 10023870     Engine.obj
 0002:00003884       ??_C@_0BC@PHMDFFAF@InitiateShutdownW?$AA@ 10023884     Engine.obj
 0002:00003898       ??_C@_0BI@PBMDOJKC@InitiateSystemShutdownA?$AA@ 10023898     Engine.obj
 0002:000038b0       ??_C@_0BK@HJAFKFIP@InitiateSystemShutdownExA?$AA@ 100238b0     Engine.obj
 0002:000038cc       ??_C@_0BK@GFJNBAFI@InitiateSystemShutdownExW?$AA@ 100238cc     Engine.obj
 0002:000038e8       ??_C@_0BI@ONFLFMHF@InitiateSystemShutdownW?$AA@ 100238e8     Engine.obj
 0002:00003900       ??_C@_0BD@OAKGEPJO@InstallApplication?$AA@ 10023900     Engine.obj
 0002:00003914       ??_C@_0O@JDINBACB@IsTextUnicode?$AA@ 10023914     Engine.obj
 0002:00003924       ??_C@_0BC@CBKGGEMP@IsTokenRestricted?$AA@ 10023924     Engine.obj
 0002:00003938       ??_C@_0BB@KPLHEOEJ@IsTokenUntrusted?$AA@ 10023938     Engine.obj
 0002:0000394c       ??_C@_0L@JGCEAIDP@IsValidAcl?$AA@ 1002394c     Engine.obj
 0002:00003958       ??_C@_0CC@BIKDIKOI@IsValidRelativeSecurityDescripto@ 10023958     Engine.obj
 0002:0000397c       ??_C@_0BK@MPIPDCIO@IsValidSecurityDescriptor?$AA@ 1002397c     Engine.obj
 0002:00003998       ??_C@_0L@KJHKJIPF@IsValidSid?$AA@ 10023998     Engine.obj
 0002:000039a4       ??_C@_0P@BMOENFOO@IsWellKnownSid?$AA@ 100239a4     Engine.obj
 0002:000039b4       ??_C@_0BE@FFALLOMJ@LockServiceDatabase?$AA@ 100239b4     Engine.obj
 0002:000039c8       ??_C@_0L@NCFIDFBE@LogonUserA?$AA@ 100239c8     Engine.obj
 0002:000039d4       ??_C@_0N@EEPPBBNF@LogonUserExA?$AA@ 100239d4     Engine.obj
 0002:000039e4       ??_C@_0P@LGIFDAHL@LogonUserExExW?$AA@ 100239e4     Engine.obj
 0002:000039f4       ??_C@_0N@FIGHKEAC@LogonUserExW?$AA@ 100239f4     Engine.obj
 0002:00003a04       ??_C@_0L@MOMAIAMD@LogonUserW?$AA@ 10023a04     Engine.obj
 0002:00003a10       ??_C@_0BD@NOIGEHON@LookupAccountNameA?$AA@ 10023a10     Engine.obj
 0002:00003a24       ??_C@_0BD@MCBOPCDK@LookupAccountNameW?$AA@ 10023a24     Engine.obj
 0002:00003a38       ??_C@_0BC@JPPPPIBO@LookupAccountSidA?$AA@ 10023a38     Engine.obj
 0002:00003a4c       ??_C@_0BC@IDGHENMJ@LookupAccountSidW?$AA@ 10023a4c     Engine.obj
 0002:00003a60       ??_C@_0BM@FGOFLPB@LookupPrivilegeDisplayNameA?$AA@ 10023a60     Engine.obj
 0002:00003a7c       ??_C@_0BM@BJPGOOCG@LookupPrivilegeDisplayNameW?$AA@ 10023a7c     Engine.obj
 0002:00003a98       ??_C@_0BF@JOFEJENO@LookupPrivilegeNameA?$AA@ 10023a98     Engine.obj
 0002:00003ab0       ??_C@_0BF@ICMMCBAJ@LookupPrivilegeNameW?$AA@ 10023ab0     Engine.obj
 0002:00003ac8       ??_C@_0BG@IOJLJHLJ@LookupPrivilegeValueA?$AA@ 10023ac8     Engine.obj
 0002:00003ae0       ??_C@_0BG@JCADCCGO@LookupPrivilegeValueW?$AA@ 10023ae0     Engine.obj
 0002:00003af8       ??_C@_0BP@JKEAEKON@LookupSecurityDescriptorPartsA?$AA@ 10023af8     Engine.obj
 0002:00003b18       ??_C@_0BP@IGNIPPDK@LookupSecurityDescriptorPartsW?$AA@ 10023b18     Engine.obj
 0002:00003b38       ??_C@_0BE@HMFICAAD@LsaAddAccountRights?$AA@ 10023b38     Engine.obj
 0002:00003b4c       ??_C@_0BK@MDDMBGJI@LsaAddPrivilegesToAccount?$AA@ 10023b4c     Engine.obj
 0002:00003b68       ??_C@_0BB@EAFPECOO@LsaClearAuditLog?$AA@ 10023b68     Engine.obj
 0002:00003b7c       ??_C@_08NGDIIGFA@LsaClose?$AA@ 10023b7c     Engine.obj
 0002:00003b88       ??_C@_0BB@EDDIDBBO@LsaCreateAccount?$AA@ 10023b88     Engine.obj
 0002:00003b9c       ??_C@_0BA@CONAHJFO@LsaCreateSecret?$AA@ 10023b9c     Engine.obj
 0002:00003bac       ??_C@_0BH@CIBNJPHF@LsaCreateTrustedDomain?$AA@ 10023bac     Engine.obj
 0002:00003bc4       ??_C@_0BJ@KDEPJHKO@LsaCreateTrustedDomainEx?$AA@ 10023bc4     Engine.obj
 0002:00003be0       ??_C@_09LOKJCCAC@LsaDelete?$AA@ 10023be0     Engine.obj
 0002:00003bec       ??_C@_0BH@BLCOLGIF@LsaDeleteTrustedDomain?$AA@ 10023bec     Engine.obj
 0002:00003c04       ??_C@_0BK@KOOHJDLM@LsaEnumerateAccountRights?$AA@ 10023c04     Engine.obj
 0002:00003c20       ??_C@_0BF@KHOJCJFF@LsaEnumerateAccounts?$AA@ 10023c20     Engine.obj
 0002:00003c38       ??_C@_0CC@ILNHBNEN@LsaEnumerateAccountsWithUserRigh@ 10023c38     Engine.obj
 0002:00003c5c       ??_C@_0BH@PBPDMNGC@LsaEnumeratePrivileges?$AA@ 10023c5c     Engine.obj
 0002:00003c74       ??_C@_0CA@MHDGLGCK@LsaEnumeratePrivilegesOfAccount?$AA@ 10023c74     Engine.obj
 0002:00003c94       ??_C@_0BL@BGPICAD@LsaEnumerateTrustedDomains?$AA@ 10023c94     Engine.obj
 0002:00003cb0       ??_C@_0BN@LJCMAMHF@LsaEnumerateTrustedDomainsEx?$AA@ 10023cb0     Engine.obj
 0002:00003cd0       ??_C@_0O@GFPFJMIM@LsaFreeMemory?$AA@ 10023cd0     Engine.obj
 0002:00003ce0       ??_C@_0BH@LHDJBIOM@LsaGetQuotasForAccount?$AA@ 10023ce0     Engine.obj
 0002:00003cf8       ??_C@_0BF@ILEELHCA@LsaGetRemoteUserName?$AA@ 10023cf8     Engine.obj
 0002:00003d10       ??_C@_0BK@NLNNFKKJ@LsaGetSystemAccessAccount?$AA@ 10023d10     Engine.obj
 0002:00003d2c       ??_C@_0P@MBDBBGDO@LsaGetUserName?$AA@ 10023d2c     Engine.obj
 0002:00003d3c       ??_C@_0BB@OIIMBMG@LsaICLookupNames?$AA@ 10023d3c     Engine.obj
 0002:00003d50       ??_C@_0BK@IKIDEKDN@LsaICLookupNamesWithCreds?$AA@ 10023d50     Engine.obj
 0002:00003d6c       ??_C@_0BA@NEMICBAK@LsaICLookupSids?$AA@ 10023d6c     Engine.obj
 0002:00003d7c       ??_C@_0BJ@KAAONFDA@LsaICLookupSidsWithCreds?$AA@ 10023d7c     Engine.obj
 0002:00003d98       ??_C@_0P@PAEMBFNB@LsaLookupNames?$AA@ 10023d98     Engine.obj
 0002:00003da8       ??_C@_0BA@BMFEPKCG@LsaLookupNames2?$AA@ 10023da8     Engine.obj
 0002:00003db8       ??_C@_0BO@HNHJEGKH@LsaLookupPrivilegeDisplayName?$AA@ 10023db8     Engine.obj
 0002:00003dd8       ??_C@_0BH@GHCNPFPP@LsaLookupPrivilegeName?$AA@ 10023dd8     Engine.obj
 0002:00003df0       ??_C@_0BI@HOPABJHJ@LsaLookupPrivilegeValue?$AA@ 10023df0     Engine.obj
 0002:00003e08       ??_C@_0O@MEAHEAJC@LsaLookupSids?$AA@ 10023e08     Engine.obj
 0002:00003e18       ??_C@_0BI@FPEOGFNH@LsaManageSidNameMapping?$AA@ 10023e18     Engine.obj
 0002:00003e30       ??_C@_0BG@DNAKLCMC@LsaNtStatusToWinError?$AA@ 10023e30     Engine.obj
 0002:00003e48       ??_C@_0P@GCGDFHKB@LsaOpenAccount?$AA@ 10023e48     Engine.obj
 0002:00003e58       ??_C@_0O@OFFKAPCB@LsaOpenPolicy?$AA@ 10023e58     Engine.obj
 0002:00003e68       ??_C@_0BB@LOAKMCMA@LsaOpenPolicySce?$AA@ 10023e68     Engine.obj
 0002:00003e7c       ??_C@_0O@MBECHDGK@LsaOpenSecret?$AA@ 10023e7c     Engine.obj
 0002:00003e8c       ??_C@_0BF@PAHABIEM@LsaOpenTrustedDomain?$AA@ 10023e8c     Engine.obj
 0002:00003ea4       ??_C@_0BL@DFJEEMJO@LsaOpenTrustedDomainByName?$AA@ 10023ea4     Engine.obj
 0002:00003ec0       ??_C@_0CA@CMCDOEDP@LsaQueryDomainInformationPolicy?$AA@ 10023ec0     Engine.obj
 0002:00003ee0       ??_C@_0BP@IIDMLDAB@LsaQueryForestTrustInformation?$AA@ 10023ee0     Engine.obj
 0002:00003f00       ??_C@_0BK@DPBDODOE@LsaQueryInfoTrustedDomain?$AA@ 10023f00     Engine.obj
 0002:00003f1c       ??_C@_0BK@KAIEEKJ@LsaQueryInformationPolicy?$AA@ 10023f1c     Engine.obj
 0002:00003f38       ??_C@_0P@MAIJEALK@LsaQuerySecret?$AA@ 10023f38     Engine.obj
 0002:00003f48       ??_C@_0BH@MFAFDCLH@LsaQuerySecurityObject?$AA@ 10023f48     Engine.obj
 0002:00003f60       ??_C@_0BK@LPDKINO@LsaQueryTrustedDomainInfo?$AA@ 10023f60     Engine.obj
 0002:00003f7c       ??_C@_0CA@DDIAHGCG@LsaQueryTrustedDomainInfoByName?$AA@ 10023f7c     Engine.obj
 0002:00003f9c       ??_C@_0BH@PGANHKNF@LsaRemoveAccountRights?$AA@ 10023f9c     Engine.obj
 0002:00003fb4       ??_C@_0BP@JECDGGOA@LsaRemovePrivilegesFromAccount?$AA@ 10023fb4     Engine.obj
 0002:00003fd4       ??_C@_0BH@GDEHBJGK@LsaRetrievePrivateData?$AA@ 10023fd4     Engine.obj
 0002:00003fec       ??_C@_0BO@GAFGMBJD@LsaSetDomainInformationPolicy?$AA@ 10023fec     Engine.obj
 0002:0000400c       ??_C@_0BN@JEAJEBCA@LsaSetForestTrustInformation?$AA@ 1002400c     Engine.obj
 0002:0000402c       ??_C@_0BI@BJNODIBF@LsaSetInformationPolicy?$AA@ 1002402c     Engine.obj
 0002:********       ??_C@_0BP@DNPFBBFF@LsaSetInformationTrustedDomain?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BH@BJBCFIJA@LsaSetQuotasForAccount?$AA@ ********     Engine.obj
 0002:0000407c       ??_C@_0N@CJBAFAGL@LsaSetSecret?$AA@ 1002407c     Engine.obj
 0002:0000408c       ??_C@_0BF@HHMBDKNO@LsaSetSecurityObject?$AA@ 1002408c     Engine.obj
 0002:000040a4       ??_C@_0BK@NKPDHNHG@LsaSetSystemAccessAccount?$AA@ 100240a4     Engine.obj
 0002:000040c0       ??_C@_0BO@HPPFFDIK@LsaSetTrustedDomainInfoByName?$AA@ 100240c0     Engine.obj
 0002:000040e0       ??_C@_0BP@DMDPIPHE@LsaSetTrustedDomainInformation?$AA@ 100240e0     Engine.obj
 0002:********       ??_C@_0BE@LAICHKED@LsaStorePrivateData?$AA@ ********     Engine.obj
 0002:********       ??_C@_08NBFHLLJG@MD4Final?$AA@ ********     Engine.obj
 0002:********       ??_C@_07IFKFBFCO@MD4Init?$AA@ ********     Engine.obj
 0002:********       ??_C@_09CLFNMBEB@MD4Update?$AA@ ********     Engine.obj
 0002:********       ??_C@_08HHCALACC@MD5Final?$AA@ ********     Engine.obj
 0002:********       ??_C@_07EOPJMGIL@MD5Init?$AA@ ********     Engine.obj
 0002:********       ??_C@_09OHPHMBNP@MD5Update?$AA@ ********     Engine.obj
 0002:********       ??_C@_0BI@IPFFLDMP@MSChapSrvChangePassword?$AA@ ********     Engine.obj
 0002:0000416c       ??_C@_0BJ@OGCENOOD@MSChapSrvChangePassword2?$AA@ 1002416c     Engine.obj
 0002:********       ??_C@_0P@LJENFLID@MakeAbsoluteSD?$AA@ 10024188     Engine.obj
 0002:00004198       ??_C@_0BA@JJHIMLLA@MakeAbsoluteSD2?$AA@ 10024198     Engine.obj
 0002:000041a8       ??_C@_0BD@BANFDJKJ@MakeSelfRelativeSD?$AA@ 100241a8     Engine.obj
 0002:000041bc       ??_C@_0P@IECBPJEL@MapGenericMask?$AA@ 100241bc     Engine.obj
 0002:000041cc       ??_C@_0BH@DACDINCN@NotifyBootConfigStatus?$AA@ 100241cc     Engine.obj
 0002:000041e4       ??_C@_0BF@LCEKJDI@NotifyChangeEventLog?$AA@ 100241e4     Engine.obj
 0002:000041fc       ??_C@_0BK@KGGJFHFB@NotifyServiceStatusChange?$AA@ 100241fc     Engine.obj
 0002:00004218       ??_C@_0BL@PFKIHGHB@NotifyServiceStatusChangeA?$AA@ 10024218     Engine.obj
 0002:00004234       ??_C@_0BL@OJDAMDKG@NotifyServiceStatusChangeW?$AA@ 10024234     Engine.obj
 0002:00004250       ??_C@_0BH@EIGCMPGO@ObjectCloseAuditAlarmA?$AA@ 10024250     Engine.obj
 0002:00004268       ??_C@_0BH@FEPKHKLJ@ObjectCloseAuditAlarmW?$AA@ 10024268     Engine.obj
 0002:00004280       ??_C@_0BI@BFAAOGDJ@ObjectDeleteAuditAlarmA?$AA@ 10024280     Engine.obj
 0002:00004298       ??_C@_0BI@JJIFDOO@ObjectDeleteAuditAlarmW?$AA@ 10024298     Engine.obj
 0002:000042b0       ??_C@_0BG@NOIMHDKB@ObjectOpenAuditAlarmA?$AA@ 100242b0     Engine.obj
 0002:000042c8       ??_C@_0BG@MCBEMGHG@ObjectOpenAuditAlarmW?$AA@ 100242c8     Engine.obj
 0002:000042e0       ??_C@_0BL@PCIPBOFF@ObjectPrivilegeAuditAlarmA?$AA@ 100242e0     Engine.obj
 0002:000042fc       ??_C@_0BL@OOBHKLIC@ObjectPrivilegeAuditAlarmW?$AA@ 100242fc     Engine.obj
 0002:00004318       ??_C@_0BE@ILICGECB@OpenBackupEventLogA?$AA@ 10024318     Engine.obj
 0002:0000432c       ??_C@_0BE@JHBKNBPG@OpenBackupEventLogW?$AA@ 1002432c     Engine.obj
 0002:00004340       ??_C@_0BG@GPMNCOOB@OpenEncryptedFileRawA?$AA@ 10024340     Engine.obj
 0002:00004358       ??_C@_0BG@HDFFJLDG@OpenEncryptedFileRawW?$AA@ 10024358     Engine.obj
 0002:00004370       ??_C@_0O@EDGJEOGK@OpenEventLogA?$AA@ 10024370     Engine.obj
 0002:00004380       ??_C@_0O@FPPBPLLN@OpenEventLogW?$AA@ 10024380     Engine.obj
 0002:00004390       ??_C@_0BB@EANJDCJP@OpenProcessToken?$AA@ 10024390     Engine.obj
 0002:000043a4       ??_C@_0P@KIBOOHEE@OpenSCManagerA?$AA@ 100243a4     Engine.obj
 0002:000043b4       ??_C@_0P@LEIGFCJD@OpenSCManagerW?$AA@ 100243b4     Engine.obj
 0002:000043c4       ??_C@_0N@MPAGLEEC@OpenServiceA?$AA@ 100243c4     Engine.obj
 0002:000043d4       ??_C@_0N@NDJOABJF@OpenServiceW?$AA@ 100243d4     Engine.obj
 0002:000043e4       ??_C@_0BA@MOEFBCGC@OpenThreadToken?$AA@ 100243e4     Engine.obj
 0002:000043f4       ??_C@_0BL@MNNEKBGG@OpenThreadWaitChainSession?$AA@ 100243f4     Engine.obj
 0002:00004410       ??_C@_0L@EPNLEADB@OpenTraceA?$AA@ 10024410     Engine.obj
 0002:0000441c       ??_C@_0L@FDEDPFOG@OpenTraceW?$AA@ 1002441c     Engine.obj
 0002:00004428       ??_C@_0BA@KNMDIEKE@PerfAddCounters?$AA@ 10024428     Engine.obj
 0002:00004438       ??_C@_0BF@OOAECKNK@PerfCloseQueryHandle?$AA@ 10024438     Engine.obj
 0002:00004450       ??_C@_0BD@HMJOIFLD@PerfCreateInstance?$AA@ 10024450     Engine.obj
 0002:00004464       ??_C@_0BP@MFMLOGGJ@PerfDecrementULongCounterValue?$AA@ 10024464     Engine.obj
 0002:00004484       ??_C@_0CD@DJGCHJNL@PerfDecrementULongLongCounterVal@ 10024484     Engine.obj
 0002:000044a8       ??_C@_0BD@IGBHFADG@PerfDeleteCounters?$AA@ 100244a8     Engine.obj
 0002:000044bc       ??_C@_0BD@GAHDCNJC@PerfDeleteInstance?$AA@ 100244bc     Engine.obj
 0002:000044d0       ??_C@_0BI@FNOFMMHA@PerfEnumerateCounterSet?$AA@ 100244d0     Engine.obj
 0002:000044e8       ??_C@_0CB@HGODHJOD@PerfEnumerateCounterSetInstances@ 100244e8     Engine.obj
 0002:0000450c       ??_C@_0BP@KPPKIAKD@PerfIncrementULongCounterValue?$AA@ 1002450c     Engine.obj
 0002:0000452c       ??_C@_0CD@HPCEPNP@PerfIncrementULongLongCounterVal@ 1002452c     Engine.obj
 0002:00004550       ??_C@_0BE@DPLOLOPN@PerfOpenQueryHandle?$AA@ 10024550     Engine.obj
 0002:00004564       ??_C@_0BF@HJHDICCD@PerfQueryCounterData?$AA@ 10024564     Engine.obj
 0002:0000457c       ??_C@_0BF@FIKBAMFE@PerfQueryCounterInfo?$AA@ 1002457c     Engine.obj
 0002:00004594       ??_C@_0CE@MKIBGBPB@PerfQueryCounterSetRegistrationI@ 10024594     Engine.obj
 0002:000045b8       ??_C@_0BC@GFGMKNMF@PerfQueryInstance?$AA@ 100245b8     Engine.obj
 0002:000045cc       ??_C@_0BH@BIKGGJBC@PerfSetCounterRefValue?$AA@ 100245cc     Engine.obj
 0002:000045e4       ??_C@_0BG@KJCLDMAG@PerfSetCounterSetInfo?$AA@ 100245e4     Engine.obj
 0002:000045fc       ??_C@_0BJ@BHCLGLJG@PerfSetULongCounterValue?$AA@ 100245fc     Engine.obj
 0002:00004618       ??_C@_0BN@DICJLHON@PerfSetULongLongCounterValue?$AA@ 10024618     Engine.obj
 0002:00004638       ??_C@_0BC@DHGONEFM@PerfStartProvider?$AA@ 10024638     Engine.obj
 0002:0000464c       ??_C@_0BE@GAHIPCO@PerfStartProviderEx?$AA@ 1002464c     Engine.obj
 0002:00004660       ??_C@_0BB@MGCGJDG@PerfStopProvider?$AA@ 10024660     Engine.obj
 0002:00004674       ??_C@_0P@PKFNFNEC@PrivilegeCheck?$AA@ 10024674     Engine.obj
 0002:00004684       ??_C@_0BN@JONADHLJ@PrivilegedServiceAuditAlarmA?$AA@ 10024684     Engine.obj
 0002:000046a4       ??_C@_0BN@ICEIICGO@PrivilegedServiceAuditAlarmW?$AA@ 100246a4     Engine.obj
 0002:000046c4       ??_C@_0BB@NCLOHJGI@ProcessIdleTasks?$AA@ 100246c4     Engine.obj
 0002:000046d8       ??_C@_0BC@LGEBJMIA@ProcessIdleTasksW?$AA@ 100246d8     Engine.obj
 0002:000046ec       ??_C@_0N@OEKLFHHH@ProcessTrace?$AA@ 100246ec     Engine.obj
 0002:000046fc       ??_C@_0BA@EJFNAINH@QueryAllTracesA?$AA@ 100246fc     Engine.obj
 0002:0000470c       ??_C@_0BA@FFMFLNAA@QueryAllTracesW?$AA@ 1002470c     Engine.obj
 0002:0000471c       ??_C@_0CD@IIAPMOBL@QueryRecoveryAgentsOnEncryptedFi@ 1002471c     Engine.obj
 0002:00004740       ??_C@_0BI@KMOBBNDJ@QuerySecurityAccessMask?$AA@ 10024740     Engine.obj
 0002:00004758       ??_C@_0BF@DLEBDGPO@QueryServiceConfig2A?$AA@ 10024758     Engine.obj
 0002:00004770       ??_C@_0BF@CHNJIDCJ@QueryServiceConfig2W?$AA@ 10024770     Engine.obj
 0002:00004788       ??_C@_0BE@CKNPGNBD@QueryServiceConfigA?$AA@ 10024788     Engine.obj
 0002:0000479c       ??_C@_0BE@DGEHNIME@QueryServiceConfigW?$AA@ 1002479c     Engine.obj
 0002:000047b0       ??_C@_0BI@DGMDBKO@QueryServiceLockStatusA?$AA@ 100247b0     Engine.obj
 0002:000047c8       ??_C@_0BI@BPPEIEHJ@QueryServiceLockStatusW?$AA@ 100247c8     Engine.obj
 0002:000047e0       ??_C@_0BL@NONNDJIE@QueryServiceObjectSecurity?$AA@ 100247e0     Engine.obj
 0002:000047fc       ??_C@_0BD@PMIABNOP@QueryServiceStatus?$AA@ 100247fc     Engine.obj
 0002:00004810       ??_C@_0BF@CLFHMDKP@QueryServiceStatusEx?$AA@ 10024810     Engine.obj
 0002:00004828       ??_C@_0M@ENMKGPGP@QueryTraceA?$AA@ 10024828     Engine.obj
 0002:00004834       ??_C@_0M@FBFCNKLI@QueryTraceW?$AA@ 10024834     Engine.obj
 0002:00004840       ??_C@_0BK@CBFCPEDL@QueryUsersOnEncryptedFile?$AA@ 10024840     Engine.obj
 0002:0000485c       ??_C@_0BF@KPBGFODI@ReadEncryptedFileRaw?$AA@ 1002485c     Engine.obj
 0002:00004874       ??_C@_0O@NKKAHEGP@ReadEventLogA?$AA@ 10024874     Engine.obj
 0002:00004884       ??_C@_0O@MGDIMBLI@ReadEventLogW?$AA@ 10024884     Engine.obj
 0002:00004894       ??_C@_0M@HLOHPNFA@RegCloseKey?$AA@ 10024894     Engine.obj
 0002:000048a0       ??_C@_0BE@HCCLCHKP@RegConnectRegistryA?$AA@ 100248a0     Engine.obj
 0002:000048b4       ??_C@_0BG@LAHHLHJN@RegConnectRegistryExA?$AA@ 100248b4     Engine.obj
 0002:000048cc       ??_C@_0BG@KMOPACEK@RegConnectRegistryExW?$AA@ 100248cc     Engine.obj
 0002:000048e4       ??_C@_0BE@GOLDJCHI@RegConnectRegistryW?$AA@ 100248e4     Engine.obj
 0002:000048f8       ??_C@_0N@MMDPNMKK@RegCopyTreeA?$AA@ 100248f8     Engine.obj
 0002:00004908       ??_C@_0N@NAKHGJHN@RegCopyTreeW?$AA@ 10024908     Engine.obj
 0002:00004918       ??_C@_0O@MBBDACGE@RegCreateKeyA?$AA@ 10024918     Engine.obj
 0002:00004928       ??_C@_0BA@NDHNNOGH@RegCreateKeyExA?$AA@ 10024928     Engine.obj
 0002:00004938       ??_C@_0BA@MPOFGLLA@RegCreateKeyExW?$AA@ 10024938     Engine.obj
 0002:00004948       ??_C@_0BI@NBLNJKIN@RegCreateKeyTransactedA?$AA@ 10024948     Engine.obj
 0002:00004960       ??_C@_0BI@MNCFCPFK@RegCreateKeyTransactedW?$AA@ 10024960     Engine.obj
 0002:00004978       ??_C@_0O@NNILLHLD@RegCreateKeyW?$AA@ 10024978     Engine.obj
 0002:00004988       ??_C@_0O@FLDDFNH@RegDeleteKeyA?$AA@ 10024988     Engine.obj
 0002:00004998       ??_C@_0BA@KECIECLD@RegDeleteKeyExA?$AA@ 10024998     Engine.obj
 0002:000049a8       ??_C@_0BA@LILAPHGE@RegDeleteKeyExW?$AA@ 100249a8     Engine.obj
 0002:000049b8       ??_C@_0BI@GMDDFLLI@RegDeleteKeyTransactedA?$AA@ 100249b8     Engine.obj
 0002:000049d0       ??_C@_0BI@HAKLOOGP@RegDeleteKeyTransactedW?$AA@ 100249d0     Engine.obj
 0002:000049e8       ??_C@_0BD@IAMDNMAE@RegDeleteKeyValueA?$AA@ 100249e8     Engine.obj
 0002:000049fc       ??_C@_0BD@JMFLGJND@RegDeleteKeyValueW?$AA@ 100249fc     Engine.obj
 0002:00004a10       ??_C@_0O@BJCLIAAA@RegDeleteKeyW?$AA@ 10024a10     Engine.obj
 0002:00004a20       ??_C@_0P@PEBLHHMB@RegDeleteTreeA?$AA@ 10024a20     Engine.obj
 0002:00004a30       ??_C@_0P@OIIDMCBG@RegDeleteTreeW?$AA@ 10024a30     Engine.obj
 0002:00004a40       ??_C@_0BA@HFGFCNNL@RegDeleteValueA?$AA@ 10024a40     Engine.obj
 0002:00004a50       ??_C@_0BA@GJPNJIAM@RegDeleteValueW?$AA@ 10024a50     Engine.obj
 0002:00004a60       ??_C@_0BK@KIOOJJCF@RegDisablePredefinedCache?$AA@ 10024a60     Engine.obj
 0002:00004a7c       ??_C@_0BM@PAJHOPDM@RegDisablePredefinedCacheEx?$AA@ 10024a7c     Engine.obj
 0002:00004a98       ??_C@_0BI@MCEAONBI@RegDisableReflectionKey?$AA@ 10024a98     Engine.obj
 0002:00004ab0       ??_C@_0BH@MJKGPAOP@RegEnableReflectionKey?$AA@ 10024ab0     Engine.obj
 0002:00004ac8       ??_C@_0M@IMBFIMEL@RegEnumKeyA?$AA@ 10024ac8     Engine.obj
 0002:00004ad4       ??_C@_0O@MLGBAFCL@RegEnumKeyExA?$AA@ 10024ad4     Engine.obj
 0002:00004ae4       ??_C@_0O@NHPJLAPM@RegEnumKeyExW?$AA@ 10024ae4     Engine.obj
 0002:00004af4       ??_C@_0M@JAINDJJM@RegEnumKeyW?$AA@ 10024af4     Engine.obj
 0002:00004b00       ??_C@_0O@BKCMGKED@RegEnumValueA?$AA@ 10024b00     Engine.obj
 0002:00004b10       ??_C@_0O@GLENPJE@RegEnumValueW?$AA@ 10024b10     Engine.obj
 0002:00004b20       ??_C@_0M@KEOLPCO@RegFlushKey?$AA@ 10024b20     Engine.obj
 0002:00004b2c       ??_C@_0BC@FJFGKNKN@RegGetKeySecurity?$AA@ 10024b2c     Engine.obj
 0002:00004b40       ??_C@_0N@DGDLFJKD@RegGetValueA?$AA@ 10024b40     Engine.obj
 0002:00004b50       ??_C@_0N@CKKDOMHE@RegGetValueW?$AA@ 10024b50     Engine.obj
 0002:00004b60       ??_C@_0P@CPMGOPPL@RegLoadAppKeyA?$AA@ 10024b60     Engine.obj
 0002:00004b70       ??_C@_0P@DDFOFKCM@RegLoadAppKeyW?$AA@ 10024b70     Engine.obj
 0002:00004b80       ??_C@_0M@FIICNDJL@RegLoadKeyA?$AA@ 10024b80     Engine.obj
 0002:00004b8c       ??_C@_0M@EEBKGGEM@RegLoadKeyW?$AA@ 10024b8c     Engine.obj
 0002:00004b98       ??_C@_0BC@LJAODCFB@RegLoadMUIStringA?$AA@ 10024b98     Engine.obj
 0002:00004bac       ??_C@_0BC@KFJGIHIG@RegLoadMUIStringW?$AA@ 10024bac     Engine.obj
 0002:00004bc0       ??_C@_0BI@ILHBFDHH@RegNotifyChangeKeyValue?$AA@ 10024bc0     Engine.obj
 0002:00004bd8       ??_C@_0BD@OINALHPG@RegOpenCurrentUser?$AA@ 10024bd8     Engine.obj
 0002:00004bec       ??_C@_0M@JBEAMLKM@RegOpenKeyA?$AA@ 10024bec     Engine.obj
 0002:00004bf8       ??_C@_0O@DCOGKJGG@RegOpenKeyExA?$AA@ 10024bf8     Engine.obj
 0002:00004c08       ??_C@_0O@COHOBMLB@RegOpenKeyExW?$AA@ 10024c08     Engine.obj
 0002:00004c18       ??_C@_0BG@FGMNHMFJ@RegOpenKeyTransactedA?$AA@ 10024c18     Engine.obj
 0002:00004c30       ??_C@_0BG@EKFFMJIO@RegOpenKeyTransactedW?$AA@ 10024c30     Engine.obj
 0002:00004c48       ??_C@_0M@INNIHOHL@RegOpenKeyW?$AA@ 10024c48     Engine.obj
 0002:00004c54       ??_C@_0BH@KFNIPFHH@RegOpenUserClassesRoot?$AA@ 10024c54     Engine.obj
 0002:00004c6c       ??_C@_0BF@BIAKKKJD@RegOverridePredefKey?$AA@ 10024c6c     Engine.obj
 0002:00004c84       ??_C@_0BB@ECNEDBH@RegQueryInfoKeyA?$AA@ 10024c84     Engine.obj
 0002:00004c98       ??_C@_0BB@BILFPGMA@RegQueryInfoKeyW?$AA@ 10024c98     Engine.obj
 0002:00004cac       ??_C@_0BI@CEEIBGKL@RegQueryMultipleValuesA?$AA@ 10024cac     Engine.obj
 0002:00004cc4       ??_C@_0BI@DINAKDHM@RegQueryMultipleValuesW?$AA@ 10024cc4     Engine.obj
 0002:00004cdc       ??_C@_0BG@DOLEDKPN@RegQueryReflectionKey?$AA@ 10024cdc     Engine.obj
 0002:00004cf4       ??_C@_0P@CDBCGPOI@RegQueryValueA?$AA@ 10024cf4     Engine.obj
 0002:00004d04       ??_C@_0BB@HHEIPGME@RegQueryValueExA?$AA@ 10024d04     Engine.obj
 0002:00004d18       ??_C@_0BB@GLNAEDBD@RegQueryValueExW?$AA@ 10024d18     Engine.obj
 0002:00004d2c       ??_C@_0P@DPIKNKDP@RegQueryValueW?$AA@ 10024d2c     Engine.obj
 0002:00004d3c       ??_C@_0N@KLKDEFD@RegRenameKey?$AA@ 10024d3c     Engine.obj
 0002:00004d4c       ??_C@_0P@KHKPLGHO@RegReplaceKeyA?$AA@ 10024d4c     Engine.obj
 0002:00004d5c       ??_C@_0P@LLDHADKJ@RegReplaceKeyW?$AA@ 10024d5c     Engine.obj
 0002:00004d6c       ??_C@_0P@IPKDGIFM@RegRestoreKeyA?$AA@ 10024d6c     Engine.obj
 0002:00004d7c       ??_C@_0P@JDDLNNIL@RegRestoreKeyW?$AA@ 10024d7c     Engine.obj
 0002:00004d8c       ??_C@_0M@FHFEDNOM@RegSaveKeyA?$AA@ 10024d8c     Engine.obj
 0002:00004d98       ??_C@_0O@JGEBHHFO@RegSaveKeyExA?$AA@ 10024d98     Engine.obj
 0002:00004da8       ??_C@_0O@IKNJMCIJ@RegSaveKeyExW?$AA@ 10024da8     Engine.obj
 0002:00004db8       ??_C@_0M@ELMMIIDL@RegSaveKeyW?$AA@ 10024db8     Engine.obj
 0002:00004dc4       ??_C@_0BC@JHEIFHIB@RegSetKeySecurity?$AA@ 10024dc4     Engine.obj
 0002:00004dd8       ??_C@_0BA@GJBPOJOC@RegSetKeyValueA?$AA@ 10024dd8     Engine.obj
 0002:00004de8       ??_C@_0BA@HFIHFMDF@RegSetKeyValueW?$AA@ 10024de8     Engine.obj
 0002:00004df8       ??_C@_0N@BIHOHIDB@RegSetValueA?$AA@ 10024df8     Engine.obj
 0002:00004e08       ??_C@_0P@KEGBDGDJ@RegSetValueExA?$AA@ 10024e08     Engine.obj
 0002:00004e18       ??_C@_0P@LIPJIDOO@RegSetValueExW?$AA@ 10024e18     Engine.obj
 0002:00004e28       ??_C@_0N@EOGMNOG@RegSetValueW?$AA@ 10024e28     Engine.obj
 0002:00004e38       ??_C@_0O@HPKACLIM@RegUnLoadKeyA?$AA@ 10024e38     Engine.obj
 0002:00004e48       ??_C@_0O@GDDIJOFL@RegUnLoadKeyW?$AA@ 10024e48     Engine.obj
 0002:00004e58       ??_C@_0BF@EBHPDBGP@RegisterEventSourceA?$AA@ 10024e58     Engine.obj
 0002:00004e70       ??_C@_0BF@FNOHIELI@RegisterEventSourceW?$AA@ 10024e70     Engine.obj
 0002:00004e88       ??_C@_0BB@EPNCPDHO@RegisterIdleTask?$AA@ 10024e88     Engine.obj
 0002:00004e9c       ??_C@_0BM@DMNNDIFN@RegisterServiceCtrlHandlerA?$AA@ 10024e9c     Engine.obj
 0002:00004eb8       ??_C@_0BO@BLPFHHKB@RegisterServiceCtrlHandlerExA?$AA@ 10024eb8     Engine.obj
 0002:00004ed8       ??_C@_0BO@HGNMCHG@RegisterServiceCtrlHandlerExW?$AA@ 10024ed8     Engine.obj
 0002:00004ef8       ??_C@_0BM@CAEFINIK@RegisterServiceCtrlHandlerW?$AA@ 10024ef8     Engine.obj
 0002:00004f14       ??_C@_0BE@BJDBHDBC@RegisterTraceGuidsA?$AA@ 10024f14     Engine.obj
 0002:00004f28       ??_C@_0BE@FKJMGMF@RegisterTraceGuidsW?$AA@ 10024f28     Engine.obj
 0002:00004f3c       ??_C@_0BN@NFOPLPGA@RegisterWaitChainCOMCallback?$AA@ 10024f3c     Engine.obj
 0002:00004f5c       ??_C@_0BE@CLBLNLJE@RemoveTraceCallback?$AA@ 10024f5c     Engine.obj
 0002:00004f70       ??_C@_0BN@GLJFDLCO@RemoveUsersFromEncryptedFile?$AA@ 10024f70     Engine.obj
 0002:00004f90       ??_C@_0N@PKNECLKP@ReportEventA?$AA@ 10024f90     Engine.obj
 0002:00004fa0       ??_C@_0N@OGEMJOHI@ReportEventW?$AA@ 10024fa0     Engine.obj
 0002:00004fb0       ??_C@_0N@NBKDDPIG@RevertToSelf?$AA@ 10024fb0     Engine.obj
 0002:00004fc0       ??_C@_0BA@LKBACENA@SaferCloseLevel?$AA@ 10024fc0     Engine.obj
 0002:00004fd0       ??_C@_0BL@DCJPBPKO@SaferComputeTokenFromLevel?$AA@ 10024fd0     Engine.obj
 0002:00004fec       ??_C@_0BB@IGBPMEAA@SaferCreateLevel?$AA@ 10024fec     Engine.obj
 0002:00005000       ??_C@_0BJ@DKPBKKM@SaferGetLevelInformation?$AA@ 10025000     Engine.obj
 0002:0000501c       ??_C@_0BK@NIOOGJOJ@SaferGetPolicyInformation?$AA@ 1002501c     Engine.obj
 0002:00005038       ??_C@_0BD@PKMJBCIM@SaferIdentifyLevel?$AA@ 10025038     Engine.obj
 0002:0000504c       ??_C@_0BJ@NOAPPAP@SaferRecordEventLogEntry?$AA@ 1002504c     Engine.obj
 0002:00005068       ??_C@_0BJ@KNIEFKNA@SaferSetLevelInformation?$AA@ 10025068     Engine.obj
 0002:00005084       ??_C@_0BK@IBPDHPLO@SaferSetPolicyInformation?$AA@ 10025084     Engine.obj
 0002:000050a0       ??_C@_0BK@DMJLLNE@SaferiChangeRegistryScope?$AA@ 100250a0     Engine.obj
 0002:000050bc       ??_C@_0BJ@CBOPBNKK@SaferiCompareTokenLevels?$AA@ 100250bc     Engine.obj
 0002:000050d8       ??_C@_0BD@MBCLHCBP@SaferiIsDllAllowed?$AA@ 100250d8     Engine.obj
 0002:000050ec       ??_C@_0BL@BIEHGGMJ@SaferiIsExecutableFileType?$AA@ 100250ec     Engine.obj
 0002:00005108       ??_C@_0CB@OFAJGCPJ@SaferiPopulateDefaultsInRegistry@ 10025108     Engine.obj
 0002:0000512c       ??_C@_0BK@DBJIDCMM@SaferiRecordEventLogEntry?$AA@ 1002512c     Engine.obj
 0002:00005148       ??_C@_0BO@LDPBCAHC@SaferiSearchMatchingHashRules?$AA@ 10025148     Engine.obj
 0002:00005168       ??_C@_0BC@LINLLFOO@SetAclInformation?$AA@ 10025168     Engine.obj
 0002:0000517c       ??_C@_0BJ@JIIFLAAF@SetEncryptedFileMetadata?$AA@ 1002517c     Engine.obj
 0002:00005198       ??_C@_0BI@KHIHKCMG@SetEntriesInAccessListA?$AA@ 10025198     Engine.obj
 0002:000051b0       ??_C@_0BI@LLBPBHBB@SetEntriesInAccessListW?$AA@ 100251b0     Engine.obj
 0002:000051c8       ??_C@_0BB@EOLDBOGM@SetEntriesInAclA?$AA@ 100251c8     Engine.obj
 0002:000051dc       ??_C@_0BB@FCCLKLLL@SetEntriesInAclW?$AA@ 100251dc     Engine.obj
 0002:000051f0       ??_C@_0BH@FJFBBHIB@SetEntriesInAuditListA?$AA@ 100251f0     Engine.obj
 0002:00005208       ??_C@_0BH@EFMJKCFG@SetEntriesInAuditListW?$AA@ 10025208     Engine.obj
 0002:00005220       ??_C@_0BB@PMFHDDMB@SetFileSecurityA?$AA@ 10025220     Engine.obj
 0002:00005234       ??_C@_0BB@OAMPIGBG@SetFileSecurityW?$AA@ 10025234     Engine.obj
 0002:00005248       ??_C@_0BO@EGHLHHOC@SetInformationCodeAuthzLevelW?$AA@ 10025248     Engine.obj
 0002:00005268       ??_C@_0BP@DJNBPPNK@SetInformationCodeAuthzPolicyW?$AA@ 10025268     Engine.obj
 0002:00005288       ??_C@_0BI@CIHCMAMM@SetKernelObjectSecurity?$AA@ 10025288     Engine.obj
 0002:000052a0       ??_C@_0BG@CILHLCDC@SetNamedSecurityInfoA?$AA@ 100252a0     Engine.obj
 0002:000052b8       ??_C@_0BI@PEPNHOJN@SetNamedSecurityInfoExA?$AA@ 100252b8     Engine.obj
 0002:000052d0       ??_C@_0BI@OIGFMLEK@SetNamedSecurityInfoExW?$AA@ 100252d0     Engine.obj
 0002:000052e8       ??_C@_0BG@DECPAHOF@SetNamedSecurityInfoW?$AA@ 100252e8     Engine.obj
 0002:00005300       ??_C@_0BJ@CMLNMFLG@SetPrivateObjectSecurity?$AA@ 10025300     Engine.obj
 0002:0000531c       ??_C@_0BL@MICGKPGJ@SetPrivateObjectSecurityEx?$AA@ 1002531c     Engine.obj
 0002:00005338       ??_C@_0BG@CFCCEOOO@SetSecurityAccessMask?$AA@ 10025338     Engine.obj
 0002:00005350       ??_C@_0BN@BJAHFDJO@SetSecurityDescriptorControl?$AA@ 10025350     Engine.obj
 0002:00005370       ??_C@_0BK@DNNLOFO@SetSecurityDescriptorDacl?$AA@ 10025370     Engine.obj
 0002:0000538c       ??_C@_0BL@FLBKGAAA@SetSecurityDescriptorGroup?$AA@ 1002538c     Engine.obj
 0002:000053a8       ??_C@_0BL@OJAFMLIK@SetSecurityDescriptorOwner?$AA@ 100253a8     Engine.obj
 0002:000053c4       ??_C@_0BP@CHEAKNPE@SetSecurityDescriptorRMControl?$AA@ 100253c4     Engine.obj
 0002:000053e4       ??_C@_0BK@NBBNPFMM@SetSecurityDescriptorSacl?$AA@ 100253e4     Engine.obj
 0002:00005400       ??_C@_0BA@KIJGHDEI@SetSecurityInfo?$AA@ 10025400     Engine.obj
 0002:00005410       ??_C@_0BD@HMLNCPDH@SetSecurityInfoExA?$AA@ 10025410     Engine.obj
 0002:00005424       ??_C@_0BD@GACFJKOA@SetSecurityInfoExW?$AA@ 10025424     Engine.obj
 0002:00005438       ??_C@_0P@MAKMPCGB@SetServiceBits?$AA@ 10025438     Engine.obj
 0002:00005448       ??_C@_0BJ@MFOAIHCF@SetServiceObjectSecurity?$AA@ 10025448     Engine.obj
 0002:00005464       ??_C@_0BB@DCELCPIA@SetServiceStatus?$AA@ 10025464     Engine.obj
 0002:00005478       ??_C@_0P@ECFJFPAL@SetThreadToken?$AA@ 10025478     Engine.obj
 0002:00005488       ??_C@_0BE@DGBAMKJH@SetTokenInformation?$AA@ 10025488     Engine.obj
 0002:0000549c       ??_C@_0BB@CNCLNBDL@SetTraceCallback?$AA@ 1002549c     Engine.obj
 0002:000054b0       ??_C@_0BJ@OLLCOMEI@SetUserFileEncryptionKey?$AA@ 100254b0     Engine.obj
 0002:000054cc       ??_C@_0BL@KLDMDJ@SetUserFileEncryptionKeyEx?$AA@ 100254cc     Engine.obj
 0002:000054e8       ??_C@_0O@IKOPFGDH@StartServiceA?$AA@ 100254e8     Engine.obj
 0002:000054f8       ??_C@_0BM@DIHJHAOJ@StartServiceCtrlDispatcherA?$AA@ 100254f8     Engine.obj
 0002:00005514       ??_C@_0BM@CEOBMFDO@StartServiceCtrlDispatcherW?$AA@ 10025514     Engine.obj
 0002:00005530       ??_C@_0O@JGHHODOA@StartServiceW?$AA@ 10025530     Engine.obj
 0002:00005540       ??_C@_0M@PCJBJLJL@StartTraceA?$AA@ 10025540     Engine.obj
 0002:0000554c       ??_C@_0M@OOAJCOEM@StartTraceW?$AA@ 1002554c     Engine.obj
 0002:00005558       ??_C@_0L@BOMDBPMA@StopTraceA?$AA@ 10025558     Engine.obj
 0002:00005564       ??_C@_0L@CFLKKBH@StopTraceW?$AA@ 10025564     Engine.obj
 0002:00005570       ??_C@_0BC@NJADPFDJ@SystemFunction001?$AA@ 10025570     Engine.obj
 0002:00005584       ??_C@_0BC@PCCOKGPK@SystemFunction002?$AA@ 10025584     Engine.obj
 0002:00005598       ??_C@_0BC@OLDFJHLL@SystemFunction003?$AA@ 10025598     Engine.obj
 0002:000055ac       ??_C@_0BC@KEHEABHM@SystemFunction004?$AA@ 100255ac     Engine.obj
 0002:000055c0       ??_C@_0BC@LNGPDADN@SystemFunction005?$AA@ 100255c0     Engine.obj
 0002:000055d4       ??_C@_0BC@JGECGDPO@SystemFunction006?$AA@ 100255d4     Engine.obj
 0002:000055e8       ??_C@_0BC@IPFJFCLP@SystemFunction007?$AA@ 100255e8     Engine.obj
 0002:000055fc       ??_C@_0BC@IMBEOHA@SystemFunction008?$AA@ 100255fc     Engine.obj
 0002:00005610       ??_C@_0BC@BBNKHPDB@SystemFunction009?$AA@ 10025610     Engine.obj
 0002:00005624       ??_C@_0BC@MBNKKOEP@SystemFunction010?$AA@ 10025624     Engine.obj
 0002:00005638       ??_C@_0BC@NIMBJPAO@SystemFunction011?$AA@ 10025638     Engine.obj
 0002:0000564c       ??_C@_0BC@PDOMMMMN@SystemFunction012?$AA@ 1002564c     Engine.obj
 0002:00005660       ??_C@_0BC@OKPHPNIM@SystemFunction013?$AA@ 10025660     Engine.obj
 0002:00005674       ??_C@_0BC@KFLGGLEL@SystemFunction014?$AA@ 10025674     Engine.obj
 0002:00005688       ??_C@_0BC@LMKNFKAK@SystemFunction015?$AA@ 10025688     Engine.obj
 0002:0000569c       ??_C@_0BC@JHIAAJMJ@SystemFunction016?$AA@ 1002569c     Engine.obj
 0002:000056b0       ??_C@_0BC@IOJLDIII@SystemFunction017?$AA@ 100256b0     Engine.obj
 0002:000056c4       ??_C@_0BC@JADCEEH@SystemFunction018?$AA@ 100256c4     Engine.obj
 0002:000056d8       ??_C@_0BC@BABIBFAG@SystemFunction019?$AA@ 100256d8     Engine.obj
 0002:000056ec       ??_C@_0BC@MDJMBABG@SystemFunction020?$AA@ 100256ec     Engine.obj
 0002:00005700       ??_C@_0BC@NKIHCBFH@SystemFunction021?$AA@ 10025700     Engine.obj
 0002:00005714       ??_C@_0BC@PBKKHCJE@SystemFunction022?$AA@ 10025714     Engine.obj
 0002:00005728       ??_C@_0BC@OILBEDNF@SystemFunction023?$AA@ 10025728     Engine.obj
 0002:0000573c       ??_C@_0BC@KHPANFBC@SystemFunction024?$AA@ 1002573c     Engine.obj
 0002:00005750       ??_C@_0BC@LOOLOEFD@SystemFunction025?$AA@ 10025750     Engine.obj
 0002:00005764       ??_C@_0BC@JFMGLHJA@SystemFunction026?$AA@ 10025764     Engine.obj
 0002:00005778       ??_C@_0BC@IMNNIGNB@SystemFunction027?$AA@ 10025778     Engine.obj
 0002:0000578c       ??_C@_0BC@LEFJKBO@SystemFunction028?$AA@ 1002578c     Engine.obj
 0002:000057a0       ??_C@_0BC@BCFOKLFP@SystemFunction029?$AA@ 100257a0     Engine.obj
 0002:000057b4       ??_C@_0BC@MCFOHKCB@SystemFunction030?$AA@ 100257b4     Engine.obj
 0002:000057c8       ??_C@_0BC@NLEFELGA@SystemFunction031?$AA@ 100257c8     Engine.obj
 0002:000057dc       ??_C@_0BC@PAGIBIKD@SystemFunction032?$AA@ 100257dc     Engine.obj
 0002:000057f0       ??_C@_0BC@OJHDCJOC@SystemFunction033?$AA@ 100257f0     Engine.obj
 0002:00005804       ??_C@_0BC@KGDCLPCF@SystemFunction034?$AA@ 10025804     Engine.obj
 0002:00005818       ??_C@_0BC@LPCJIOGE@SystemFunction035?$AA@ 10025818     Engine.obj
 0002:0000582c       ??_C@_0BC@JEAENNKH@SystemFunction036?$AA@ 1002582c     Engine.obj
 0002:00005840       ??_C@_0BC@MHBBGMKE@SystemFunction040?$AA@ 10025840     Engine.obj
 0002:00005854       ??_C@_0BC@NOAKFNOF@SystemFunction041?$AA@ 10025854     Engine.obj
 0002:00005868       ??_C@_0L@EOLEDHLE@TraceEvent?$AA@ 10025868     Engine.obj
 0002:00005874       ??_C@_0BD@IODDECCI@TraceEventInstance?$AA@ 10025874     Engine.obj
 0002:00005888       ??_C@_0N@MCONEKLO@TraceMessage?$AA@ 10025888     Engine.obj
 0002:00005898       ??_C@_0P@PIJIOBLB@TraceMessageVa?$AA@ 10025898     Engine.obj
 0002:000058a8       ??_C@_0BE@CENECBPA@TraceSetInformation?$AA@ 100258a8     Engine.obj
 0002:000058bc       ??_C@_0BM@KKJPBLBH@TreeResetNamedSecurityInfoA?$AA@ 100258bc     Engine.obj
 0002:000058d8       ??_C@_0BM@LGAHKOMA@TreeResetNamedSecurityInfoW?$AA@ 100258d8     Engine.obj
 0002:000058f4       ??_C@_0BK@EDFGCCL@TreeSetNamedSecurityInfoA?$AA@ 100258f4     Engine.obj
 0002:00005910       ??_C@_0BK@BIKNNHPM@TreeSetNamedSecurityInfoW?$AA@ 10025910     Engine.obj
 0002:0000592c       ??_C@_0BH@CMLALJDE@TrusteeAccessToObjectA?$AA@ 1002592c     Engine.obj
 0002:00005944       ??_C@_0BH@DACIAMOD@TrusteeAccessToObjectW?$AA@ 10025944     Engine.obj
 0002:0000595c       ??_C@_0BF@FJPEFGCM@UninstallApplication?$AA@ 1002595c     Engine.obj
 0002:00005974       ??_C@_0BG@HANNDOHA@UnlockServiceDatabase?$AA@ 10025974     Engine.obj
 0002:0000598c       ??_C@_0BD@OMLAHJBH@UnregisterIdleTask?$AA@ 1002598c     Engine.obj
 0002:000059a0       ??_C@_0BF@OFMMALCC@UnregisterTraceGuids?$AA@ 100259a0     Engine.obj
 0002:000059b8       ??_C@_0N@JAMHBKOI@UpdateTraceA?$AA@ 100259b8     Engine.obj
 0002:000059c8       ??_C@_0N@IMFPKPDP@UpdateTraceW?$AA@ 100259c8     Engine.obj
 0002:000059d8       ??_C@_0BJ@PJGIGGNF@UsePinForEncryptedFilesA?$AA@ 100259d8     Engine.obj
 0002:000059f4       ??_C@_0BJ@OFPANDAC@UsePinForEncryptedFilesW?$AA@ 100259f4     Engine.obj
 0002:00005a10       ??_C@_0O@NNNDDKOD@WmiCloseBlock?$AA@ 10025a10     Engine.obj
 0002:00005a20       ??_C@_0BK@BOMKBFFO@WmiDevInstToInstanceNameA?$AA@ 10025a20     Engine.obj
 0002:00005a3c       ??_C@_0BK@CFCKAIJ@WmiDevInstToInstanceNameW?$AA@ 10025a3c     Engine.obj
 0002:00005a58       ??_C@_0BC@IAKHHOPO@WmiEnumerateGuids?$AA@ 10025a58     Engine.obj
 0002:00005a6c       ??_C@_0BC@PFOCPMKL@WmiExecuteMethodA?$AA@ 10025a6c     Engine.obj
 0002:00005a80       ??_C@_0BC@OJHKEJHM@WmiExecuteMethodW?$AA@ 10025a80     Engine.obj
 0002:00005a94       ??_C@_0BN@KLDNLAF@WmiFileHandleToInstanceNameA?$AA@ 10025a94     Engine.obj
 0002:00005ab4       ??_C@_0BN@BGCLGONC@WmiFileHandleToInstanceNameW?$AA@ 10025ab4     Engine.obj
 0002:00005ad4       ??_C@_0O@BEJCHEO@WmiFreeBuffer?$AA@ 10025ad4     Engine.obj
 0002:00005ae4       ??_C@_0BK@FIFNKAOD@WmiMofEnumerateResourcesA?$AA@ 10025ae4     Engine.obj
 0002:00005b00       ??_C@_0BK@EEMFBFDE@WmiMofEnumerateResourcesW?$AA@ 10025b00     Engine.obj
 0002:00005b1c       ??_C@_0BN@NFLGJ@WmiNotificationRegistrationA?$AA@ 10025b1c     Engine.obj
 0002:00005b3c       ??_C@_0BN@BMJFOOLO@WmiNotificationRegistrationW?$AA@ 10025b3c     Engine.obj
 0002:00005b5c       ??_C@_0N@HEAJFKPL@WmiOpenBlock?$AA@ 10025b5c     Engine.obj
 0002:00005b6c       ??_C@_0BB@CPOENCOI@WmiQueryAllDataA?$AA@ 10025b6c     Engine.obj
 0002:00005b80       ??_C@_0BJ@JNOBGLEI@WmiQueryAllDataMultipleA?$AA@ 10025b80     Engine.obj
 0002:00005b9c       ??_C@_0BJ@IBHJNOJP@WmiQueryAllDataMultipleW?$AA@ 10025b9c     Engine.obj
 0002:00005bb8       ??_C@_0BB@DDHMGHDP@WmiQueryAllDataW?$AA@ 10025bb8     Engine.obj
 0002:00005bcc       ??_C@_0BI@NDFOGPPL@WmiQueryGuidInformation?$AA@ 10025bcc     Engine.obj
 0002:00005be4       ??_C@_0BI@FGMMIBIK@WmiQuerySingleInstanceA?$AA@ 10025be4     Engine.obj
 0002:00005bfc       ??_C@_0CA@CCPDKEB@WmiQuerySingleInstanceMultipleA?$AA@ 10025bfc     Engine.obj
 0002:00005c1c       ??_C@_0CA@BOLHIPJG@WmiQuerySingleInstanceMultipleW?$AA@ 10025c1c     Engine.obj
 0002:00005c3c       ??_C@_0BI@EKFEDEFN@WmiQuerySingleInstanceW?$AA@ 10025c3c     Engine.obj
 0002:00005c54       ??_C@_0BJ@DKFKMGIC@WmiReceiveNotificationsA?$AA@ 10025c54     Engine.obj
 0002:00005c70       ??_C@_0BJ@CGMCHDFF@WmiReceiveNotificationsW?$AA@ 10025c70     Engine.obj
 0002:00005c8c       ??_C@_0BG@OOBGOOCK@WmiSetSingleInstanceA?$AA@ 10025c8c     Engine.obj
 0002:00005ca4       ??_C@_0BG@PCIOFLPN@WmiSetSingleInstanceW?$AA@ 10025ca4     Engine.obj
 0002:00005cbc       ??_C@_0BC@MNFIAKAC@WmiSetSingleItemA?$AA@ 10025cbc     Engine.obj
 0002:00005cd0       ??_C@_0BC@NBMALPNF@WmiSetSingleItemW?$AA@ 10025cd0     Engine.obj
 0002:00005ce4       ??_C@_0BG@MGDPGFPH@WriteEncryptedFileRaw?$AA@ 10025ce4     Engine.obj
 0002:00005cfc       ??_C@_04KOJKLCBG@1000?$AA@ 10025cfc     Engine.obj
 0002:00005d04       ??_C@_03DIFKLMIL@dss?$AA@  10025d04     Engine.obj
 0002:00005d08       ??_C@_02EOPBOLLC@dd?$AA@   10025d08     Engine.obj
 0002:00005d0c       ??_C@_05DDCPCBJI@Honor?$AA@ 10025d0c     Engine.obj
 0002:00005d14       ??_C@_06JDKABGKH@cancel?$AA@ 10025d14     Engine.obj
 0002:00005d1c       ??_C@_05LBOHBHFK@close?$AA@ 10025d1c     Engine.obj
 0002:00005d24       ??_C@_02MFDMBIJM@ok?$AA@   10025d24     Engine.obj
 0002:00005d28       ??_C@_04GEKAJPIK@bbdw?$AA@ 10025d28     Engine.obj
 0002:00005d30       ??_C@_05JBLOAJE@bbdws?$AA@ 10025d30     Engine.obj
 0002:00005d38       ??_C@_0O@IHNNOPML@convers_title?$AA@ 10025d38     Engine.obj
 0002:00005d48       ??_C@_0M@LIHOJBIB@convers_per?$AA@ 10025d48     Engine.obj
 0002:00005d54       ??_C@_0CK@IEKEEFKP@?$CDnStrength?59?$CDnIntelligence?59?$CDnAg@ 10025d54     Engine.obj
 0002:00005d80       ??_C@_02MBMLJIHN@?$CDn?$AA@ 10025d80     Engine.obj
 0002:00005d84       ??_C@_01OJONOECF@b?$AA@    10025d84     Engine.obj
 0002:00005d88       ??_C@_0BK@FIOJEPGC@deathmatchtournamentpopup?$AA@ 10025d88     Engine.obj
 0002:00005da4       ??_C@_0BO@MGNNJAOA@deathmatchtournamentpopup_sub?$AA@ 10025da4     Engine.obj
 0002:00005dc4       ??_C@_0BF@MDKDJNBK@deathmatchtournament?$AA@ 10025dc4     Engine.obj
 0002:00005ddc       ??_C@_04NOJCDH@?$CF02x?$AA@ 10025ddc     Engine.obj
 0002:00005de4       ??_C@_02JDPG@rb?$AA@       10025de4     Engine.obj
 0002:00005f08       ??_C@_00CNPNBAHC@?$AA@     10025f08     Engine.obj
 0002:00005f0c       ??_C@_05FLHLICGF@PKPWD?$AA@ 10025f0c     Engine.obj
 0002:00005f14       ??_C@_01KDCPPGHE@r?$AA@    10025f14     Engine.obj
 0002:00005f18       ??_C@_0P@EEANBPIL@?4?1checksum?4xea?$AA@ 10025f18     Engine.obj
 0002:00005f28       ??_C@_0BN@HNILPNEG@?4?6Please?5update?5your?5client?4?$AA@ 10025f28     Engine.obj
 0002:00005f48       ??_C@_0P@GGKECKNP@Missing?5file?3?5?$AA@ 10025f48     Engine.obj
 0002:00005f58       ??_C@_0BA@EPNAPOMM@TheHyperNetwork?$AA@ 10025f58     Engine.obj
 0002:00005f68       ??_C@_09MBDFIFEN@FileCheck?$AA@ 10025f68     Engine.obj
 0002:00005f74       ??_C@_01LPLHEDKD@d?$AA@    10025f74     Engine.obj
 0002:00005f78       ??_C@_0BD@OLBABOEK@vector?$DMT?$DO?5too?5long?$AA@ 10025f78     Engine.obj
 0002:00005f8c       ??_C@_08EPJLHIJG@bad?5cast?$AA@ 10025f8c     Engine.obj
 0002:00005f9c       ??_7?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@ 10025f9c     Engine.obj
 0002:00005fdc       ??_7?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@ 10025fdc     Engine.obj
 0002:00005fe0       ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@ 10025fe0     Packets.obj
 0002:00005fe0       ??_8?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@ 10025fe0     Engine.obj
 0002:00005fe8       ??_8?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@ 10025fe8     Engine.obj
 0002:00005ff4       ??_7?$basic_filebuf@DU?$char_traits@D@std@@@std@@6B@ 10025ff4     Engine.obj
 0002:00006034       ??_7?$basic_ifstream@DU?$char_traits@D@std@@@std@@6B@ 10026034     Engine.obj
 0002:00006038       ??_8?$basic_ifstream@DU?$char_traits@D@std@@@std@@7B@ 10026038     Engine.obj
 0002:00006044       ??_7Lock@@6B@              10026044     Graphics.obj
 0002:00006048       ??_C@_0BH@HHDGIIFB@0123456789abcdefABCDEF?$AA@ 10026048     Packets.obj
 0002:00006060       ??_C@_03MINKAKBA@c?3?2?$AA@ 10026060     Packets.obj
 0002:00006068       ??_C@_1EA@FECOHKPP@?$AAS?$AAo?$AAf?$AAt?$AAw?$AAa?$AAr?$AAe?$AA?2?$AAM?$AAi?$AAc?$AAr?$AAo?$AAs?$AAo?$AAf?$AAt?$AA?2?$AAC?$AAr?$AAy?$AAp?$AAt?$AAo?$AAg?$AAr?$AAa?$AAp?$AAh?$AAy?$AA?$AA@ 10026068     Packets.obj
 0002:000060a8       ??_C@_1BI@HFOGPJOA@?$AAM?$AAa?$AAc?$AAh?$AAi?$AAn?$AAe?$AAG?$AAu?$AAi?$AAd?$AA?$AA@ 100260a8     Packets.obj
 0002:000060c0       ??_C@_05LBJMNBOG@empty?$AA@ 100260c0     Packets.obj
 0002:000060c8       ??_C@_0M@BHIBOOFG@?2engine?4exe?$AA@ 100260c8     Packets.obj
 0002:000060d4       ??_C@_08DGPNMMIE@dddddddd?$AA@ 100260d4     Packets.obj
 0002:000060e0       ??_C@_0BG@DHOCNFOK@Scenario3_3_timescore?$AA@ 100260e0     Packets.obj
 0002:000060f8       ??_C@_08GAMLEKNH@ddwddddd?$AA@ 100260f8     Packets.obj
 0002:00006104       ??_C@_0DM@FJDFCFHK@Illegal?5Files?5detected?0?5reinstal@ 10026104     Packets.obj
 0002:00006140       ??_C@_04HOEACKCB@dsss?$AA@ 10026140     Packets.obj
 0002:00006148       ??_C@_0L@MKGPNGAP@Engine?4dll?$AA@ 10026148     Packets.obj
 0002:00006154       ??_C@_03CBDDAJHO@dds?$AA@  10026154     Packets.obj
 0002:00006158       ??_C@_0BA@BNAGIBEL@?4?1AutoLogin?4txt?$AA@ 10026158     Packets.obj
 0002:00006168       ??_C@_08GBJFCIEI@Username?$AA@ 10026168     Packets.obj
 0002:00006174       ??_C@_05DHEMANOL@Login?$AA@ 10026174     Packets.obj
 0002:0000617c       ??_C@_08FJIAHGMM@Password?$AA@ 1002617c     Packets.obj
 0002:00006188       ??_C@_07MIPILLGD@?$FLLogin?$FN?$AA@ 10026188     Packets.obj
 0002:00006190       ??_C@_0M@LKLEOICH@Username?5?$DN?5?$AA@ 10026190     Packets.obj
 0002:0000619c       ??_C@_0M@BFGNMPHC@Password?5?$DN?5?$AA@ 1002619c     Packets.obj
 0002:000061a8       ??_C@_03PFJENDKN@sss?$AA@  100261a8     Packets.obj
 0002:000061ac       ??_C@_0CI@IHLCJBOD@You?5are?5not?5allowed?5to?5use?5this?5@ 100261ac     Packets.obj
 0002:000061d4       ??_C@_0CB@NFNGCFHD@99f4fc172a5ace36cf00aa7038d23f2c@ 100261d4     Packets.obj
 0002:000061f8       ??_C@_0N@IMIFPLE@d3dx9_29?4dll?$AA@ 100261f8     Packets.obj
 0002:00006208       ??_C@_0CB@MMJCILJO@3b5f0bf4125688a531fa21c823ea6193@ 10026208     Packets.obj
 0002:0000622c       ??_C@_0M@OOEJBPPK@dbghelp?4dll?$AA@ 1002622c     Packets.obj
 0002:00006238       ??_C@_0CB@KKOMOGFE@f35a584e947a5b401feb0fe01db4a0d7@ 10026238     Packets.obj
 0002:0000625c       ??_C@_09JNHJAMBA@MFC71?4dll?$AA@ 1002625c     Packets.obj
 0002:00006268       ??_C@_0CB@NMBMIPBA@561fa2abb31dfa8fab762145f81667c2@ 10026268     Packets.obj
 0002:0000628c       ??_C@_0M@IHCMBBKA@msvcp71?4dll?$AA@ 1002628c     Packets.obj
 0002:00006298       ??_C@_0CB@KIDMPIHE@86f1895ae8c5e8b17d99ece768a70732@ 10026298     Packets.obj
 0002:000062bc       ??_C@_0M@MFAJBGNN@msvcr71?4dll?$AA@ 100262bc     Packets.obj
 0002:000062c8       ??_C@_0BM@IFGHJAEE@File?5manipulation?5detected?4?$AA@ 100262c8     Packets.obj
 0002:000062e4       ??_C@_04IICEJJDG@wddd?$AA@ 100262e4     Packets.obj
 0002:000062ec       ??_C@_04PJDONDOC@dddb?$AA@ 100262ec     Packets.obj
 0002:000062f4       ??_C@_0BC@GAPJBJMI@wdddwddIIsbdsIIbd?$AA@ 100262f4     Packets.obj
 0002:00006308       ??_C@_03PABHFHDL@ssd?$AA@  10026308     Packets.obj
 0002:0000630c       ??_C@_02BMCGDAIG@bb?$AA@   1002630c     Packets.obj
 0002:00006310       ??_C@_03FFGKOIG@bbd?$AA@   10026310     Packets.obj
 0002:00006314       ??_C@_05OKBANMFO@dsbdb?$AA@ 10026314     Packets.obj
 0002:0000631c       ??_C@_02BIKLEMDE@db?$AA@   1002631c     Packets.obj
 0002:00006320       ??_C@_05KJMBIHHL@Quest?$AA@ 10026320     Packets.obj
 0002:00006328       ??_C@_03BECFFIFL@dId?$AA@  10026328     Packets.obj
 0002:0000632c       ??_C@_02FCBLNKNB@ss?$AA@   1002632c     Packets.obj
 0002:00006330       ??_C@_01LKDEMHDF@s?$AA@    10026330     Packets.obj
 0002:00006334       ??_C@_05EIAFHKEB@dssss?$AA@ 10026334     Packets.obj
 0002:0000633c       ??_C@_02FHJIFOEH@sd?$AA@   1002633c     Packets.obj
 0002:00006340       ??_C@_0BD@JMKKOLB@dddddddddddddddddd?$AA@ 10026340     Packets.obj
 0002:00006354       ??_C@_03CELAINOI@ddd?$AA@  10026354     Packets.obj
 0002:00006358       ??_C@_05ELOBGPJG@ddddd?$AA@ 10026358     Packets.obj
 0002:00006360       ??_C@_04KPGEHEGE@dddd?$AA@ 10026360     Packets.obj
 0002:00006368       ??_C@_0BD@LOOJAFNL@Item_Conversion_Ok?$AA@ 10026368     Packets.obj
 0002:0000637c       ??_C@_06GGFELJO@dsdddd?$AA@ 1002637c     Packets.obj
 0002:00006384       ??_C@_03OJHOOCMO@sdd?$AA@  10026384     Packets.obj
 0002:00006388       ??_C@_0BC@KJNAEEED@Item_Qigong_Rates?$AA@ 10026388     Packets.obj
 0002:0000639c       ??_C@_0P@IGODKPED@screeninfoedit?$AA@ 1002639c     Packets.obj
 0002:000063ac       ??_C@_09OMMCAHLG@hell_time?$AA@ 100263ac     Packets.obj
 0002:000063b8       ??_C@_03FHIBHFLC@bdb?$AA@  100263b8     Packets.obj
 0002:000063bc       ??_C@_03HCOKCKGO@ddb?$AA@  100263bc     Packets.obj
 0002:000063c0       ??_C@_0O@LADJBPKI@mstone_socket?$AA@ 100263c0     Packets.obj
 0002:000063d0       ??_C@_02EKHMJHAA@bd?$AA@   100263d0     Packets.obj
 0002:000063d4       ??_C@_0CO@GKBJFNMC@Please?5Restart?5your?5client?5using@ 100263d4     Packets.obj
 0002:00006404       ??_C@_0BG@HECKJKI@Packet?5Hack?5detected?4?$AA@ 10026404     Packets.obj
 0002:0000641c       ??_C@_0CA@PACOONLK@?4?6Close?5and?5update?5your?5client?4?$AA@ 1002641c     Packets.obj
 0002:0000643c       ??_C@_0BA@KCGGEPND@Outdated?5file?3?5?$AA@ 1002643c     Packets.obj
 0002:0000644c       ??_C@_0BP@EKANKKPA@?4?6Close?5and?5re?9extract?5Client?4?$AA@ 1002644c     Packets.obj
 0002:0000646c       ??_C@_0CA@IEEICBOG@You?5have?5been?5Hardware?5blocked?4?$AA@ 1002646c     Packets.obj
 0002:00006490       ??_C@_0EP@MIKGOEFI@Outdated?5files?5detected?0?5Please?5@ 10026490     Packets.obj
 0002:000064e0       ??_C@_04PMOCAHAA@open?$AA@ 100264e0     Packets.obj
 0002:000064e8       ??_C@_0BD@MDIBHING@TrainingTimeWindow?$AA@ 100264e8     Packets.obj
 0002:000064fc       ??_C@_0CA@PNFFFIID@?$DN?$DN?$DN?$DN?$DN?$DN?$DN?$DNIgnored?5Players?$DN?$DN?$DN?$DN?$DN?$DN?$DN?$DN?$AA@ 100264fc     Packets.obj
 0002:0000651c       ??_C@_0CF@GLEJPLFC@?5Your?5ignored?5players?5list?5is?5em@ 1002651c     Packets.obj
 0002:00006544       ??_C@_0BH@DDLGJJNF@rental_extend_question?$AA@ 10026544     Packets.obj
 0002:0000655c       ??_C@_0N@CIIJAEPH@auction_main?$AA@ 1002655c     Packets.obj
 0002:0000656c       ??_C@_07OCGPFBLI@dwddddd?$AA@ 1002656c     Packets.obj
 0002:00006574       ??_C@_0BL@BLMIPCCE@?$FN?5second?$CIs?$CJ?5to?5continue?4?4?4?$AA@ 10026574     Packets.obj
 0002:00006590       ??_C@_0L@OBDAOGBA@?$FN?5within?5?$FL?$AA@ 10026590     Packets.obj
 0002:0000659c       ??_C@_0BE@IDGAGMIJ@?$FLAFK?5Check?$FN?5Press?5?$FL?$AA@ 1002659c     Packets.obj
 0002:000065b0       ??_C@_06BMDLAFAP@ddddbd?$AA@ 100265b0     Packets.obj
 0002:000065b8       ??_C@_0P@EKEBEHFI@back_decorate_?$AA@ 100265b8     Packets.obj
 0002:000065c8       ??_C@_02FNAOPJMI@s?4?$AA@  100265c8     Packets.obj
 0002:000065cc       ??_C@_01LFCBOECM@?4?$AA@   100265cc     Packets.obj
 0002:000065d0       ??_C@_0O@KLLJPMFP@You?5have?5lost?$AA@ 100265d0     Packets.obj
 0002:000065e0       ??_C@_02ELHCGPCE@ds?$AA@   100265e0     Packets.obj
 0002:000065e4       ??_C@_09FGEMLJPD@?$FLNotice?$FN?5?$AA@ 100265e4     Packets.obj
 0002:000065f4       ??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@ 100265f4     Packets.obj
 0002:000065f8       ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@ 100265f8     Packets.obj
 0002:00006600       ??_C@_09LJAJGODI@?2?9?1?3?$DP?$CC?$DM?$DO?$HM?$AA@ 10026600     Protect.obj
 0002:0000660c       ??_C@_06LNHGJBBP@CheckX?$AA@ 1002660c     Protect.obj
 0002:00006614       ??_C@_09NGAAFJKF@sbbbbbbbb?$AA@ 10026614     Protect.obj
 0002:00006624       ??_7IProtect@@6B@          10026624     Protect.obj
 0002:00006628       ??_C@_02HHAEMJFE@?2?1?$AA@ 10026628     Tools.obj
 0002:00006634       ??_7ITools@@6B@            10026634     Tools.obj
 0002:00006680       ??_C@_0O@NMJGKHLN@EV?$CJO8?$EABL$3O2E?$AA@ 10026680     Variables.obj
 0002:00006690       __real@453b8000            10026690      CIL library: CIL module
 0002:00006694       __real@43960000            10026694      CIL library: CIL module
 0002:00006698       __load_config_used         10026698     MSVCRT:loadcfg.obj
 0002:0000671c       ??_R4type_info@@6B@        1002671c     MSVCRT:ti_inst.obj
 0002:00006730       ??_R3type_info@@8          10026730     MSVCRT:ti_inst.obj
 0002:00006740       ??_R2type_info@@8          10026740     MSVCRT:ti_inst.obj
 0002:00006748       ??_R1A@?0A@EA@type_info@@8 10026748     MSVCRT:ti_inst.obj
 0002:00006764       ??_R4bad_alloc@std@@6B@    10026764     base64.obj
 0002:00006778       ??_R3bad_alloc@std@@8      10026778     base64.obj
 0002:00006788       ??_R2bad_alloc@std@@8      10026788     base64.obj
 0002:00006794       ??_R1A@?0A@EA@exception@std@@8 10026794     base64.obj
 0002:000067b0       ??_R3exception@std@@8      100267b0     base64.obj
 0002:000067c0       ??_R2exception@std@@8      100267c0     base64.obj
 0002:000067c8       ??_R1A@?0A@EA@bad_alloc@std@@8 100267c8     base64.obj
 0002:000067e4       ??_R4?$basic_ifstream@DU?$char_traits@D@std@@@std@@6B@ 100267e4     Engine.obj
 0002:000067f8       ??_R3?$basic_ifstream@DU?$char_traits@D@std@@@std@@8 100267f8     Engine.obj
 0002:00006808       ??_R2?$basic_ifstream@DU?$char_traits@D@std@@@std@@8 10026808     Engine.obj
 0002:00006820       ??_R1A@?0A@EA@?$basic_ifstream@DU?$char_traits@D@std@@@std@@8 10026820     Engine.obj
 0002:0000683c       ??_R4?$basic_filebuf@DU?$char_traits@D@std@@@std@@6B@ 1002683c     Engine.obj
 0002:00006850       ??_R3?$basic_filebuf@DU?$char_traits@D@std@@@std@@8 10026850     Engine.obj
 0002:00006860       ??_R2?$basic_filebuf@DU?$char_traits@D@std@@@std@@8 10026860     Engine.obj
 0002:0000686c       ??_R1A@?0A@EA@?$basic_filebuf@DU?$char_traits@D@std@@@std@@8 1002686c     Engine.obj
 0002:00006888       ??_R4?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@ 10026888     Engine.obj
 0002:0000689c       ??_R3?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 1002689c     Engine.obj
 0002:000068ac       ??_R2?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 100268ac     Engine.obj
 0002:000068d8       ??_R1A@?0A@EA@?$basic_iostream@DU?$char_traits@D@std@@@std@@8 100268d8     Engine.obj
 0002:000068f4       ??_R3?$basic_iostream@DU?$char_traits@D@std@@@std@@8 100268f4     Engine.obj
 0002:00006904       ??_R2?$basic_iostream@DU?$char_traits@D@std@@@std@@8 10026904     Engine.obj
 0002:0000692c       ??_R1BA@?0A@EA@?$basic_ostream@DU?$char_traits@D@std@@@std@@8 1002692c     Engine.obj
 0002:00006948       ??_R3?$basic_ostream@DU?$char_traits@D@std@@@std@@8 10026948     Engine.obj
 0002:00006958       ??_R2?$basic_ostream@DU?$char_traits@D@std@@@std@@8 10026958     Engine.obj
 0002:0000696c       ??_R1A@?0A@EA@?$basic_ostream@DU?$char_traits@D@std@@@std@@8 1002696c     Engine.obj
 0002:00006988       ??_R1A@?0A@EA@?$basic_istream@DU?$char_traits@D@std@@@std@@8 10026988     Engine.obj
 0002:000069a4       ??_R3?$basic_istream@DU?$char_traits@D@std@@@std@@8 100269a4     Engine.obj
 0002:000069b4       ??_R2?$basic_istream@DU?$char_traits@D@std@@@std@@8 100269b4     Engine.obj
 0002:000069c8       ??_R17A@3EA@?$_Iosb@H@std@@8 100269c8     Engine.obj
 0002:000069e4       ??_R1A@A@3EA@ios_base@std@@8 100269e4     Engine.obj
 0002:00006a00       ??_R1A@A@3FA@?$basic_ios@DU?$char_traits@D@std@@@std@@8 10026a00     Engine.obj
 0002:00006a1c       ??_R3?$basic_ios@DU?$char_traits@D@std@@@std@@8 10026a1c     Engine.obj
 0002:00006a2c       ??_R2?$basic_ios@DU?$char_traits@D@std@@@std@@8 10026a2c     Engine.obj
 0002:00006a3c       ??_R1A@?0A@EA@ios_base@std@@8 10026a3c     Engine.obj
 0002:00006a58       ??_R3ios_base@std@@8       10026a58     Engine.obj
 0002:00006a68       ??_R2ios_base@std@@8       10026a68     Engine.obj
 0002:00006a74       ??_R17?0A@EA@?$_Iosb@H@std@@8 10026a74     Engine.obj
 0002:00006a90       ??_R3?$_Iosb@H@std@@8      10026a90     Engine.obj
 0002:00006aa0       ??_R2?$_Iosb@H@std@@8      10026aa0     Engine.obj
 0002:00006aa8       ??_R1A@?0A@EA@?$_Iosb@H@std@@8 10026aa8     Engine.obj
 0002:00006ac4       ??_R1A@?0A@EA@?$basic_ios@DU?$char_traits@D@std@@@std@@8 10026ac4     Engine.obj
 0002:00006ae0       ??_R1A@?0A@EA@?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 10026ae0     Engine.obj
 0002:00006afc       ??_R4?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@ 10026afc     Engine.obj
 0002:00006b10       ??_R3?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 10026b10     Engine.obj
 0002:00006b20       ??_R2?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 10026b20     Engine.obj
 0002:00006b2c       ??_R1A@?0A@EA@?$basic_streambuf@DU?$char_traits@D@std@@@std@@8 10026b2c     Engine.obj
 0002:00006b48       ??_R3?$basic_streambuf@DU?$char_traits@D@std@@@std@@8 10026b48     Engine.obj
 0002:00006b58       ??_R2?$basic_streambuf@DU?$char_traits@D@std@@@std@@8 10026b58     Engine.obj
 0002:00006b60       ??_R1A@?0A@EA@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@8 10026b60     Engine.obj
 0002:00006b7c       ??_R4Lock@@6B@             10026b7c     Graphics.obj
 0002:00006b90       ??_R3Lock@@8               10026b90     Graphics.obj
 0002:00006ba0       ??_R2Lock@@8               10026ba0     Graphics.obj
 0002:00006ba8       ??_R1A@?0A@EA@Lock@@8      10026ba8     Graphics.obj
 0002:00006bc4       ??_R4?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@ 10026bc4     Packets.obj
 0002:00006bd8       ??_R3?$basic_fstream@DU?$char_traits@D@std@@@std@@8 10026bd8     Packets.obj
 0002:00006be8       ??_R2?$basic_fstream@DU?$char_traits@D@std@@@std@@8 10026be8     Packets.obj
 0002:00006c14       ??_R1A@?0A@EA@?$basic_fstream@DU?$char_traits@D@std@@@std@@8 10026c14     Packets.obj
 0002:00006c30       ??_R4IProtect@@6B@         10026c30     Protect.obj
 0002:00006c44       ??_R3IProtect@@8           10026c44     Protect.obj
 0002:00006c54       ??_R2IProtect@@8           10026c54     Protect.obj
 0002:00006c5c       ??_R1A@?0A@EA@IProtect@@8  10026c5c     Protect.obj
 0002:00006c78       ??_R4ITools@@6B@           10026c78     Tools.obj
 0002:00006c8c       ??_R3ITools@@8             10026c8c     Tools.obj
 0002:00006c9c       ??_R2ITools@@8             10026c9c     Tools.obj
 0002:00006ca4       ??_R1A@?0A@EA@ITools@@8    10026ca4     Tools.obj
 0002:00006cc0       ___safe_se_handler_table   10026cc0     <linker-defined>
 0002:00006dd4       ___rtc_iaa                 10026dd4     MSVCRT:_initsect_.obj
 0002:00006dd8       ___rtc_izz                 10026dd8     MSVCRT:_initsect_.obj
 0002:00006ddc       ___rtc_taa                 10026ddc     MSVCRT:_initsect_.obj
 0002:00006de0       ___rtc_tzz                 10026de0     MSVCRT:_initsect_.obj
 0002:00006edc       __CT??_R0?AVexception@std@@@8??0exception@std@@QAE@ABV01@@Z12 10026edc     base64.obj
 0002:00006ef8       __CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QAE@ABV01@@Z12 10026ef8     base64.obj
 0002:00006f14       __CTA2?AVbad_alloc@std@@   10026f14     base64.obj
 0002:00006f20       __TI2?AVbad_alloc@std@@    10026f20     base64.obj
 0002:00006f30       __CT??_R0?AVbad_cast@std@@@8??0bad_cast@std@@QAE@ABV01@@Z12 10026f30     Engine.obj
 0002:00006f4c       __CTA2?AVbad_cast@std@@    10026f4c     Engine.obj
 0002:00006f58       __TI2?AVbad_cast@std@@     10026f58     Engine.obj
 0002:00008234       __IMPORT_DESCRIPTOR_KERNEL32 10028234     kernel32:KERNEL32.dll
 0002:00008248       __IMPORT_DESCRIPTOR_USER32 10028248     user32:USER32.dll
 0002:0000825c       __IMPORT_DESCRIPTOR_ADVAPI32 1002825c     advapi32:ADVAPI32.dll
 0002:00008270       __IMPORT_DESCRIPTOR_SHELL32 10028270     shell32:SHELL32.dll
 0002:00008284       __IMPORT_DESCRIPTOR_MSVCP100 10028284     msvcprt:MSVCP100.dll
 0002:00008298       __IMPORT_DESCRIPTOR_MSVCR100 10028298     MSVCRT:MSVCR100.dll
 0002:000082ac       __NULL_IMPORT_DESCRIPTOR   100282ac     kernel32:KERNEL32.dll
 0003:00000000       ??_R0?AVtype_info@@@8      10030000     MSVCRT:ti_inst.obj
 0003:00000018       ___security_cookie         10030018     MSVCRT:gs_cookie.obj
 0003:0000001c       ___security_cookie_complement 1003001c     MSVCRT:gs_cookie.obj
 0003:00000020       ___native_dllmain_reason   10030020     MSVCRT:natstart.obj
 0003:00000024       ___native_vcclrit_reason   10030024     MSVCRT:natstart.obj
 0003:00000028       ??_R0?AVexception@std@@@8  10030028     base64.obj
 0003:00000044       ??_R0?AVbad_alloc@std@@@8  10030044     base64.obj
 0003:000001e4       ?PCName@@3PBDB             100301e4     Packets.obj
 0003:00000228       ?AsadalValue@@3HA          10030228     Packets.obj
 0003:0000022c       ?packCheck@@3PAHA          1003022c     Packets.obj
 0003:00000250       ?Patches@IOnSend@@0PAKA    10030250     OnSend.obj
 0003:0000161c       ?ID@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A 1003161c     Packets.obj
 0003:00001638       ?PWD@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A 10031638     Packets.obj
 0003:00001654       ?fxLock@@3VLock@@A         10031654     Packets.obj
 0003:00001690       ?setCheck@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A 10031690     Variables.obj
 0003:000016ac       ?mac@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A 100316ac     Variables.obj
 0003:000016c8       ?buffLock@@3VLock@@A       100316c8     Variables.obj
 0003:000016e8       ?PacketLock@@3VLock@@A     100316e8     Variables.obj
 0003:00001708       ??_R0?AVITools@@@8         10031708     Buff.obj
 0003:00001720       ??_R0?AVIChatbox@@@8       10031720     Chatbox.obj
 0003:00001738       ??_R0?AVIGraphics@@@8      10031738     Chatbox.obj
 0003:00001750       ??_R0?AV?$basic_ifstream@DU?$char_traits@D@std@@@std@@@8 10031750     Engine.obj
 0003:0000178c       ??_R0?AV?$basic_filebuf@DU?$char_traits@D@std@@@std@@@8 1003178c     Engine.obj
 0003:000017c8       ??_R0?AV?$basic_ostream@DU?$char_traits@D@std@@@std@@@8 100317c8     Engine.obj
 0003:00001804       ??_R0?AV?$_Iosb@H@std@@@8  10031804     Engine.obj
 0003:00001820       ??_R0?AVios_base@std@@@8   10031820     Engine.obj
 0003:0000183c       ??_R0?AV?$basic_ios@DU?$char_traits@D@std@@@std@@@8 1003183c     Engine.obj
 0003:00001874       ??_R0?AV?$basic_istream@DU?$char_traits@D@std@@@std@@@8 10031874     Engine.obj
 0003:000018b0       ??_R0?AV?$basic_iostream@DU?$char_traits@D@std@@@std@@@8 100318b0     Engine.obj
 0003:000018f0       ??_R0?AV?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@8 100318f0     Engine.obj
 0003:00001940       ??_R0?AV?$basic_streambuf@DU?$char_traits@D@std@@@std@@@8 10031940     Engine.obj
 0003:00001980       ??_R0?AV?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@8 10031980     Engine.obj
 0003:000019d0       ??_R0?AVIPackets@@@8       100319d0     Engine.obj
 0003:000019e8       ??_R0?AVIProtect@@@8       100319e8     Engine.obj
 0003:00001a00       ??_R0?AVIBuff@@@8          10031a00     Engine.obj
 0003:00001a14       ??_R0?AVbad_cast@std@@@8   10031a14     Engine.obj
 0003:00001a30       ??_R0?AVLock@@@8           10031a30     Graphics.obj
 0003:00001a44       ??_R0?AVIOnSend@@@8        10031a44     OnSend.obj
 0003:00001a5c       ??_R0?AV?$basic_fstream@DU?$char_traits@D@std@@@std@@@8 10031a5c     Packets.obj
 0003:00001aa8       ?__type_info_root_node@@3U__type_info_node@@A 10031aa8     MSVCRT:tncleanup.obj
 0003:00001e00       ?_Stinit@?1??_Init@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IAEXPAU_iobuf@@W4_Initfl@23@@Z@4HA 10031e00     Engine.obj
 0003:00001e08       ?_pInstance@InterfaceManager@@0PAV1@A 10031e08     Interface.obj
 0003:00001e0c       ?username@@3PADA           10031e0c     Packets.obj
 0003:00001e20       ?password@@3PADA           10031e20     Packets.obj
 0003:00001e2f       ?Expanded@@3_NA            10031e2f     Packets.obj
 0003:00001e30       ?hL@@3PAUHINSTANCE__@@A    10031e30     Engine.obj
 0003:00001e34       ?CheckArmorGrade@@3HA      10031e34     Engine.obj
 0003:00001e38       ?p@@3PAP6GHXZA             10031e38     Engine.obj
 0003:00002ad0       ?Delay@@3JA                10032ad0     Engine.obj
 0003:00002ad4       ?StoreValue@@3HA           10032ad4     Engine.obj
 0003:00002ad8       ?count@@3HA                10032ad8     Engine.obj
 0003:00002adc       ?FaceChange@@3HA           10032adc     Packets.obj
 0003:00002ae0       ?Speed@@3HA                10032ae0     Packets.obj
 0003:00002ae4       ?voteTime@@3HA             10032ae4     Packets.obj
 0003:00002ae8       ?FFSize@@3DA               10032ae8     Packets.obj
 0003:00002ae9       ?StopClient@@3_NA          10032ae9     Variables.obj
 0003:00002aec       ?FPEnable@@3HA             10032aec     Packets.obj
 0003:00002af0       ?Check@@3HA                10032af0     Packets.obj
 0003:00002af4       ?AutoPick@@3HA             10032af4     Packets.obj
 0003:00002af8       ?DamageCheck@@3HA          10032af8     Packets.obj
 0003:00002afc       ?GeonCheck@@3HA            10032afc     Packets.obj
 0003:00002b00       ?Absorb@@3HA               10032b00     Packets.obj
 0003:00002b04       ?Channel@@3HA              10032b04     Packets.obj
 0003:00002b08       ?KeyD@@3HA                 10032b08     Packets.obj
 0003:00002b0c       ?GuildCheck@@3HA           10032b0c     Packets.obj
 0003:00002b10       ?SentKey@@3HA              10032b10     Packets.obj
 0003:00002b14       ?PressableKey@@3HA         10032b14     Variables.obj
 0003:00002b18       ?numLock@@3HA              10032b18     Variables.obj
 0003:00002b1c       ?UniqueKey@@3HA            10032b1c     Variables.obj
 0003:00002b20       ?CryptKey@@3HA             10032b20     Variables.obj
 0003:00002b24       ?HonorType@@3HA            10032b24     Variables.obj
 0003:00002b28       ?MyHonor@@3HA              10032b28     Variables.obj
 0003:00002b2c       ?VButton@@3HA              10032b2c     Variables.obj
 0003:00002b30       ?DelayCheck@@3JA           10032b30     Variables.obj
 0003:00002b34       ?CurHash@@3JA              10032b34     Variables.obj
 0003:00002b38       ?CurPos@@3JA               10032b38     Variables.obj
 0003:00002b3c       ?MD5Time@@3JA              10032b3c     Variables.obj
 0003:00002b40       ?hL2@@3PAUHINSTANCE__@@A   10032b40     Variables.obj
 0003:00002b44       ?latestPacket@@3HA         10032b44     Variables.obj
 0003:00002b48       ?MD5TimeToSleep@@3JA       10032b48     Variables.obj
 0003:00002b4c       ?__i_Init_IChatbox@@3V__Init_IChatbox@@A 10032b4c     Chatbox.obj
 0003:00002b4d       ?__i_Init_IGraphics@@3V__Init_IGraphics@@A 10032b4d     Graphics.obj
 0003:00002b4e       ?__i_Init_IOnSend@@3V__Init_IOnSend@@A 10032b4e     OnSend.obj
 0003:00002b4f       ?__i_Init_IPackets@@3V__Init_IPackets@@A 10032b4f     Packets.obj
 0003:00002b50       ?SummonedNPCs@@3V?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@A 10032b50     Packets.obj
 0003:00002b60       ?IgnorePlayers@@3V?$set@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@A 10032b60     Packets.obj
 0003:00002b70       ?PlayerNames@@3V?$map@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@@std@@A 10032b70     Packets.obj
 0003:00002b80       ?Fxs@@3V?$map@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@@std@@A 10032b80     Packets.obj
 0003:00002b90       ?Tick@@3HA                 10032b90     Packets.obj
 0003:00002b94       ?NKey@@3HA                 10032b94     Packets.obj
 0003:00002b98       ?SeenPlayers@@3V?$set@HU?$less@H@std@@V?$allocator@H@2@@std@@A 10032b98     Packets.obj
 0003:00002ba8       ?__i_Init_IProtect@@3V__Init_IProtect@@A 10032ba8     Protect.obj
 0003:00002ba9       ?__i_Init_ITools@@3V__Init_ITools@@A 10032ba9     Tools.obj
 0003:00002bac       ?BuffIcons@@3V?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@A 10032bac     Variables.obj
 0003:00002bbc       ?MD5Files@@3V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@A 10032bbc     Variables.obj
 0003:00002bcc       ?_Psave@?$_Facetptr@V?$codecvt@DDH@std@@@std@@2PBVfacet@locale@2@B 10032bcc     Engine.obj
 0003:00002fd0       ___native_startup_state    10032fd0     <common>
 0003:00002fd4       ___native_startup_lock     10032fd4     <common>
 0003:00002fd8       ___dyn_tls_init_callback   10032fd8     <common>
 0003:00002fdc       ___onexitend               10032fdc     <common>
 0003:00002fe0       ___onexitbegin             10032fe0     <common>

 entry point at        0001:0001a4c8

 Static symbols

 0001:00000923       __catch$?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXII@Z$0 10001923 f i  CIL library: CIL module
 0001:000009a7       __catch$?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXII@Z$1 100019a7 f i  CIL library: CIL module
 0001:0000230a       __catch$?_Buynode@?$_List_val@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEPAU_Node@?$_List_nod@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@PAU342@0ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z$0 1000330a f i  CIL library: CIL module
 0001:0000a620       ?init_keys@@YAXPBKPBDPAK@Z 1000b620 f    CIL library: CIL module
 0001:0000d372       __catch$?reserve@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXI@Z$0 1000e372 f i  CIL library: CIL module
 0001:00013343       __catch$??0?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAE@ABV01@@Z$0 10014343 f i  CIL library: CIL module
 0001:00013864       __catch$??0?$vector@HV?$allocator@H@std@@@std@@QAE@ABV01@@Z$0 10014864 f i  CIL library: CIL module
 0001:00013e04       __catch$??0?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE@ABV01@@Z$0 10014e04 f i  CIL library: CIL module
 0001:00014662       __catch$?reserve@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAEXI@Z$0 10015662 f i  CIL library: CIL module
 0001:0001538a       __catch$?_Copy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEXII@Z$0 1001638a f i  CIL library: CIL module
 0001:00015412       __catch$?_Copy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEXII@Z$1 10016412 f i  CIL library: CIL module
 0001:0001574d       __catch$?_Copy@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@PAU342@0@Z$0 1001674d f i  CIL library: CIL module
 0001:00015f54       __catch$??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z$0 10016f54 f i  CIL library: CIL module
 0001:000160cd       __catch$??$_Buynode@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z$0 100170cd f i  CIL library: CIL module
 0001:0001747e       __catch$??$_Construct@V?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXV?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@0Uforward_iterator_tag@1@@Z$0 1001847e f i  CIL library: CIL module
 0001:000176ac       __catch$??$_Buynode@U?$pair@HPAD@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HPAD@1@@Z$0 100186ac f i  CIL library: CIL module
 0001:0001778c       __catch$??$_Buynode@U?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z$0 1001878c f i  CIL library: CIL module
 0001:000178e7       __catch$??$_Buynode@AAPBD@?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@1@AAPBD@Z$0 100188e7 f i  CIL library: CIL module
 0001:00019bda       __catch$??0?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAE@ABV01@@Z$0 1001abda f i  CIL library: CIL module
 0001:00019e5c       __catch$?reserve@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAEXI@Z$0 1001ae5c f i  CIL library: CIL module
 0001:0001a15a       _pre_c_init                1001b15a f   MSVCRT:crtdll.obj
 0001:0001a3b2       ___DllMainCRTStartup       1001b3b2 f   MSVCRT:crtdll.obj
 0001:0001ab50       ?detour_is_imported@@YA_NPAE0@Z 1001bb50 f   detours:detours.obj
 0001:0001ace0       ?detour_writable_trampoline_regions@@YAXXZ 1001bce0 f   detours:detours.obj
 0001:0001ae30       ?detour_runnable_trampoline_regions@@YAXXZ 1001be30 f   detours:detours.obj
 0001:0001ae90       ?detour_free_trampoline@@YAXPAU_DETOUR_TRAMPOLINE@@@Z 1001be90 f   detours:detours.obj
 0001:0001b320       ?detour_free_unused_trampoline_regions@@YAXXZ 1001c320 f   detours:detours.obj
 0001:0001b390       ?detour_is_region_empty@@YAHPAUDETOUR_REGION@@@Z 1001c390 f   detours:detours.obj
 0001:0001b420       ?detour_align_from_trampoline@@YAEPAU_DETOUR_TRAMPOLINE@@E@Z 1001c420 f   detours:detours.obj
 0001:0001b470       ?detour_align_from_target@@YAJPAU_DETOUR_TRAMPOLINE@@J@Z 1001c470 f   detours:detours.obj
 0001:0001ba00       ?detour_alloc_trampoline@@YAPAU_DETOUR_TRAMPOLINE@@PAE@Z 1001ca00 f   detours:detours.obj
 0001:0001bce0       ?detour_alloc_region_from_lo@@YAPAXPAE0@Z 1001cce0 f   detours:detours.obj
 0001:0001bdb0       ?detour_alloc_round_up_to_region@@YAPAEPAE@Z 1001cdb0 f   detours:detours.obj
 0001:0001bde0       ?detour_alloc_region_from_hi@@YAPAXPAE0@Z 1001cde0 f   detours:detours.obj
 0001:0001beb0       ?detour_alloc_round_down_to_region@@YAPAEPAE@Z 1001ceb0 f   detours:detours.obj
 0001:0001c9a0       __unwindfunclet$??1_Fac_tidy_reg_t@std@@QAE@XZ$0 1001d9a0 f   msvcprt:locale0_implib.obj
 0001:0001c9a9       __ehhandler$??1_Fac_tidy_reg_t@std@@QAE@XZ 1001d9a9 f   msvcprt:locale0_implib.obj
 0001:0001c9d0       __unwindfunclet$??1sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAE@XZ$0 1001d9d0 f    CIL library: CIL module
 0001:0001c9d8       __ehhandler$??1sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QAE@XZ 1001d9d8 f    CIL library: CIL module
 0001:0001ca00       __ehhandler$?reserve@?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAEXI@Z 1001da00 f    CIL library: CIL module
 0001:0001ca20       __unwindfunclet$??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z$3 1001da20 f    CIL library: CIL module
 0001:0001ca28       __unwindfunclet$??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z$2 1001da28 f    CIL library: CIL module
 0001:0001ca32       __unwindfunclet$??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z$4 1001da32 f    CIL library: CIL module
 0001:0001ca3a       __ehhandler$??$?6U?$char_traits@D@std@@@std@@YAAAV?$basic_ostream@DU?$char_traits@D@std@@@0@AAV10@PBD@Z 1001da3a f    CIL library: CIL module
 0001:0001ca60       __ehhandler$?reserve@?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAEXI@Z 1001da60 f    CIL library: CIL module
 0001:0001ca80       __unwindfunclet$??$use_facet@V?$codecvt@DDH@std@@@std@@YAABV?$codecvt@DDH@0@ABVlocale@0@@Z$0 1001da80 f    CIL library: CIL module
 0001:0001ca89       __ehhandler$??$use_facet@V?$codecvt@DDH@std@@@std@@YAABV?$codecvt@DDH@0@ABVlocale@0@@Z 1001da89 f    CIL library: CIL module
 0001:0001cab0       __unwindfunclet$??_D?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXXZ$0 1001dab0 f    CIL library: CIL module
 0001:0001cabc       __ehhandler$??_D?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXXZ 1001dabc f    CIL library: CIL module
 0001:0001cae0       __ehhandler$??0?$vector@UBuffConfig@@V?$allocator@UBuffConfig@@@std@@@std@@QAE@ABV01@@Z 1001dae0 f    CIL library: CIL module
 0001:0001cb00       __unwindfunclet$?buffTimer@@YAXXZ$0 1001db00 f    CIL library: CIL module
 0001:0001cb08       __ehhandler$?buffTimer@@YAXXZ 1001db08 f    CIL library: CIL module
 0001:0001cb30       __unwindfunclet$?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PB_WHH@Z$0 1001db30 f    CIL library: CIL module
 0001:0001cb30       __unwindfunclet$?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PBDHH@Z$0 1001db30 f    CIL library: CIL module
 0001:0001cb38       __ehhandler$?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PB_WHH@Z 1001db38 f    CIL library: CIL module
 0001:0001cb38       __ehhandler$?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PBDHH@Z 1001db38 f    CIL library: CIL module
 0001:0001cb60       __ehhandler$??0?$vector@HV?$allocator@H@std@@@std@@QAE@ABV01@@Z 1001db60 f    CIL library: CIL module
 0001:0001cb80       __ehhandler$??0?$vector@UConfigNPC@@V?$allocator@UConfigNPC@@@std@@@std@@QAE@ABV01@@Z 1001db80 f    CIL library: CIL module
 0001:0001cba0       __unwindfunclet$??_G?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z$0 1001dba0 f    CIL library: CIL module
 0001:0001cbac       __ehhandler$??_G?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAEPAXI@Z 1001dbac f    CIL library: CIL module
 0001:0001cbd0       __unwindfunclet$??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z$0 1001dbd0 f    CIL library: CIL module
 0001:0001cbed       __unwindfunclet$??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z$1 1001dbed f    CIL library: CIL module
 0001:0001cbf9       __unwindfunclet$??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z$3 1001dbf9 f    CIL library: CIL module
 0001:0001cc02       __ehhandler$??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z 1001dc02 f    CIL library: CIL module
 0001:0001cc20       __unwindfunclet$??0InterfaceManager@@QAE@XZ$0 1001dc20 f    CIL library: CIL module
 0001:0001cc2a       __ehhandler$??0InterfaceManager@@QAE@XZ 1001dc2a f    CIL library: CIL module
 0001:0001cc50       __ehhandler$?_Copy@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXII@Z 1001dc50 f    CIL library: CIL module
 0001:0001cc70       __ehhandler$?_Copy@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEXII@Z 1001dc70 f    CIL library: CIL module
 0001:0001cc90       __unwindfunclet$?overflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHH@Z$0 1001dc90 f    CIL library: CIL module
 0001:0001cc90       __unwindfunclet$?uflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHXZ$0 1001dc90 f    CIL library: CIL module
 0001:0001cc98       __ehhandler$?overflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHH@Z 1001dc98 f    CIL library: CIL module
 0001:0001cc98       __ehhandler$?uflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MAEHXZ 1001dc98 f    CIL library: CIL module
 0001:0001ccc0       __unwindfunclet$?_GetInstance@InterfaceManager@@SAPAV1@XZ$0 1001dcc0 f    CIL library: CIL module
 0001:0001cccc       __ehhandler$?_GetInstance@InterfaceManager@@SAPAV1@XZ 1001dccc f    CIL library: CIL module
 0001:0001ccf0       __unwindfunclet$?base64_decode@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@@Z$0 1001dcf0 f    CIL library: CIL module
 0001:0001cd09       __ehhandler$?base64_decode@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@@Z 1001dd09 f    CIL library: CIL module
 0001:0001cd30       __unwindfunclet$??$_Buynode@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@1@@Z$2 1001dd30 f    CIL library: CIL module
 0001:0001cd35       __ehhandler$??$_Buynode@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@1@@Z 1001dd35 f    CIL library: CIL module
 0001:0001cd50       __unwindfunclet$??$_Buynode@AAPBD@?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@1@AAPBD@Z$2 1001dd50 f    CIL library: CIL module
 0001:0001cd55       __ehhandler$??$_Buynode@AAPBD@?$_Tree_val@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@1@AAPBD@Z 1001dd55 f    CIL library: CIL module
 0001:0001cd70       __unwindfunclet$??$_Buynode@U?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z$2 1001dd70 f    CIL library: CIL module
 0001:0001cd75       __ehhandler$??$_Buynode@U?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z 1001dd75 f    CIL library: CIL module
 0001:0001cd90       __unwindfunclet$??$_Buynode@U?$pair@HPAD@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HPAD@1@@Z$2 1001dd90 f    CIL library: CIL module
 0001:0001cd95       __ehhandler$??$_Buynode@U?$pair@HPAD@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@$$QAU?$pair@HPAD@1@@Z 1001dd95 f    CIL library: CIL module
 0001:0001cdb0       __ehhandler$??$_Construct@V?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXV?$_String_iterator@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@0Uforward_iterator_tag@1@@Z 1001ddb0 f    CIL library: CIL module
 0001:0001cdd0       __unwindfunclet$??A?$map@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@@std@@QAEAAV?$vector@HV?$allocator@H@std@@@1@ABH@Z$0 1001ddd0 f    CIL library: CIL module
 0001:0001cdd8       __unwindfunclet$??A?$map@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@@std@@QAEAAV?$vector@HV?$allocator@H@std@@@1@ABH@Z$1 1001ddd8 f    CIL library: CIL module
 0001:0001cde0       __ehhandler$??A?$map@HV?$vector@HV?$allocator@H@std@@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$vector@HV?$allocator@H@std@@@std@@@std@@@2@@std@@QAEAAV?$vector@HV?$allocator@H@std@@@1@ABH@Z 1001dde0 f    CIL library: CIL module
 0001:0001ce00       __unwindfunclet$??1?$map@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@@std@@QAE@XZ$1 1001de00 f    CIL library: CIL module
 0001:0001ce00       __unwindfunclet$??1?$map@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@@std@@QAE@XZ$1 1001de00 f    CIL library: CIL module
 0001:0001ce08       __ehhandler$??1?$map@IPAXU?$less@I@std@@V?$allocator@U?$pair@$$CBIPAX@std@@@2@@std@@QAE@XZ 1001de08 f    CIL library: CIL module
 0001:0001ce08       __ehhandler$??1?$map@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@@std@@QAE@XZ 1001de08 f    CIL library: CIL module
 0001:0001ce30       __unwindfunclet$??$_Uninit_move@PAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V12@@std@@YAPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAV10@00AAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@0U_Nonscalar_ptr_iterator_tag@0@@Z$2 1001de30 f    CIL library: CIL module
 0001:0001ce35       __ehhandler$??$_Uninit_move@PAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V12@@std@@YAPAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PAV10@00AAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@0U_Nonscalar_ptr_iterator_tag@0@@Z 1001de35 f    CIL library: CIL module
 0001:0001ce50       __unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBDABV10@@Z$0 1001de50 f    CIL library: CIL module
 0001:0001ce69       __ehhandler$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PBDABV10@@Z 1001de69 f    CIL library: CIL module
 0001:0001ce90       __ehhandler$?reserve@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXI@Z 1001de90 f    CIL library: CIL module
 0001:0001ceb0       __unwindfunclet$?_Endwrite@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IAE_NXZ$0 1001deb0 f    CIL library: CIL module
 0001:0001ceb8       __ehhandler$?_Endwrite@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IAE_NXZ 1001deb8 f    CIL library: CIL module
 0001:0001cee0       __unwindfunclet$??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ$0 1001dee0 f    CIL library: CIL module
 0001:0001cee9       __ehhandler$??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ 1001dee9 f    CIL library: CIL module
 0001:0001cf10       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z$0 1001df10 f    CIL library: CIL module
 0001:0001cf10       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z$0 1001df10 f    CIL library: CIL module
 0001:0001cf10       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$0 1001df10 f    CIL library: CIL module
 0001:0001cf2d       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z$1 1001df2d f    CIL library: CIL module
 0001:0001cf2d       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$1 1001df2d f    CIL library: CIL module
 0001:0001cf2d       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z$1 1001df2d f    CIL library: CIL module
 0001:0001cf39       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z$3 1001df39 f    CIL library: CIL module
 0001:0001cf39       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z$3 1001df39 f    CIL library: CIL module
 0001:0001cf39       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$3 1001df39 f    CIL library: CIL module
 0001:0001cf42       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$2 1001df42 f    CIL library: CIL module
 0001:0001cf42       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z$2 1001df42 f    CIL library: CIL module
 0001:0001cf42       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z$2 1001df42 f    CIL library: CIL module
 0001:0001cf4d       __ehhandler$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PBDHH@Z 1001df4d f    CIL library: CIL module
 0001:0001cf4d       __ehhandler$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z 1001df4d f    CIL library: CIL module
 0001:0001cf4d       __ehhandler$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@HH@Z 1001df4d f    CIL library: CIL module
 0001:0001cf70       __unwindfunclet$??_D?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAEXXZ$0 1001df70 f    CIL library: CIL module
 0001:0001cf7c       __ehhandler$??_D?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAEXXZ 1001df7c f    CIL library: CIL module
 0001:0001cfa0       __unwindfunclet$?Display@IChatbox@@QAEXV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0 1001dfa0 f    CIL library: CIL module
 0001:0001cfa8       __ehhandler$?Display@IChatbox@@QAEXV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 1001dfa8 f    CIL library: CIL module
 0001:0001cfd0       __unwindfunclet$?Compile@ITools@@UAEPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z$0 1001dfd0 f    CIL library: CIL module
 0001:0001cfd8       __unwindfunclet$?Compile@ITools@@UAEPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z$1 1001dfd8 f    CIL library: CIL module
 0001:0001cfe0       __ehhandler$?Compile@ITools@@UAEPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z 1001dfe0 f    CIL library: CIL module
 0001:0001d010       __unwindfunclet$?Compile@ITools@@UAAPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ$0 1001e010 f    CIL library: CIL module
 0001:0001d018       __ehhandler$?Compile@ITools@@UAAPADPADV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 1001e018 f    CIL library: CIL module
 0001:0001d040       __unwindfunclet$?Compile@ITools@@UAEXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z$0 1001e040 f    CIL library: CIL module
 0001:0001d048       __unwindfunclet$?Compile@ITools@@UAEXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z$1 1001e048 f    CIL library: CIL module
 0001:0001d050       __ehhandler$?Compile@ITools@@UAEXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0@Z 1001e050 f    CIL library: CIL module
 0001:0001d080       __unwindfunclet$?Compile@ITools@@UAAXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ$0 1001e080 f    CIL library: CIL module
 0001:0001d088       __ehhandler$?Compile@ITools@@UAAXPADHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 1001e088 f    CIL library: CIL module
 0001:0001d0b0       __unwindfunclet$?GenerateSize@ITools@@UAEIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$0 1001e0b0 f    CIL library: CIL module
 0001:0001d0b8       __unwindfunclet$?GenerateSize@ITools@@UAEIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$1 1001e0b8 f    CIL library: CIL module
 0001:0001d0c0       __ehhandler$?GenerateSize@ITools@@UAEIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z 1001e0c0 f    CIL library: CIL module
 0001:0001d0f0       __unwindfunclet$?GenerateSize@ITools@@UAAIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ$0 1001e0f0 f    CIL library: CIL module
 0001:0001d0f8       __ehhandler$?GenerateSize@ITools@@UAAIV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 1001e0f8 f    CIL library: CIL module
 0001:0001d120       __unwindfunclet$?GetExecutableFromPath@ITools@@UAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V23@@Z$0 1001e120 f    CIL library: CIL module
 0001:0001d128       __ehhandler$?GetExecutableFromPath@ITools@@UAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V23@@Z 1001e128 f    CIL library: CIL module
 0001:0001d150       __unwindfunclet$?LoadDLL2@@YIXPAX0PA_WPAUHINSTANCE__@@@Z$0 1001e150 f    CIL library: CIL module
 0001:0001d150       __unwindfunclet$?LoadDLL@@YIXPAX0PADPAUHINSTANCE__@@@Z$0 1001e150 f    CIL library: CIL module
 0001:0001d15b       __unwindfunclet$?LoadDLL2@@YIXPAX0PA_WPAUHINSTANCE__@@@Z$1 1001e15b f    CIL library: CIL module
 0001:0001d15b       __unwindfunclet$?LoadDLL@@YIXPAX0PADPAUHINSTANCE__@@@Z$1 1001e15b f    CIL library: CIL module
 0001:0001d166       __unwindfunclet$?LoadDLL@@YIXPAX0PADPAUHINSTANCE__@@@Z$2 1001e166 f    CIL library: CIL module
 0001:0001d166       __unwindfunclet$?LoadDLL2@@YIXPAX0PA_WPAUHINSTANCE__@@@Z$2 1001e166 f    CIL library: CIL module
 0001:0001d175       __ehhandler$?LoadDLL2@@YIXPAX0PA_WPAUHINSTANCE__@@@Z 1001e175 f    CIL library: CIL module
 0001:0001d175       __ehhandler$?LoadDLL@@YIXPAX0PADPAUHINSTANCE__@@@Z 1001e175 f    CIL library: CIL module
 0001:0001d1b0       __unwindfunclet$??$_Buynode@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z$2 1001e1b0 f    CIL library: CIL module
 0001:0001d1b5       __ehhandler$??$_Buynode@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@?$_Tree_val@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@1@AAU?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z 1001e1b5 f    CIL library: CIL module
 0001:0001d1d0       __ehhandler$?_Copy@?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@IAEPAU_Node@?$_Tree_nod@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@2@PAU342@0@Z 1001e1d0 f    CIL library: CIL module
 0001:0001d1f0       __unwindfunclet$??0?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE@ABV01@@Z$2 1001e1f0 f    CIL library: CIL module
 0001:0001d1f8       __ehhandler$??0?$_Tree@V?$_Tmap_traits@HV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$less@H@2@V?$allocator@U?$pair@$$CBHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$0A@@std@@@std@@QAE@ABV01@@Z 1001e1f8 f    CIL library: CIL module
 0001:0001d220       __unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@XZ$0 1001e220 f    CIL library: CIL module
 0001:0001d23d       __unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@XZ$1 1001e23d f    CIL library: CIL module
 0001:0001d249       __unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@XZ$3 1001e249 f    CIL library: CIL module
 0001:0001d252       __ehhandler$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@XZ 1001e252 f    CIL library: CIL module
 0001:0001d270       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@XZ$0 1001e270 f    CIL library: CIL module
 0001:0001d28d       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@XZ$1 1001e28d f    CIL library: CIL module
 0001:0001d299       __unwindfunclet$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@XZ$3 1001e299 f    CIL library: CIL module
 0001:0001d2a2       __ehhandler$??0?$basic_ifstream@DU?$char_traits@D@std@@@std@@QAE@XZ 1001e2a2 f    CIL library: CIL module
 0001:0001d2c0       __unwindfunclet$??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ$0 1001e2c0 f    CIL library: CIL module
 0001:0001d2cc       __ehhandler$??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ 1001e2cc f    CIL library: CIL module
 0001:0001d2f0       __unwindfunclet$?engineCheck@@YA_NXZ$0 1001e2f0 f    CIL library: CIL module
 0001:0001d2fb       __unwindfunclet$?engineCheck@@YA_NXZ$1 1001e2fb f    CIL library: CIL module
 0001:0001d306       __ehhandler$?engineCheck@@YA_NXZ 1001e306 f    CIL library: CIL module
 0001:0001d330       __unwindfunclet$?str@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ$0 1001e330 f    CIL library: CIL module
 0001:0001d338       __unwindfunclet$?str@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ$2 1001e338 f    CIL library: CIL module
 0001:0001d340       __unwindfunclet$?str@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ$3 1001e340 f    CIL library: CIL module
 0001:0001d348       __ehhandler$?str@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ 1001e348 f    CIL library: CIL module
 0001:0001d370       __unwindfunclet$??_G?$basic_ifstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z$0 1001e370 f    CIL library: CIL module
 0001:0001d37c       __ehhandler$??_G?$basic_ifstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1001e37c f    CIL library: CIL module
 0001:0001d3a0       __unwindfunclet$?_Buynode@?$_List_val@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEPAU_Node@?$_List_nod@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@PAU342@0ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z$2 1001e3a0 f    CIL library: CIL module
 0001:0001d3a5       __ehhandler$?_Buynode@?$_List_val@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEPAU_Node@?$_List_nod@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@PAU342@0ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 1001e3a5 f    CIL library: CIL module
 0001:0001d3c0       __unwindfunclet$?Create@?$Interface@VIChatbox@@@@SAXXZ$0 1001e3c0 f    CIL library: CIL module
 0001:0001d3cc       __ehhandler$?Create@?$Interface@VIChatbox@@@@SAXXZ 1001e3cc f    CIL library: CIL module
 0001:0001d3f0       __unwindfunclet$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0 1001e3f0 f    CIL library: CIL module
 0001:0001d3f8       __unwindfunclet$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$1 1001e3f8 f    CIL library: CIL module
 0001:0001d400       __unwindfunclet$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$3 1001e400 f    CIL library: CIL module
 0001:0001d408       __unwindfunclet$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$4 1001e408 f    CIL library: CIL module
 0001:0001d410       __unwindfunclet$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$5 1001e410 f    CIL library: CIL module
 0001:0001d418       __ehhandler$?EnterText@IChatbox@@QAEHV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z 1001e418 f    CIL library: CIL module
 0001:0001d440       __unwindfunclet$??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z$0 1001e440 f    CIL library: CIL module
 0001:0001d44c       __ehhandler$??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z 1001e44c f    CIL library: CIL module
 0001:0001d470       __unwindfunclet$?Int2String@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z$0 1001e470 f    CIL library: CIL module
 0001:0001d47b       __unwindfunclet$?Int2String@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z$1 1001e47b f    CIL library: CIL module
 0001:0001d494       __unwindfunclet$?Int2String@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z$2 1001e494 f    CIL library: CIL module
 0001:0001d4a0       __ehhandler$?Int2String@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z 1001e4a0 f    CIL library: CIL module
 0001:0001d4c0       __unwindfunclet$?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z$0 1001e4c0 f    CIL library: CIL module
 0001:0001d4c5       __unwindfunclet$?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z$1 1001e4c5 f    CIL library: CIL module
 0001:0001d4ca       __ehhandler$?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z 1001e4ca f    CIL library: CIL module
 0001:0001d4f0       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$0 1001e4f0 f    CIL library: CIL module
 0001:0001d4f8       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$1 1001e4f8 f    CIL library: CIL module
 0001:0001d500       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$2 1001e500 f    CIL library: CIL module
 0001:0001d519       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$3 1001e519 f    CIL library: CIL module
 0001:0001d521       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$5 1001e521 f    CIL library: CIL module
 0001:0001d526       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$6 1001e526 f    CIL library: CIL module
 0001:0001d52b       __unwindfunclet$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z$4 1001e52b f    CIL library: CIL module
 0001:0001d533       __ehhandler$?Explode@ITools@@UAE?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@0@Z 1001e533 f    CIL library: CIL module
 0001:0001d560       __unwindfunclet$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$0 1001e560 f    CIL library: CIL module
 0001:0001d56b       __unwindfunclet$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$1 1001e56b f    CIL library: CIL module
 0001:0001d576       __unwindfunclet$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$2 1001e576 f    CIL library: CIL module
 0001:0001d581       __unwindfunclet$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$3 1001e581 f    CIL library: CIL module
 0001:0001d58c       __unwindfunclet$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$4 1001e58c f    CIL library: CIL module
 0001:0001d597       __ehhandler$?getHWID@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ 1001e597 f    CIL library: CIL module
 0001:0001d5d0       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$0 1001e5d0 f    CIL library: CIL module
 0001:0001d5d8       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$1 1001e5d8 f    CIL library: CIL module
 0001:0001d5e0       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$6 1001e5e0 f    CIL library: CIL module
 0001:0001d5e8       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$10 1001e5e8 f    CIL library: CIL module
 0001:0001d5f3       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$11 1001e5f3 f    CIL library: CIL module
 0001:0001d5fb       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$12 1001e5fb f    CIL library: CIL module
 0001:0001d606       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$21 1001e606 f    CIL library: CIL module
 0001:0001d615       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$23 1001e615 f    CIL library: CIL module
 0001:0001d624       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$15 1001e624 f    CIL library: CIL module
 0001:0001d643       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$16 1001e643 f    CIL library: CIL module
 0001:0001d662       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$17 1001e662 f    CIL library: CIL module
 0001:0001d681       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$18 1001e681 f    CIL library: CIL module
 0001:0001d6a0       __unwindfunclet$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z$19 1001e6a0 f    CIL library: CIL module
 0001:0001d6c2       __ehhandler$?Send@IPackets@@QAEHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PAD@Z 1001e6c2 f    CIL library: CIL module
 0001:0001d6f0       __unwindfunclet$?Send@IPackets@@QAAHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ$0 1001e6f0 f    CIL library: CIL module
 0001:0001d6f8       __ehhandler$?Send@IPackets@@QAAHEV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ZZ 1001e6f8 f    CIL library: CIL module
 0001:0001d720       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$0 1001e720 f    CIL library: CIL module
 0001:0001d72b       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$1 1001e72b f    CIL library: CIL module
 0001:0001d736       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$2 1001e736 f    CIL library: CIL module
 0001:0001d741       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$11 1001e741 f    CIL library: CIL module
 0001:0001d750       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$6 1001e750 f    CIL library: CIL module
 0001:0001d75b       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$15 1001e75b f    CIL library: CIL module
 0001:0001d77e       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$16 1001e77e f    CIL library: CIL module
 0001:0001d78a       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$18 1001e78a f    CIL library: CIL module
 0001:0001d796       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$9 1001e796 f    CIL library: CIL module
 0001:0001d7a1       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$19 1001e7a1 f    CIL library: CIL module
 0001:0001d7b0       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$3 1001e7b0 f    CIL library: CIL module
 0001:0001d7bb       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$21 1001e7bb f    CIL library: CIL module
 0001:0001d7ca       __unwindfunclet$?ChecksumCheck@@YAXPAX@Z$13 1001e7ca f    CIL library: CIL module
 0001:0001d7d9       __ehhandler$?ChecksumCheck@@YAXPAX@Z 1001e7d9 f    CIL library: CIL module
 0001:0001d810       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$3 1001e810 f    CIL library: CIL module
 0001:0001d81b       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$4 1001e81b f    CIL library: CIL module
 0001:0001d826       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$5 1001e826 f    CIL library: CIL module
 0001:0001d833       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$63 1001e833 f    CIL library: CIL module
 0001:0001d83e       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$9 1001e83e f    CIL library: CIL module
 0001:0001d849       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$11 1001e849 f    CIL library: CIL module
 0001:0001d86b       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$12 1001e86b f    CIL library: CIL module
 0001:0001d876       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$18 1001e876 f    CIL library: CIL module
 0001:0001d881       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$19 1001e881 f    CIL library: CIL module
 0001:0001d88c       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$20 1001e88c f    CIL library: CIL module
 0001:0001d897       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$21 1001e897 f    CIL library: CIL module
 0001:0001d8a2       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$22 1001e8a2 f    CIL library: CIL module
 0001:0001d8ad       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$23 1001e8ad f    CIL library: CIL module
 0001:0001d8b8       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$24 1001e8b8 f    CIL library: CIL module
 0001:0001d8c3       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$25 1001e8c3 f    CIL library: CIL module
 0001:0001d8ce       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$26 1001e8ce f    CIL library: CIL module
 0001:0001d8d9       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$27 1001e8d9 f    CIL library: CIL module
 0001:0001d8e4       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$30 1001e8e4 f    CIL library: CIL module
 0001:0001d8ef       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$34 1001e8ef f    CIL library: CIL module
 0001:0001d8fa       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$38 1001e8fa f    CIL library: CIL module
 0001:0001d905       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$39 1001e905 f    CIL library: CIL module
 0001:0001d910       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$40 1001e910 f    CIL library: CIL module
 0001:0001d91b       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$41 1001e91b f    CIL library: CIL module
 0001:0001d926       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$42 1001e926 f    CIL library: CIL module
 0001:0001d931       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$43 1001e931 f    CIL library: CIL module
 0001:0001d93c       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$44 1001e93c f    CIL library: CIL module
 0001:0001d947       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$45 1001e947 f    CIL library: CIL module
 0001:0001d952       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$46 1001e952 f    CIL library: CIL module
 0001:0001d95d       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$47 1001e95d f    CIL library: CIL module
 0001:0001d968       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$48 1001e968 f    CIL library: CIL module
 0001:0001d973       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$49 1001e973 f    CIL library: CIL module
 0001:0001d97e       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$50 1001e97e f    CIL library: CIL module
 0001:0001d989       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$51 1001e989 f    CIL library: CIL module
 0001:0001d9ab       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$52 1001e9ab f    CIL library: CIL module
 0001:0001d9cd       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$53 1001e9cd f    CIL library: CIL module
 0001:0001d9ef       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$54 1001e9ef f    CIL library: CIL module
 0001:0001d9fa       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$55 1001e9fa f    CIL library: CIL module
 0001:0001da05       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$56 1001ea05 f    CIL library: CIL module
 0001:0001da10       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$57 1001ea10 f    CIL library: CIL module
 0001:0001da1b       __unwindfunclet$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z$58 1001ea1b f    CIL library: CIL module
 0001:0001da26       __ehhandler$?Recv@IPackets@@QAEHPAUPacket@Engine@@@Z 1001ea26 f    CIL library: CIL module
 0001:0001da60       __unwindfunclet$??__FSeenPlayers@@YAXXZ$1 1001ea60 f    CIL library: CIL module
 0001:0001da65       __ehhandler$??__FSeenPlayers@@YAXXZ 1001ea65 f    CIL library: CIL module
 0001:0001da80       __unwindfunclet$??__FFxs@@YAXXZ$1 1001ea80 f    CIL library: CIL module
 0001:0001da85       __ehhandler$??__FFxs@@YAXXZ 1001ea85 f    CIL library: CIL module
 0001:0001daa0       __unwindfunclet$??__FPlayerNames@@YAXXZ$1 1001eaa0 f    CIL library: CIL module
 0001:0001daaa       __ehhandler$??__FPlayerNames@@YAXXZ 1001eaaa f    CIL library: CIL module
 0001:0001dad0       __unwindfunclet$??__FIgnorePlayers@@YAXXZ$1 1001ead0 f    CIL library: CIL module
 0001:0001dad5       __ehhandler$??__FIgnorePlayers@@YAXXZ 1001ead5 f    CIL library: CIL module
 0001:0001daf0       ??__Ebase64_chars@@YAXXZ   1001eaf0 f    CIL library: CIL module
 0001:0001db20       ??__E__i_Init_IChatbox@@YAXXZ 1001eb20 f    CIL library: CIL module
 0001:0001db40       ??__E__i_Init_IGraphics@@YAXXZ 1001eb40 f    CIL library: CIL module
 0001:0001db60       ??__E__i_Init_IOnSend@@YAXXZ 1001eb60 f    CIL library: CIL module
 0001:0001db80       ??__E__i_Init_IPackets@@YAXXZ 1001eb80 f    CIL library: CIL module
 0001:0001dba0       ??__ESeenPlayers@@YAXXZ    1001eba0 f    CIL library: CIL module
 0001:0001dc20       ??__ESummonedNPCs@@YAXXZ   1001ec20 f    CIL library: CIL module
 0001:0001dc30       ??__EPlayerNames@@YAXXZ    1001ec30 f    CIL library: CIL module
 0001:0001dcb0       ??__EIgnorePlayers@@YAXXZ  1001ecb0 f    CIL library: CIL module
 0001:0001dd30       ??__EFxs@@YAXXZ            1001ed30 f    CIL library: CIL module
 0001:0001ddb0       ??__ENKey@@YAXXZ           1001edb0 f    CIL library: CIL module
 0001:0001de10       ??__ETick@@YAXXZ           1001ee10 f    CIL library: CIL module
 0001:0001de20       ??__EID@@YAXXZ             1001ee20 f    CIL library: CIL module
 0001:0001de50       ??__EPWD@@YAXXZ            1001ee50 f    CIL library: CIL module
 0001:0001de80       ??__EfxLock@@YAXXZ         1001ee80 f    CIL library: CIL module
 0001:0001dea0       ??__E__i_Init_IProtect@@YAXXZ 1001eea0 f    CIL library: CIL module
 0001:0001dec0       ??__EforbiddenChars@@YAXXZ 1001eec0 f    CIL library: CIL module
 0001:0001def0       ??__E__i_Init_ITools@@YAXXZ 1001eef0 f    CIL library: CIL module
 0001:0001df10       ??__EsetCheck@@YAXXZ       1001ef10 f    CIL library: CIL module
 0001:0001df40       ??__Emac@@YAXXZ            1001ef40 f    CIL library: CIL module
 0001:0001df70       ??__EbuffLock@@YAXXZ       1001ef70 f    CIL library: CIL module
 0001:0001df90       ??__EPacketLock@@YAXXZ     1001ef90 f    CIL library: CIL module
 0001:0001dfb0       ??__EBuffIcons@@YAXXZ      1001efb0 f    CIL library: CIL module
 0001:0001dfc0       ??__EMD5Files@@YAXXZ       1001efc0 f    CIL library: CIL module
 0001:0001dfcc       ??__E_Fac_tidy_reg@std@@YAXXZ 1001efcc f   msvcprt:locale0_implib.obj
 0001:0001dfe0       ??__Fbase64_chars@@YAXXZ   1001efe0 f    CIL library: CIL module
 0001:0001e010       ??__F__i_Init_IChatbox@@YAXXZ 1001f010 f    CIL library: CIL module
 0001:0001e020       ??__F__i_Init_IGraphics@@YAXXZ 1001f020 f    CIL library: CIL module
 0001:0001e030       ??__F__i_Init_IOnSend@@YAXXZ 1001f030 f    CIL library: CIL module
 0001:0001e040       ??__F__i_Init_IPackets@@YAXXZ 1001f040 f    CIL library: CIL module
 0001:0001e050       ??__FSeenPlayers@@YAXXZ    1001f050 f    CIL library: CIL module
 0001:0001e0b0       ??__FSummonedNPCs@@YAXXZ   1001f0b0 f    CIL library: CIL module
 0001:0001e0f0       ??__FPlayerNames@@YAXXZ    1001f0f0 f    CIL library: CIL module
 0001:0001e160       ??__FIgnorePlayers@@YAXXZ  1001f160 f    CIL library: CIL module
 0001:0001e1c0       ??__FFxs@@YAXXZ            1001f1c0 f    CIL library: CIL module
 0001:0001e220       ??__FPWD@@YAXXZ            1001f220 f    CIL library: CIL module
 0001:0001e250       ??__FID@@YAXXZ             1001f250 f    CIL library: CIL module
 0001:0001e280       ??__FfxLock@@YAXXZ         1001f280 f    CIL library: CIL module
 0001:0001e2a0       ??__F__i_Init_IProtect@@YAXXZ 1001f2a0 f    CIL library: CIL module
 0001:0001e2b0       ??__FforbiddenChars@@YAXXZ 1001f2b0 f    CIL library: CIL module
 0001:0001e2e0       ??__F__i_Init_ITools@@YAXXZ 1001f2e0 f    CIL library: CIL module
 0001:0001e2f0       ??__FsetCheck@@YAXXZ       1001f2f0 f    CIL library: CIL module
 0001:0001e320       ??__Fmac@@YAXXZ            1001f320 f    CIL library: CIL module
 0001:0001e350       ??__FbuffLock@@YAXXZ       1001f350 f    CIL library: CIL module
 0001:0001e370       ??__FPacketLock@@YAXXZ     1001f370 f    CIL library: CIL module
 0001:0001e390       ??__FBuffIcons@@YAXXZ      1001f390 f    CIL library: CIL module
 0001:0001e3d0       ??__FMD5Files@@YAXXZ       1001f3d0 f    CIL library: CIL module
 0001:0001e414       ??__F_Fac_tidy_reg@std@@YAXXZ 1001f414 f   msvcprt:locale0_implib.obj
