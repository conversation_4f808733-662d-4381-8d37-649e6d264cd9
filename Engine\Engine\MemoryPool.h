#ifndef __MEMORYPOOL_H
#define __MEMORYPOOL_H

#include <Windows.h>

// Memory pool for packet data to avoid frequent allocations
class PacketMemoryPool {
private:
    static const size_t POOL_SIZE = 10;
    static const size_t MAX_PACKET_SIZE = 8192;
    char* pool[POOL_SIZE];
    bool used[POOL_SIZE];
    CRITICAL_SECTION cs;
    
public:
    PacketMemoryPool() {
        InitializeCriticalSection(&cs);
        for (int i = 0; i < POOL_SIZE; i++) {
            pool[i] = new char[MAX_PACKET_SIZE];
            used[i] = false;
        }
    }
    
    ~PacketMemoryPool() {
        for (int i = 0; i < POOL_SIZE; i++) {
            delete[] pool[i];
        }
        DeleteCriticalSection(&cs);
    }
    
    char* Acquire(size_t size) {
        if (size > MAX_PACKET_SIZE) {
            return new char[size]; // Fallback for large packets
        }
        
        EnterCriticalSection(&cs);
        for (int i = 0; i < POOL_SIZE; i++) {
            if (!used[i]) {
                used[i] = true;
                LeaveCriticalSection(&cs);
                return pool[i];
            }
        }
        LeaveCriticalSection(&cs);
        return new char[size]; // Fallback if pool exhausted
    }
    
    void Release(char* ptr, size_t size) {
        if (size > MAX_PACKET_SIZE) {
            delete[] ptr;
            return;
        }
        
        EnterCriticalSection(&cs);
        for (int i = 0; i < POOL_SIZE; i++) {
            if (pool[i] == ptr) {
                used[i] = false;
                LeaveCriticalSection(&cs);
                return;
            }
        }
        LeaveCriticalSection(&cs);
        delete[] ptr; // Fallback
    }
};

// Global instance
extern PacketMemoryPool g_PacketPool;

#endif
