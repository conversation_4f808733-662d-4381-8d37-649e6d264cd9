﻿cl : command line  warning D9035: option 'Gm' has been deprecated and will be removed in a future release
  Core.cpp
G:\Kal\New Project\Packet Protection\Core\Core\Core\TargetFind.h(24,39): warning C4302: 'type cast': truncation from 'const char *' to 'char'
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\Core\Core\Core\Summon.h(1604,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132): warning C4474: 'sscanf' : too many arguments passed for format string
  (compiling source file 'Core.cpp')
      G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132):
      placeholders and their parameters expect 4 variadic arguments, but 6 were provided
  
     Creating library G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.lib and object G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.exp
  Generating code
  Finished generating code
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(url.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(cookie.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(mprintf.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(formdata.obj)'
  Core.vcxproj -> G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.dll
