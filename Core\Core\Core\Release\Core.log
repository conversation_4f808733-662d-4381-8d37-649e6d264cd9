﻿cl : command line  warning D9035: option 'Gm' has been deprecated and will be removed in a future release
  Core.cpp
G:\Kal\New Project\Packet Protection\Core\Core\Core\TargetFind.h(24,39): warning C4302: 'type cast': truncation from 'const char *' to 'char'
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\PacketProtection.h(131,31): error C2065: 'MAX_PACKET_DELAY': undeclared identifier
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\PacketProtection.h(132,31): error C2065: 'SEQUENCE_WINDOW': undeclared identifier
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132): warning C4474: 'sscanf' : too many arguments passed for format string
  (compiling source file 'Core.cpp')
      G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132):
      placeholders and their parameters expect 4 variadic arguments, but 6 were provided
  
