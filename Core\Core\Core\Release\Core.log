﻿cl : command line  warning D9035: option 'Gm' has been deprecated and will be removed in a future release
  Core.cpp
G:\Kal\New Project\Packet Protection\Core\Core\Core\TargetFind.h(24,39): warning C4302: 'type cast': truncation from 'const char *' to 'char'
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\Core\Core\Core\Summon.h(1604,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132): warning C4474: 'sscanf' : too many arguments passed for format string
  (compiling source file 'Core.cpp')
      G:\Kal\New Project\Packet Protection\Core\Core\Core\Command.h(163,132):
      placeholders and their parameters expect 4 variadic arguments, but 6 were provided
  
cl : command line  warning D9035: option 'Gm' has been deprecated and will be removed in a future release
  sqlite3.c
  shell.c
     Creating library G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.lib and object G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.exp
Core.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::DetectPacketInjection(int,char const *,unsigned int)" (?DetectPacketInjection@PacketProtection@@SA_NHPBDI@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static unsigned int __cdecl PacketProtection::GenerateRandomKey(void)" (?GenerateRandomKey@PacketProtection@@SAIXZ)
Core.obj : error LNK2001: unresolved external symbol "public: static unsigned __int64 __cdecl PacketProtection::GetCurrentTimestamp(void)" (?GetCurrentTimestamp@PacketProtection@@SA_KXZ)
Core.obj : error LNK2001: unresolved external symbol "public: static void __cdecl PacketProtection::CleanupSocket(int)" (?CleanupSocket@PacketProtection@@SAXH@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::DecryptPacket(int,char *,unsigned int,unsigned char)" (?DecryptPacket@PacketProtection@@SA_NHPADIE@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl PacketProtection::InitializeServerProtection(int,unsigned int)" (?InitializeServerProtection@PacketProtection@@SA_NHI@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static unsigned int __cdecl PacketProtection::GenerateClientToken(int,unsigned char)" (?GenerateClientToken@PacketProtection@@SAIHE@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static bool __cdecl AntiCheat::AnalyzeSuspiciousActivity(int)" (?AnalyzeSuspiciousActivity@AntiCheat@@SA_NH@Z)
Core.obj : error LNK2001: unresolved external symbol "public: static void __cdecl AntiCheat::UpdatePlayerBehavior(int,unsigned char)" (?UpdatePlayerBehavior@AntiCheat@@SAXHE@Z)
Core.obj : error LNK2001: unresolved external symbol "struct ProtectionConfig g_protectionConfig" (?g_protectionConfig@@3UProtectionConfig@@A)
G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.dll : fatal error LNK1120: 10 unresolved externals
