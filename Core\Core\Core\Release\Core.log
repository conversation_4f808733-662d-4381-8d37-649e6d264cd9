﻿cl : command line  warning D9035: option 'Gm' has been deprecated and will be removed in a future release
  PacketProtection.cpp
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(309,19): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(356,25): warning C4018: '>': signed/unsigned mismatch
G:\Kal\New Project\Packet Protection\PacketProtection.cpp(445,28): warning C4018: '>=': signed/unsigned mismatch
  Skipping... (no relevant changes detected)
  Core.cpp
     Creating library G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.lib and object G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.exp
  Generating code
  Finished generating code
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(url.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(cookie.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(mprintf.obj)'
LINK : warning LNK4286: symbol '___iob_func' defined in '* CIL library *(* CIL module *)' is imported by 'libcurl_a.lib(formdata.obj)'
  Core.vcxproj -> G:\Kal\New Project\Packet Protection\Core\Core\Release\Core.dll
