#include "OnSend.h"
#include "Interface.h"
#include "Hooks.h"
#include "Tools.h"

CREATE_INTERFACE(IOnSend)

unsigned long IOnSend::Patches[] =
{	
	0x0041CDB4, 0x0041CE73, 0x0042D011, 0x0042D047, 0x0042D122, 0x0042D155, 0x0042DA71, 0x0042DAF5, 0x0042DBD3, 0x0042DC56, 0x0042DCDA, 0x0042F422, 0x0042F630, 0x00430A84, 0x00430EC3, 0x00431174, 0x004313FE, 0x00431432, 0x004314C6, 0x0043157D, 0x004315EF, 0x004319C8, 0x00431A35, 0x00431BCB, 0x00431C87, 0x00431CB2, 0x00431CC8, 0x004333B4, 0x004335FE, 0x004338EF, 0x004573FE, 0x0048F0DC, 0x004D4289, 0x004D42CC, 0x00540D61, 0x00544CD3, 0x00544CF7, 0x00544D1B, 0x00544D3F, 0x00544D63, 0x00544D87, 0x00544DAB, 0x00544DCF, 
	0x00544DF0, 0x00544E11, 0x00544E32, 0x00545CFD, 0x00545D24, 0x00545D4B, 0x00545D72, 0x00545D99, 0x00545DC0, 0x00545DE4, 0x00545E08, 0x00545E2C, 0x00545E6A, 0x00545EA8, 0x00545F55, 0x00545F7C, 0x00545FA3, 0x00545FCA, 0x00545FF1, 0x00546018, 0x0054603C, 0x00546060, 0x00546084, 0x005460CD, 0x00546113, 0x0054733C, 0x00548CFF, 0x0054937F, 0x0054DEE8, 0x0054E6AA, 0x0054E6F3, 0x0054EB59, 0x0054EC37, 0x0054EC80, 0x0054F0BF, 0x0054F4DB, 0x005500F3, 0x005502C3, 0x00550305, 0x00550544, 0x005506D2, 0x00551214, 0x00552AF6, 
	0x00552E6E, 0x00552FA1, 0x00553287, 0x005539F5, 0x005547EF, 0x00554832, 0x00554875, 0x005548B3, 0x00554938, 0x00554AB4, 0x00554D2C, 0x00554DEB, 0x00554E2D, 0x00555B3B, 0x00555B97, 0x00555C09, 0x00557F33, 0x00557F73, 0x005598CA, 0x0055D194, 0x0055D3C9, 0x0055E25B, 0x0055F3FB, 0x00560425, 0x005604E3, 0x00563482, 0x0056358A, 0x005635E4, 0x00565FF2, 0x0056A935, 0x0056AABB, 0x00578843, 0x00578887, 0x0057B1A3, 0x0057B4F0, 0x00580BBA, 0x005817CE, 0x005819E2, 0x00581AE0, 0x00581CA8, 0x00583F7B, 0x00584765, 0x00584D90, 
	0x00585141, 0x00585B2D, 0x00585B66, 0x00585BCF, 0x00585C06, 0x00585C31, 0x00585C8E, 0x00585D06, 0x00585D2A, 0x00585DA1, 0x00585DF6, 0x005863AE, 0x005863D7, 0x00586419, 0x00586449, 0x0058C7CB, 0x0058C817, 0x0058E839, 0x0059127F, 0x00593FC7, 0x005949EC, 0x00595362, 0x00597CF8, 0x00597D74, 0x00597E17, 0x00597EBA, 0x005992DC, 0x005992FC, 0x0059931C, 0x0059933C, 0x00599359, 0x005993D4, 0x005993F4, 0x0059942C, 0x0059946B, 0x005994AA, 0x0059990E, 0x00599996, 0x005999C2, 0x00599A21, 0x00599A7D, 0x0059A2EA, 0x0059C5D6,
	0x0059C7AF, 0x0059C9E1, 0x005A0478, 0x005A1277, 0x005A2E2F, 0x005AD681, 0x005AD791, 0x005B04B5, 0x005B0ECE, 0x005B0EE7, 0x005B2B1D, 0x005B2B44, 0x005B4CA4, 0x005B52B2, 0x005B5384, 0x005B53CC, 0x005B5DCA, 0x005B5E03, 0x005B6926, 0x005B6EC2, 0x005B711E, 0x005B754C, 0x005B797A, 0x005B83BF, 0x005B8493, 0x005B90B1, 0x005BA144, 0x005BB043, 0x005BC933, 0x005BDC4C, 0x005BEDD7, 0x005BEE07, 0x005BEE3A, 0x005BEE67, 0x005BEE97, 0x005BEFA2, 0x005BEFDF, 0x005BF02C, 0x005BF0A8, 0x005BF63F, 0x005BF676, 0x005BF795, 0x005BF7D1, 
	0x005BF80A, 0x005BF843, 0x005BF87C, 0x005BF8B8, 0x005BF8F1, 0x005BF92A, 0x005BF966, 0x005BF99F, 0x005BF9DA, 0x005BFA13, 0x005C013C, 0x005C0206, 0x005C023F, 0x005C031D, 0x005C03E2, 0x005C04A7, 0x005C0599, 0x005C07D9, 0x005C0831, 0x005C0882, 0x005C0B8D, 0x005C0BFC, 0x005C0FE9, 0x005C1202, 0x005C13CA, 0x005C145E, 0x005C16C4, 0x005C16F2, 0x005C1720, 0x005C174E, 0x005C177C, 0x005C17AA, 0x005C17D8, 0x005C1806, 0x005C1834, 0x005C1862, 0x005C1890, 0x005C18BE, 0x005C18EC, 0x005C191A, 0x005C1948, 0x005C19C1, 0x005C19EB, 
	0x005C1A3E, 0x005C1B55, 0x005C1C00, 0x005C1C3C, 0x005C1C82, 0x005C1C9E, 0x005C1EF3, 0x005C1F2A, 0x005C7AC4, 0x005C7B24, 0x005C7B63, 0x005C7BA2, 0x005C7BDE, 0x005C7C1A, 0x005C809C, 0x005C8516, 0x005DA50B, 0x005DB1D0, 0x005DB1F8, 0x005DB297, 0x005DCC37, 0x005DCF2E, 0x005DCF42, 0x005DF31F, 0x005DF411, 0x005DF481, 0x005E01EE, 0x005E0261, 0x005E0823, 0x005E083E, 0x005E09D3, 0x005E3115, 0x005E3F6C, 0x005E41AC, 0x005E43E5, 0x005E440C, 0x005E4476, 0x005E46F8, 0x005E4C74, 0x005E4FB2, 0x005E502A, 0x005E6023, 0x005E6206, 
	0x005E7398, 0x005E7B96, 0x005E82F5, 0x005E851D, 0x005ECD19, 0x005ECE7E, 0x005ECFB9, 0x005ED114, 0x005ED174, 0x005ED1D1, 0x005ED351, 0x005ED387, 0x005ED3C4, 0x005ED3FE, 0x005ED751, 0x005ED86A, 0x005ED8D6, 0x005ED9B1, 0x005EDB3A, 0x005EDCFB, 0x005EDE41, 0x005EE165, 0x005EE5D6, 0x005EEF4C, 0x005EF054, 0x005EF203, 0x005EF2F4, 0x005EF9EC, 0x005EFB81, 0x005F01F3, 0x005F0491, 0x005F0AC6, 0x005F0B6D, 0x005F16DF, 0x005F16EE, 0x005F1A98, 0x005F1AA7, 0x005F1F03, 0x005F1F12, 0x005F1FC7, 0x005F1FD6, 0x005F2BBE, 0x005FD5F5, 
	0x005FE636, 0x005FF0A4, 0x0060088C, 0x00600BDE, 0x00600C5A, 0x00600CF4, 0x00600D70, 0x00600E0A, 0x00600E86, 0x00601021, 0x00601044, 0x00601067, 0x00601BE0, 0x00601E9A, 0x00605DAE, 0x006083EA, 0x0060B79D, 0x0060B7BC, 0x0060BA65, 0x0060C339, 0x0060C36B, 0x0060C3D1, 0x0060C3E9, 0x0060C445, 0x0060C485, 0x0060F367, 0x0060F468, 0x0060F4A1, 0x0060F4C2, 0x00611A62, 0x0061FA96, 0x006259C7, 0x00625CA6, 0x00625CFD, 0x00627C9C, 0x00628666, 0x00628880, 0x00629BA4, 0x00629C6E, 0x0062A140, 0x0062A6E5, 0x0062B30C, 0x0062B77F, 
	0x0062B7E9, 0x0062BD10, 0x0062C951, 0x0062C9B9, 0x006397B5, 0x0063AF16, 0x0063AF66, 0x0063ECBA, 0x0063ED31, 0x0063EFBE, 0x0063F039, 0x0063F160, 0x0063F1B8, 0x0063F24A, 0x00642C64, 0x006455F6, 0x00645801, 0x00645A30, 0x0064A11B, 0x0064B61D, 0x0064F76A, 0x0064FA3A, 0x00650BEC, 0x00651538, 0x006517F5, 0x00654AB5, 0x00655400, 0x00655B09, 0x00661E64, 0x0066C881, 0x00678595, 0x0067B704, 0x0067B7B8, 0x0068158C, 0x006822F7, 0x00682320, 0x00682349, 0x0068239E, 0x00682452, 0x00682F30, 0x0068458B, 0x006845A4, 0x006845BD, 
	0x00684898, 0x00684A11, 0x006854F4, 0x00685E37, 0x0068670B, 0x00686724, 0x0068673D, 0x00686A91, 0x00686CB3, 0x00687131, 0x00688A46, 0x00688B2A, 0x00688BFC, 0x00688CC0, 0x00688CE7, 0x00688D3F, 0x006898B3, 0x00699A5E, 0x006B4596, 0x006B6BFA, 0x006B6E68, 0x006B70DE, 0x006B7231, 0x006B74B9, 0x006B75F0, 0x006B7677, 0x006B7743, 0x006B7767, 0x006B7788, 0x006B77A8, 0x006B77C9, 0x006B77FC, 0x006B7830, 0x006B7869, 0x006B78A1, 0x006B78DA, 0x006B7913, 0x006B794B, 0x006B7984, 0x006B7A17, 0x006B7A4B, 0x006B7A7E, 0x006B7AB2, 
	0x006B7B00, 0x006B7B34, 0x006B7B94, 0x006B7CAA, 0x006B7E1B, 0x006B7EAC, 0x006B801D, 0x006B80B1, 0x006B81AA, 0x006B82A5, 0x006B82D8, 0x006B830C, 0x006B83D6, 0x006B83F7, 0x006B8417, 0x006B8438, 0x006B8459, 0x006B8479, 0x006B8497, 0x006B84B5, 0x006B84D2, 0x006B84F0, 0x006B8950, 0x006B998C, 0x006B99B5, 0x006B99DE, 0x006B9ADF, 0x006B9BFD, 0x006B9C4F, 0x006B9E5D, 0x006B9EAD, 0x006B9EDB, 0x006B9F5E, 0x006B9FA5, 0x006BA137, 0x006BA1B3, 0x006BA21E, 0x006BA616, 0x006BA929, 0x006BB6DC, 0x006BB70E, 0x006BB73F, 0x006BBF28, 
	0x006BBF5B, 0x006BC020, 0x006BC053, 0x006BC453, 0x006BC4BB, 0x006BC523, 0x006BC58C, 0x006BC5B8, 0x006C548E, 0x006C55BA, 0x006C6851, 0x006C697D, 0x006C6BDF, 0x006C6C6D, 0x006C6C88, 0x006C6CD8, 0x006C6E76, 0x006C7303, 0x006C76FB, 0x006C7B04, 0x006C7B27, 0x006C7E05, 0x006C8130, 0x006C82BE, 0x006C8910, 0x006C89C6, 0x006C89D7, 0x006C8FC5, 0x006C94E5, 0x006C95BA, 0x006CBB82, 0x006CC504, 0x006D2358, 0x006D4976, 0x006D7990, 0x006D7CF0, 0x006D8AEB, 0x006D8D59, 0x006D902F, 0x006E47E1, 0x006E53A3, 0x006E542D, 0x006E54B7, 
	0x006E54F4, 0x006E55B5, 0x006E572B, 0x006E5BDC, 0x006E6475, 0x006E66C6, 0x006E67E2, 0x006E7929, 0x006E7B65, 0x006E826F, 0x006EA711, 0x006EC990, 0x006F04B2, 0x006F04C5, 0x006F04D8, 0x007342BB, 0x0073439B, 0x007367BA, 0x00736A03, 0x007386B0, 0x007393DE, 0x007394E0, 0x0073959E, 0x00739D73, 0x00739D9B, 0x007651C4, 0x007659C9, 0x0077CD24, 0x007CB452, 0x0056E84E, 0x0056E940, 0x0056EBC0, 0x0056ED3F, 0x0056ED71, 0x0056EDBF, 0x0056EDF1, 0x0056EF8A, 0x005730F0, 0x00573111, 0x00573132, 0x00573153, 0x00573171, 0x0057318F, 
	0x005731AD, 0x005731CB, 0x0057320F, 0x00573230, 0x00573251, 0x00573272, 0x00573290, 0x005732AE, 0x005732CC, 0x005732EA, 0x0057332E, 0x0057334F, 0x00573370, 0x00573391, 0x005733AF, 0x005733CD, 0x005733EB, 0x00573409, 0x0057344D, 0x0057346E, 0x0057348F, 0x005734B0, 0x005734CE, 0x005734EC, 0x0057350A, 0x00573528, 0x0057356C, 0x0057358D, 0x005735AE, 0x005735CF, 0x005735ED, 0x0057360B, 0x00573629, 0x00573647, 0x0057368B, 0x005736AC, 0x005736CA, 0x005736E8, 0x00573706, 0x00573724, 0x0057375B, 0x00573792, 0x00589987, 
	0x005C0D50, 0x005C0D9C, 0x005ED22C, 0x005ED460, 0x006131FB, 0x00618512, 0x006185C7, 0x00618972, 0x006BA962, 0x006BA98F, 0x006BA9BC, 0x006BA9E9, 0x006BAA16, 0x006BAA43, 0x006BAA70, 0x006BAA9D, 0x006BAACA, 0x006BAAF7, 0x006BAB24, 0x006BAB51, 0x006BAB7E, 0x006BABAB, 0x006BABD8, 0x006BAC05, 0x006BAC32, 0x006BAC5F, 0x006BAC8C, 0x006BACB9, 0x006BACE6, 0x006BC2D9, 0x006BC384, 0x006BC3B6, 0x006CC1D4, 0x006CEB18, 0x006CF25C, 0x006CF2D8, 0x007343CC, 0x007343F7, 0x00734424, 0x00734451, 0x0073447C, 0x007344A9
};

void IOnSend::Hook()
{
	Interface<ITools> Tools;

	for(size_t i=0; i < sizeof this->Patches / sizeof(unsigned long); i++)
		Tools->Intercept(ITools::_I_CALL, (void*)IOnSend::Patches[i], Hooks::KSocket::WritePacketAutoCrc, 5);
}