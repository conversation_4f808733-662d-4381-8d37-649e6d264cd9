[Map]
Index=7

; Jail Coordinate Format:
; (jailCoord (X X_Coordinate)(Y Y_Coordinate)(Z Z_Coordinate))
; or
; (jailCoord (X X_Coordinate)(Y Y_Coordinate))
;
; Parameters:
; - X: X coordinate for jail spawn location
; - Y: Y coordinate for jail spawn location  
; - Z: Z coordinate for jail spawn location (optional, defaults to 0)
;
; Multiple jail coordinates can be defined and the system will randomly
; select one when jailing a player.
;
; Example Jail Coordinates:
; (jailCoord (X 268444)(Y 242472)(Z 0))
; (jailCoord (X 268490)(Y 242485)(Z 0))
; (jailCoord (X 268549)(Y 242513)(Z 0))
; (jailCoord (X 268597)(Y 242536)(Z 0))

; Default jail coordinates (you can modify these or add more):
(jailCoord (X 268444)(Y 242472)(Z 0))
(jailCoord (X 268490)(Y 242485)(Z 0))
(jailCoord (X 268549)(Y 242513)(Z 0))
(jailCoord (X 268597)(Y 242536)(Z 0))

; Add additional jail coordinates below this line:
