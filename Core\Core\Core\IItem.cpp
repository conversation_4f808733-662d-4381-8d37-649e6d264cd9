#include <windows.h>
#include "IItem.h"
#include "IChar.h"
#include "Functions.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>

#define WEAPON 0
#define ARMOR 1
#define ORNAMENT 2
#define GENERAL 3
#define QUEST 4
#define MONEY 5
#define TRANSFORM 6

IItem::IItem(void* Offset)
{
	if (!Offset) {
		throw std::invalid_argument("IItem::IItem: Offset cannot be null");
	}
	this->Offset = Offset;
}

IItem::~IItem()
{
}

void* IItem::GetOffset()
{
	if (!this->Offset) {
		throw std::runtime_error("IItem::GetOffset: Offset is null");
	}
	return this->Offset;
}

bool IItem::Exists() {
	try {
		if (this->Offset) {
			if (CBase::IsDeleted(reinterpret_cast<int>(this->Offset))) {
				return false;
			}
			return true;
		}
		return false;
	} catch (...) {
		return false;
	}
}


int IItem::GetInfo()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 48)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetType()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 72))) - 2;
	} catch (...) {
		return 0;
	}
}

int IItem::Prefix()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 44)));
	} catch (...) {
		return 0;
	}
}

int IItem::PrefixID()
{
	try {
		if (this->Prefix()) {
			DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 44);
			if (!basePtr || !*basePtr) {
				return 0;
			}
			return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 32)));
		}
		return 0;
	} catch (...) {
		return 0;
	}
}

int IItem::GetIntEndurance()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 116)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetEndurance()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 96)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetIID()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 36)));
	} catch (...) {
		return 0;
	}
}

int IItem::CheckIndex()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 64)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetItemClass()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 68)));
	} catch (...) {
		return 0;
	}
}

bool IItem::HighMemory()
{
	try {
		int Class = this->GetItemClass();
		return (Class == WEAPON || Class == ARMOR || Class == TRANSFORM);
	} catch (...) {
		return false;
	}
}

bool IItem::IsOrnament()
{
	try {
		return (this->GetItemClass() == ORNAMENT);
	} catch (...) {
		return false;
	}
}

bool IItem::IsWeapon()
{
	try {
		return (this->GetItemClass() == WEAPON);
	} catch (...) {
		return false;
	}
}

bool IItem::IsMoney()
{
	try {
		return (this->GetItemClass() == MONEY);
	} catch (...) {
		return false;
	}
}

bool IItem::IsTransform()
{
	try {
		return (this->GetItemClass() == TRANSFORM);
	} catch (...) {
		return false;
	}
}

bool IItem::IsQuest()
{
	try {
		return (this->GetItemClass() == QUEST);
	} catch (...) {
		return false;
	}
}

bool IItem::IsGeneral()
{
	try {
		return (this->GetItemClass() == GENERAL);
	} catch (...) {
		return false;
	}
}

bool IItem::IsMask()
{
	try {
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return false;
		}
		int Type = static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 72)));
		return (this->GetItemClass() == ARMOR && Type == 20);
	} catch (...) {
		return false;
	}
}

bool IItem::IsStandard()
{
	try {
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return false;
		}
		int Type = static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 72)));
		return (this->GetItemClass() == ARMOR && Type == 22);
	} catch (...) {
		return false;
	}
}

bool IItem::IsDefense()
{
	try {
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return false;
		}
		int Type = static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 72)));
		return (this->GetItemClass() == ARMOR && (Type != 20 && Type != 22));
	} catch (...) {
		return false;
	}
}

int IItem::GetInitItem()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40)));
	} catch (...) {
		return 0;
	}
}

void IItem::IncreaseEndurance(int Value)
{
	try {
		if (!this->Exists() || Value < 0) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 96)) += static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

void IItem::DecreaseEndurance(int Value)
{
	try {
		if (!this->Exists() || Value < 0) {
			return;
		}
		DWORD* endurancePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 96);
		if (*endurancePtr >= static_cast<DWORD>(Value)) {
			*endurancePtr -= static_cast<DWORD>(Value);
		} else {
			*endurancePtr = 0;
		}
	} catch (...) {
		// Silently handle errors
	}
}

void IItem::SetInfo(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 48)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

int IItem::GetTalismanOA()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 100)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetTalismanOM()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 104)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetTOA()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 112)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetUpgrade()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 124)));
	} catch (...) {
		return 0;
	}
}

void IItem::SetTalismanOA(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 100)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

void IItem::SetTalismanOM(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 104)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

void IItem::SetTOA(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 112)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

void IItem::SetUpgrade(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 124)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

int IItem::GetAmount()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 52)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetSetGem()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 84)));
	} catch (...) {
		return 0;
	}
}

void IItem::SetSetGem(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 84)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

int IItem::LevelLimit()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 100)));
	} catch (...) {
		return 0;
	}
}

void IItem::SetLevelLimit(int Value)
{
	try {
		if (!this->Exists()) {
			return;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return;
		}
		*(reinterpret_cast<DWORD*>(*basePtr + 100)) = static_cast<DWORD>(Value);
	} catch (...) {
		// Silently handle errors
	}
}

int IItem::GetItemPointerFromIID(void *Player,int IID)
{
	try {
		if (!Player) {
			return 0;
		}

		IChar IPlayer(Player);

		if (IPlayer.IsOnline()) {
			int CheckValue = 0, Checkx = 0, Recheckx = 0, itemIID = this->GetIID(), MyItem = 0;
			Undefined::CreateMonsterValue(static_cast<char*>(IPlayer.GetOffset()) + 1068, reinterpret_cast<int>(&CheckValue), reinterpret_cast<int>(&itemIID));
			Checkx = Undefined::Check(reinterpret_cast<int>(static_cast<char*>(IPlayer.GetOffset()) + 1068), reinterpret_cast<int>(&Recheckx));

			if (Undefined::CheckValues(&CheckValue, Checkx)) {
				MyItem = *(reinterpret_cast<DWORD*>(Undefined::GetValue(&CheckValue) + 4));
				return MyItem;
			}
		}

		return 0;
	} catch (...) {
		return 0;
	}
}

int IItem::GetGrade()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 80)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetClass()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		DWORD* basePtr = reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 40);
		if (!basePtr || !*basePtr) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(*basePtr + 96)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetXDefense()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 108)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetXDodge()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 116)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetXHit()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 112)));
	} catch (...) {
		return 0;
	}
}

char IItem::GetXAttack()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<char>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 100)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetXMagic()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 104)));
	} catch (...) {
		return 0;
	}
}

int IItem::GetUpgrRate()
{
	try {
		if (!this->Exists()) {
			return 0;
		}
		return static_cast<int>(*(reinterpret_cast<DWORD*>(static_cast<char*>(this->Offset) + 128)));
	} catch (...) {
		return 0;
	}
}
