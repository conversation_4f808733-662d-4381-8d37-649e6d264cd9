[AreaUnlock]
Active=1

; AreaUnlock System Configuration
; Format: (Index %d)(Name [a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�])(Mobs %[0-9\\,-])(MobsToKill %[0-9\\,-])(TotalAmount %d)
; Index: Unique identifier for the area unlock
; Name: Display name for the area
; Mobs: Comma-separated list of mob indices that are locked in this area
; MobsToKill: Comma-separated list of mob indices that need to be killed to unlock this area
; TotalAmount: Total number of mobs that need to be killed to unlock this area

; Example configuration - replace with your actual area unlock requirements
; This matches your original hardcoded mob indices: 23, 19, 21, 29, 30, 31, 37
; ReplyQuest: Quest ID that players can use to check progress (0 = disabled)
; (Unlocked 0) = locked, (Unlocked 1) = manually unlocked, omit for default (locked)
(Index 1)(Name Beginner_Area)(Mobs 23,19,21)(MobsToKill 1,2,3)(TotalAmount 50)(ReplyQuest 1001)(Unlocked 0)
(Index 2)(Name Intermediate_Area)(Mobs 29,30,31)(MobsToKill 23,19,21)(TotalAmount 100)(ReplyQuest 1002)(Unlocked 0)
(Index 3)(Name Advanced_Area)(Mobs 37)(MobsToKill 29,30,31)(TotalAmount 200)(ReplyQuest 1003)(Unlocked 1)
