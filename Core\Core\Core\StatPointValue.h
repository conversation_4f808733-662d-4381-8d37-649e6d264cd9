static int StatTable[] =
{
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
	2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	9, 9, 9, 9, 9
};

static int _StatTable[] =
{
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
	2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
	2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
	5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	9, 9, 9, 9, 9
};

int QigongChest[][3] = {
	{3,0,1}, {3,1,1}, {3,4,1}, {3,3,1}, {3,2,1}, {4,0,1}, {4,1,1}, {4,4,1}, {4,3,1}, {4,2,1},
	{5,0,1}, {5,1,1}, {5,4,1}, {5,3,1}, {5,2,1}, {6,0,1}, {6,1,1}, {6,4,1}, {6,3,1}, {6,2,1},
	{7,0,2}, {7,1,2}, {7,4,2}, {7,3,2}, {7,2,2}, {8,0,2}, {8,1,2}, {8,4,2}, {8,3,2}, {8,2,2},
	{9,0,2}, {9,1,2}, {9,4,2}, {9,3,2}, {9,2,2}, {10,0,2}, {10,1,2}, {10,4,2}, {10,3,2}, {10,2,2},
	{11,0,3}, {11,1,3}, {11,4,3}, {11,3,3}, {11,2,3}, {12,0,3}, {12,1,3}, {12,4,3}, {12,3,3}, {12,2,3},
	{13,0,3}, {13,1,3}, {13,4,3}, {13,3,3}, {13,2,3}, {14,0,3}, {14,1,3}, {14,4,3}, {14,3,3}, {14,2,3},
	{15,0,5}, {15,1,5}, {15,4,5}, {15,3,5}, {15,2,5}, {16,0,5}, {16,1,5}, {16,4,5}, {16,3,5}, {16,2,5},
	{17,0,7}, {17,1,7}, {17,4,7}, {17,3,7}, {17,2,7}, {18,0,7}, {18,1,7}, {18,4,7}, {18,3,7}, {18,2,7}
};

int QigongMain[][3] = {
	{3,0,1}, {3,1,1}, {3,4,1}, {3,3,1}, {3,2,1}, {4,0,1}, {4,1,1}, {4,4,1}, {4,3,1}, {4,2,1},
	{5,0,1}, {5,1,1}, {5,4,1}, {5,3,1}, {5,2,1}, {6,0,1}, {6,1,1}, {6,4,1}, {6,3,1}, {6,2,1},
	{7,0,1}, {7,1,1}, {7,4,1}, {7,3,1}, {7,2,1}, {8,0,1}, {8,1,1}, {8,4,1}, {8,3,1}, {8,2,1},
	{9,0,2}, {9,1,2}, {9,4,2}, {9,3,2}, {9,2,2}, {10,0,2}, {10,1,2}, {10,4,2}, {10,3,2}, {10,1,2},
	{11,0,2}, {11,1,2}, {11,4,2}, {11,3,2}, {11,2,2}, {12,0,2}, {12,1,2}, {12,4,2}, {12,3,2}, {12,2,2},
	{13,0,3}, {13,1,3}, {13,4,3}, {13,3,3}, {13,2,3}, {14,0,3}, {14,1,3}, {14,4,3}, {14,3,3}, {14,2,3},
	{15,0,4}, {15,1,4}, {15,4,4}, {15,3,4}, {15,2,4}, {16,0,4}, {16,1,4}, {16,4,4}, {16,3,4}, {16,2,4},
	{17,0,5}, {17,1,5}, {17,4,5}, {17,3,5}, {17,2,5}, {18,0,5}, {18,1,5}, {18,4,5}, {18,3,5}, {18,2,5}
};

int QigongShort[][3] = {
	{3,0,1}, {3,1,1}, {3,4,1}, {3,3,1}, {3,2,1}, {4,0,1}, {4,1,1}, {4,4,1}, {4,3,1}, {4,2,1},
	{5,0,1}, {5,1,1}, {5,4,1}, {5,3,1}, {5,2,1}, {6,0,1}, {6,1,1}, {6,4,1}, {6,3,1}, {6,2,1},
	{7,0,1}, {7,1,1}, {7,4,1}, {7,3,1}, {7,2,1}, {8,0,1}, {8,1,1}, {8,4,1}, {8,3,1}, {8,2,1},
	{9,0,2}, {9,1,2}, {9,4,2}, {9,3,2}, {9,2,2}, {10,0,2}, {10,1,2}, {10,4,2}, {10,3,2}, {10,2,2},
	{11,0,2}, {11,1,2}, {11,4,2}, {11,3,2}, {11,2,2}, {12,0,2}, {12,1,2}, {12,4,2}, {12,3,2}, {12,2,2},
	{13,0,3}, {13,1,3}, {13,4,3}, {13,3,3}, {13,2,3}, {14,0,3}, {14,1,3}, {14,4,3}, {14,3,3}, {14,2,3},
	{15,0,5}, {15,1,5}, {15,4,5}, {15,3,5}, {15,2,5}, {16,0,5}, {16,1,5}, {16,4,5}, {16,3,5}, {16,2,5},
	{17,0,6}, {17,1,6}, {17,4,6}, {17,3,6}, {17,2,6}, {18,0,6}, {18,1,6}, {18,4,6}, {18,3,6}, {18,2,6}
};

int QigongWeapon[][7] = {
	{1,1,30,10,2,0,1}, {1,1,30,10,2,1,1}, {1,1,30,10,2,4,1}, {1,1,30,10,2,3,1}, {1,1,30,10,2,2,1},
	{2,2,30,10,2,0,1}, {2,2,30,10,2,1,1}, {2,2,30,10,2,4,1}, {2,2,30,10,2,3,1}, {2,2,30,10,2,2,1},
	{3,3,50,30,4,0,1}, {3,3,50,30,4,1,1}, {3,3,50,30,4,4,1}, {3,3,50,30,4,3,1}, {3,3,50,30,4,2,1},
	{4,4,50,30,4,0,1}, {4,4,50,30,4,1,1}, {4,4,50,30,4,4,1}, {4,4,50,30,4,3,1}, {4,4,50,30,4,2,1},
	{5,5,70,50,6,0,2}, {5,5,70,50,6,1,2}, {5,5,70,50,6,4,2}, {5,5,70,50,6,3,2}, {5,5,70,50,6,2,2},
	{6,6,70,50,6,0,2}, {6,6,70,50,6,1,2}, {6,6,70,50,6,4,2}, {6,6,70,50,6,3,2}, {6,6,70,50,6,2,2},
	{7,7,90,70,8,0,2}, {7,7,90,70,8,1,2}, {7,7,90,70,8,4,2}, {7,7,90,70,8,3,2}, {7,7,90,70,8,2,2},
	{8,8,90,70,8,0,2}, {8,8,90,70,8,1,2}, {8,8,90,70,8,4,2}, {8,8,90,70,8,3,2}, {8,8,90,70,8,2,2},
	{9,9,110,90,10,0,3}, {9,9,110,90,10,1,3}, {9,9,110,90,10,4,3}, {9,9,110,90,10,3,3}, {9,9,110,90,10,2,3},
	{10,10,110,90,10,0,3}, {10,10,110,90,10,1,3}, {10,10,110,90,10,4,3}, {10,10,110,90,10,3,3}, {10,10,110,90,10,2,3},
	{11,11,130,110,12,0,3}, {11,11,130,110,12,1,3}, {11,11,130,110,12,4,3}, {11,11,130,110,12,3,3}, {11,11,130,110,12,2,3},
	{12,12,130,110,12,0,3}, {12,12,130,110,12,1,3}, {12,12,130,110,12,4,3}, {12,12,130,110,12,3,3}, {12,12,130,110,12,2,3},
	{13,13,200,150,15,0,5}, {13,13,200,150,15,1,5}, {13,13,200,150,15,4,5}, {13,13,200,150,15,3,5}, {13,13,200,150,15,2,5},
	{14,14,200,150,15,0,5}, {14,14,200,150,15,1,5}, {14,14,200,150,15,4,5}, {14,14,200,150,15,3,5}, {14,14,200,150,15,2,5},
	{16,16,250,200,18,0,8}, {16,16,250,200,18,1,8}, {16,16,250,200,18,4,8}, {16,16,250,200,18,3,8}, {16,16,250,200,18,2,8},
	{17,17,250,200,18,0,8}, {17,17,250,200,18,1,8}, {17,17,250,200,18,4,8}, {17,17,250,200,18,3,8}, {17,17,250,200,18,2,8}
};

int NormalPickaxe[][3] = {
	{2537,2530,2531},
	{2532,2533,2534},
	{2535,2536,2539}
};

int BlueDragonPickaxe[][11] = {
	{2530,2531,2532,2533,2534,2530,2531,2532,2533,2534,2530},
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668},
	{2657,2658,2661,2662,2665,2666,2669,2670,2671,2672,2673}
};

int WhiteTigerPickaxe[][11] = {
	{2530,2531,2532,2533,2534,2537,2530,2531,2532,2533,2534},
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668},
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668}
};

int RedBirdPickaxe[][11] = {
	{2530,2531,2532,2533,2534,2530,2531,2532,2533,2534,2530},
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668},
	{2657,2658,2661,2662,2665,2666,2669,2670,2671,2672,2673}
};

int BlackTorotisePickaxe[][11] = {
	{2530,2531,2532,2533,2534,2530,2531,2532,2533,2534,2530},
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668},
	{2657,2658,2661,2662,2665,2666,2669,2670,2671,2672,2673}
};

int MysteriousPickaxe[][11] = {
	{2535,2536,2539,2655,2656,2659,2660,2663,2664,2667,2668},
	{2657,2658,2661,2662,2665,2666,2669,2670,2671,2672,2673},
	{2539,2674,2675,2539,2674,2675,2539,2674,2675,2539,2674}
};

int DemonGongStoneEnchant[][9] = {
	{10000,10000,10000,10000,10000,10000,10000,10000,10000},
	{8000,9000,10000,10000,10000,10000,10000,10000,10000},
	{3500,6000,8500,10000,10000,10000,10000,10000,10000},
	{1000,2000,7000,8000,10000,10000,10000,10000,10000},
	{400,700,2000,5000,7500,10000,10000,10000,10000},
	{130,250,600,1500,5000,7000,10000,10000,10000},
	{40,80,200,450,1150,4400,6500,10000,10000},
	{13,25,60,140,350,900,3000,6000,10000},
	{4,7,17,39,95,250,650,2300,5500}
};

int DemonGongStoneStat[][10] = {
	{1,2,3,5,8,13,19,27,37,50},
	{1,2,3,4,6,8,10,12,15,20}
};

int TriagramUpgradeRate[][14] = {
	{10000,7000,5000,3500,2500,1700,1200,1000,800,500,350,250,100,1}
};

int TriagramStats[][15] = {
	{26,32,38,44,50,56,62,68,74,80,86,92,98,104,110},
	{32,43,53,64,75,86,96,107,118,129,139,150,161,172,182},
	{42,60,77,95,112,130,147,165,182,200,217,235,252,270,287},
	{9,12,14,16,18,21,23,25,27,30,32,34,36,39,41},
	{15,20,25,30,35,40,45,50,55,60,65,70,75,80,85},
	{21,30,38,47,56,65,73,82,91,100,108,117,126,135,143},
	{1,2,2,3,3,4,4,5,5,6,6,6,7,7,8},
	{3,4,5,6,7,8,9,10,11,12,13,14,15,16,17},
	{6,9,12,15,18,20,23,26,29,32,34,37,40,43,46},
	{1,1,1,1,1,1,1,1,1,2,2,2,2,2,2},
	{1,1,1,1,1,2,2,2,2,3,3,3,3,4,4},
	{1,1,2,2,2,3,3,4,4,5,5,6,6,7,7},
	{1,1,1,1,1,1,1,1,1,2,2,2,2,2,2},
	{1,1,1,1,1,2,2,2,2,3,3,3,3,4,4},
	{1,1,2,2,2,3,3,4,4,5,5,6,6,7,7},
	{1,1,1,1,1,1,1,1,1,2,2,2,2,2,2},
	{1,1,1,1,1,2,2,2,2,3,3,3,3,4,4},
	{1,1,2,2,2,3,3,4,4,5,5,6,6,7,7},
	{1,1,1,1,1,1,1,1,1,2,2,2,2,2,2},
	{1,1,1,1,1,2,2,2,2,3,3,3,3,4,4},
	{1,1,2,2,2,3,3,4,4,5,5,6,6,7,7},
	{1,1,1,1,1,1,1,1,1,2,2,2,2,2,2},
	{1,1,1,1,1,2,2,2,2,3,3,3,3,4,4},
	{1,1,2,2,2,3,3,4,4,5,5,6,6,7,7}
};

int YinYangRate[][15] = {
	{10,13,16,20,25,32,40,50,64,80,101,127,160,202,254},
	{10,13,17,21,28,36,46,59,77,100,129,166,215,277,357},
	{10,13,17,22,29,39,51,66,87,114,149,195,255,335,438}
};

int TaegeukPrefix[][8] = {
	{100,0,0,0,0,0,0,0},
	{0,36,0,0,0,0,0,0},
	{0,0,6,0,0,0,0,0},
	{0,0,0,2,0,0,0,0},
	{0,0,0,0,2,0,0,0},
	{0,0,0,0,0,2,0,0},
	{0,0,0,0,0,0,2,0},
	{0,0,0,0,0,0,0,2},
	{150,0,0,0,0,0,0,0},
	{0,70,0,0,0,0,0,0},
	{0,0,14,0,0,0,0,0},
	{0,0,0,2,0,0,0,0},
	{0,0,0,0,2,0,0,0},
	{0,0,0,0,0,2,0,0},
	{0,0,0,0,0,0,2,0},
	{0,0,0,0,0,0,0,2},
	{224,0,0,0,0,0,0,0},
	{0,112,0,0,0,0,0,0},
	{0,0,36,0,0,0,0,0},
	{0,0,0,4,0,0,0,0},
	{0,0,0,0,4,0,0,0},
	{0,0,0,0,0,4,0,0},
	{0,0,0,0,0,0,4,0},
	{0,0,0,0,0,0,0,4},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{160,0,0,0,0,0,0,0},
	{0,60,0,0,0,0,0,0},
	{0,0,12,0,0,0,0,0},
	{0,0,0,4,0,0,0,0},
	{0,0,0,0,4,0,0,0},
	{0,0,0,0,0,4,0,0},
	{0,0,0,0,0,0,4,0},
	{0,0,0,0,0,0,0,4},
	{258,0,0,0,0,0,0,0},
	{0,120,0,0,0,0,0,0},
	{0,0,24,0,0,0,0,0},
	{0,0,0,6,0,0,0,0},
	{0,0,0,0,6,0,0,0},
	{0,0,0,0,0,6,0,0},
	{0,0,0,0,0,0,6,0},
	{0,0,0,0,0,0,0,6},
	{400,0,0,0,0,0,0,0},
	{0,200,0,0,0,0,0,0},
	{0,0,64,0,0,0,0,0},
	{0,0,0,10,0,0,0,0},
	{0,0,0,0,10,0,0,0},
	{0,0,0,0,0,10,0,0},
	{0,0,0,0,0,0,10,0},
	{0,0,0,0,0,0,0,10},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{220,0,0,0,0,0,0,0},
	{0,84,0,0,0,0,0,0},
	{0,0,18,0,0,0,0,0},
	{0,0,0,6,0,0,0,0},
	{0,0,0,0,6,0,0,0},
	{0,0,0,0,0,6,0,0},
	{0,0,0,0,0,0,6,0},
	{0,0,0,0,0,0,0,6},
	{366,0,0,0,0,0,0,0},
	{0,170,0,0,0,0,0,0},
	{0,0,34,0,0,0,0,0},
	{0,0,0,10,0,0,0,0},
	{0,0,0,0,10,0,0,0},
	{0,0,0,0,0,10,0,0},
	{0,0,0,0,0,0,10,0},
	{0,0,0,0,0,0,0,10},
	{556,0,0,0,0,0,0,0},
	{0,228,0,0,0,0,0,0},
	{0,0,92,0,0,0,0,0},
	{0,0,0,16,0,0,0,0},
	{0,0,0,0,16,0,0,0},
	{0,0,0,0,0,16,0,0},
	{0,0,0,0,0,0,16,0},
	{0,0,0,0,0,0,0,16},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{0,0,0,0,0,0,0,0},
	{100,0,6,0,0,0,0,0},
	{0,36,0,0,0,2,0,0},
	{0,0,6,2,0,0,0,0},
	{0,0,0,2,0,0,0,2},
	{0,0,0,2,2,0,0,0},
	{0,0,6,0,0,2,0,0},
	{0,0,0,0,0,2,2,0},
	{100,0,0,0,0,0,0,2},
	{150,0,14,0,0,0,0,0},
	{0,70,0,0,0,2,0,0},
	{0,0,14,2,0,0,0,0},
	{0,0,0,2,0,0,0,2},
	{0,0,0,2,2,0,0,0},
	{0,0,14,0,0,2,0,0},
	{0,0,0,0,0,2,2,0},
	{150,0,0,0,0,0,0,2},
	{224,0,36,0,0,0,0,0},
	{0,112,0,0,0,4,0,0},
	{0,0,36,4,0,0,0,0},
	{0,0,0,4,0,0,0,4},
	{0,0,0,4,4,0,0,0},
	{0,0,36,0,0,4,0,0},
	{0,0,0,0,0,4,4,0},
	{224,0,0,0,0,0,0,4},
	{160,0,12,0,0,0,0,0},
	{0,60,0,0,0,4,0,0},
	{0,0,12,4,0,0,0,0},
	{0,0,0,4,0,0,0,4},
	{0,0,0,4,4,0,0,0},
	{0,0,12,0,0,4,0,0},
	{0,0,0,0,0,4,4,0},
	{160,0,0,0,0,0,0,4},
	{258,0,24,0,0,0,0,0},
	{0,120,0,0,0,6,0,0},
	{0,0,24,6,0,0,0,0},
	{0,0,0,6,0,0,0,6},
	{0,0,0,6,6,0,0,0},
	{0,0,24,0,0,6,0,0},
	{0,0,0,0,0,6,6,0},
	{258,0,0,0,0,0,0,6},
	{400,0,64,0,0,0,0,0},
	{0,200,0,0,0,10,0,0},
	{0,0,64,10,0,0,0,0},
	{0,0,0,10,0,0,0,10},
	{0,0,0,10,10,0,0,0},
	{0,0,64,0,0,10,0,0},
	{0,0,0,0,0,10,10,0},
	{400,0,0,0,0,0,0,10},
	{800,0,0,0,0,0,0,0},
	{0,400,0,0,0,0,0,0},
	{0,0,128,0,0,0,0,0},
	{0,0,0,20,0,0,0,0},
	{0,0,0,0,20,0,0,0},
	{0,0,0,0,0,20,0,0},
	{0,0,0,0,0,0,20,0},
	{0,0,0,0,0,0,0,20},
	{220,0,18,0,0,0,0,0},
	{0,84,0,0,0,6,0,0},
	{0,0,18,6,0,0,0,0},
	{0,0,0,6,0,0,0,6},
	{0,0,0,6,6,0,0,0},
	{0,0,18,0,0,6,0,0},
	{0,0,0,0,0,6,6,0},
	{220,0,0,0,0,0,0,6},
	{366,0,34,0,0,0,0,0},
	{0,170,0,0,0,10,0,0},
	{0,0,34,10,0,0,0,0},
	{0,0,0,10,0,0,0,10},
	{0,0,0,10,10,0,0,0},
	{0,0,34,0,0,10,0,0},
	{0,0,0,0,0,10,10,0},
	{366,0,0,0,0,0,0,10},
	{576,0,92,0,0,0,0,0},
	{0,288,0,0,0,16,0,0},
	{0,0,92,16,0,0,0,0},
	{0,0,0,16,0,0,0,16},
	{0,0,0,16,16,0,0,0},
	{0,0,92,0,0,16,0,0},
	{0,0,0,0,0,16,16,0},
	{576,0,0,0,0,0,0,16},
	{1200,0,0,0,0,0,0,0},
	{0,600,0,0,0,0,0,0},
	{0,0,192,0,0,0,0,0},
	{0,0,0,30,0,0,0,0},
	{0,0,0,0,30,0,0,0},
	{0,0,0,0,0,30,0,0},
	{0,0,0,0,0,0,30,0},
	{0,0,0,0,0,0,0,30}
};

int DemonGongStatArmorEarth[][10] = {
	{2,4,6,8,10,12,15,18,21,26},
	{1,1,2,2,2,3,3,3,3,4}
};

int DemonGongStatWeaponEarth[][10] = {
	{5,10,15,20,25,30,35,40,45,50},
	{1,2,3,4,5,6,7,8,9,10}
};

int DemonGongStatArmorWind[][10] = {
	{50,60,80,90,100,110,130,160,190,250},
	{1,2,2,3,3,4,4,4,5,5}
};

int DemonGongStatWeaponWind[][10] = {
	{5,10,15,20,25,30,35,40,45,50},
	{1,2,3,4,5,6,7,8,9,10}
};

int DemonGongStatArmorWater[][10] = {
	{1,2,2,2,3,3,3,4,4,5},
	{1,2,3,5,5,5,8,10,10,12}
};

int DemonGongStatWeaponWater[][10] = {
	{5,10,15,20,25,30,35,40,45,50},
	{90,100,110,130,170,190,210,250,290,350}
};

int RidingEnchant[][4] = {
	{ 7907,7908,7909,7910 },
	{ 30000, 60000, 100000, 200000},
	{ 0, 19, 39, 59 }
};

int RidingUpgrade[][3] = {
	{ 7943,7944,7945 },
	{ 50,30,20 },
	{ 1000000, 3000000, 4000000 }
};

int RidingHelmet[][10] = {
	{ 2,3,4,5,6,7,8,9,10,11 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 }
};

int RidingSaddle[][10] = {
	{ 2,3,4,5,6,7,8,9,10,11 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 }
};

int RidingArmor[][10] = {
	{ 2,3,4,5,6,7,8,9,10,11 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 },
	{ 1,2,3,4,5,6,7,8,9,10 }
};

int RidingCloth[][10] = {
	{ 1,2,2,3,3,4,4,5,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 }
};

int RidingMagnet[][10] = {
	{ 1,2,2,3,3,4,4,5,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 },
	{ 1,1,2,2,3,3,4,4,5,6 }
};

int CustomWeaponEnchant[10] = { 900000,1500000,2400000,4200000,7500000,12900000,21600000,36900000,62700000,106500000 };

int JailX[4] = { 268444, 268490, 268549, 268597 };
int JailY[4] = { 242472, 242485, 242513, 242536 };