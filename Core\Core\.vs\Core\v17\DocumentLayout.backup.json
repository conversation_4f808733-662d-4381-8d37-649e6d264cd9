{"Version": 1, "WorkspaceRootPath": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\FinalDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\FinalDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Command.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Command.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Trade.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Trade.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Quest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Quest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ReadConfig.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ReadConfig.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Channel.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Channel.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\IChar.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\IChar.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Interface.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Interface.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Tools.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Core.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Core.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Tools.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Start.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Start.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ExpTable.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ExpTable.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Player.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Player.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryGuard.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\MemoryGuard.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Packet.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Packet.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryRAII_Examples.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\MemoryRAII_Examples.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\SecureBuffer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\SecureBuffer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Timer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Timer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\BufferExamples.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\BufferExamples.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Buffer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Buffer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ISkill.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ISkill.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Wave.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Wave.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Skill.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Skill.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\PasswordDecode.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\PasswordDecode.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\CChar.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\CChar.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Process.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Process.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Memory.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Memory.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\include\\memory||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "Command.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Command.h", "RelativeDocumentMoniker": "Core\\Command.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Command.h", "RelativeToolTip": "Core\\Command.h", "ViewState": "AgIAALsAAAAAAAAAAADgv58AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-29T01:26:01.324Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "FinalDamage.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\FinalDamage.h", "RelativeDocumentMoniker": "Core\\FinalDamage.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\FinalDamage.h", "RelativeToolTip": "Core\\FinalDamage.h", "ViewState": "AgIAAC4DAAAAAAAAAAAiwFgDAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-26T23:37:49.612Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Trade.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Trade.h", "RelativeDocumentMoniker": "Core\\Trade.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Trade.h", "RelativeToolTip": "Core\\Trade.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-26T23:36:41.651Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Quest.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Quest.h", "RelativeDocumentMoniker": "Core\\Quest.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Quest.h", "RelativeToolTip": "Core\\Quest.h", "ViewState": "AgIAAPsAAAAAAAAAAAAiwBEBAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-14T21:53:57.81Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Channel.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Channel.h", "RelativeDocumentMoniker": "Core\\Channel.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Channel.h", "RelativeToolTip": "Core\\Channel.h", "ViewState": "AgIAAEcAAAAAAAAAAAAawFIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-03T13:08:40.208Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "memory", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\include\\memory", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\include\\memory", "ViewState": "AgIAAPcNAAAAAAAAAAAiwAYOAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-05-28T05:26:06.661Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "Interface.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Interface.h", "RelativeDocumentMoniker": "Core\\Interface.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Interface.h", "RelativeToolTip": "Core\\Interface.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAADIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T04:48:28.834Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Tools.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.cpp", "RelativeDocumentMoniker": "Core\\Tools.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.cpp", "RelativeToolTip": "Core\\Tools.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-28T03:22:56.242Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "Tools.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.h", "RelativeDocumentMoniker": "Core\\Tools.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Tools.h", "RelativeToolTip": "Core\\Tools.h", "ViewState": "AgIAABwAAACAXgSfArkvwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T03:22:45.319Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Start.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Start.h", "RelativeDocumentMoniker": "Core\\Start.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Start.h", "RelativeToolTip": "Core\\Start.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T03:22:39.249Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "MemoryGuard.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryGuard.h", "RelativeDocumentMoniker": "Core\\MemoryGuard.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryGuard.h", "RelativeToolTip": "Core\\MemoryGuard.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T03:06:26.027Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "MemoryRAII_Examples.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryRAII_Examples.h", "RelativeDocumentMoniker": "Core\\MemoryRAII_Examples.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\MemoryRAII_Examples.h", "RelativeToolTip": "Core\\MemoryRAII_Examples.h", "ViewState": "AgIAAHQAAAAAAAAAAAAawAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T03:05:45.569Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ExpTable.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ExpTable.h", "RelativeDocumentMoniker": "Core\\ExpTable.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ExpTable.h", "RelativeToolTip": "Core\\ExpTable.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAANEFAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T03:02:25.649Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "Timer.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Timer.h", "RelativeDocumentMoniker": "Core\\Timer.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Timer.h", "RelativeToolTip": "Core\\Timer.h", "ViewState": "AgIAAGMGAAAAAAAAAAAuwHYGAABvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:40:01.863Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "SecureBuffer.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\SecureBuffer.h", "RelativeDocumentMoniker": "Core\\SecureBuffer.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\SecureBuffer.h", "RelativeToolTip": "Core\\SecureBuffer.h", "ViewState": "AgIAAA8AAAAAAAAAAAAawCAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:39:40.633Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "BufferExamples.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\BufferExamples.h", "RelativeDocumentMoniker": "Core\\BufferExamples.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\BufferExamples.h", "RelativeToolTip": "Core\\BufferExamples.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:34:06.56Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "Buffer.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Buffer.h", "RelativeDocumentMoniker": "Core\\Buffer.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Buffer.h", "RelativeToolTip": "Core\\Buffer.h", "ViewState": "AgIAAJcAAAAAAAAAAAAawAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:34:00.177Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ReadConfig.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ReadConfig.h", "RelativeDocumentMoniker": "Core\\ReadConfig.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ReadConfig.h", "RelativeToolTip": "Core\\ReadConfig.h", "ViewState": "AgIAAEwFAAAAAAAAAAAawHMFAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:05:46.462Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ISkill.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ISkill.cpp", "RelativeDocumentMoniker": "Core\\ISkill.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\ISkill.cpp", "RelativeToolTip": "Core\\ISkill.cpp", "ViewState": "AgIAAB4AAAAAAAAAAAApwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-28T02:03:43.466Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "Player.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Player.h", "RelativeDocumentMoniker": "Core\\Player.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Player.h", "RelativeToolTip": "Core\\Player.h", "ViewState": "AgIAAGcCAAAAAAAAAAAawAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:01:30.608Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Wave.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Wave.h", "RelativeDocumentMoniker": "Core\\Wave.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Wave.h", "RelativeToolTip": "Core\\Wave.h", "ViewState": "AgIAAA4AAAAAAAAAAAApwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T02:01:10.772Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "Skill.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Skill.h", "RelativeDocumentMoniker": "Core\\Skill.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Skill.h", "RelativeToolTip": "Core\\Skill.h", "ViewState": "AgIAAOUDAAAAAAAAAAApwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T01:59:56.143Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Packet.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Packet.h", "RelativeDocumentMoniker": "Core\\Packet.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Packet.h", "RelativeToolTip": "Core\\Packet.h", "ViewState": "AgIAAGYLAAAAAAAAAAAuwHkLAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T01:59:05.419Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "PasswordDecode.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\PasswordDecode.h", "RelativeDocumentMoniker": "Core\\PasswordDecode.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\PasswordDecode.h", "RelativeToolTip": "Core\\PasswordDecode.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T01:58:56.299Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "CChar.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\CChar.h", "RelativeDocumentMoniker": "Core\\CChar.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\CChar.h", "RelativeToolTip": "Core\\CChar.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T01:58:26.431Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "Process.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Process.h", "RelativeDocumentMoniker": "Core\\Process.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Process.h", "RelativeToolTip": "Core\\Process.h", "ViewState": "AgIAAFoDAAAAAAAAAAApwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-28T01:57:58.045Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "IChar.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\IChar.cpp", "RelativeDocumentMoniker": "Core\\IChar.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\IChar.cpp", "RelativeToolTip": "Core\\IChar.cpp", "ViewState": "AgIAAHwBAAAAAAAAAADgv5ABAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-28T01:40:58.825Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Core.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Core.cpp", "RelativeDocumentMoniker": "Core\\Core.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Core.cpp", "RelativeToolTip": "Core\\Core.cpp", "ViewState": "AgIAAF4AAAAAAAAAAAAawHsAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-28T01:29:21.743Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "Memory.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Memory.cpp", "RelativeDocumentMoniker": "Core\\Memory.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Core\\Core\\Core\\Memory.cpp", "RelativeToolTip": "Core\\Memory.cpp", "ViewState": "AgIAAGUAAAAAAAAAAAAcwHcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-01-28T17:22:40.128Z"}]}]}]}