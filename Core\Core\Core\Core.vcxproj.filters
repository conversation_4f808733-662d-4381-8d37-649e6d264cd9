﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files\curl">
      <UniqueIdentifier>{a9c8361a-4857-4da2-94ab-07823a9e730f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Exports.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReadConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Process.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Functions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CreateBuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PasswordDecode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Time.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Tools.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IChar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Interface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ISkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Memory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DefenseImprovement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BlessingOfIntelligence.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RefiningWeapon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BlessingOfStrength.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RefreshBuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpeedUp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BlessingOfAgility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BlessingOfCriticalHit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BlessingOfHealth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Mix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IQuest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StoneOfBirth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StoneOfChance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EagleStat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ExpTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MonsterBlob.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BuyItemEx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SaveAllProperty.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SendMail.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Command.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CDBProcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Timer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Loader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Player.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OnLoadPlayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Packet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LevelUp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillUpgradeCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoLearn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillPointer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Start.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Quest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Skill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EggSkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WeaponChangePrefix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WeaponPut.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DefenseChangePrefix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DropItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArmorPut.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StatPointValue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CanAttack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FinalDamage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Trade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EggExp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemUse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Revival.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CastleWarFix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CutDownExp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Calls.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Transform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PowerfulWideningWound.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PerfectDefense.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Calculations.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProvocationOfBlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpinSlash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ContinuesSkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SwordDance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Blessing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TherapeuticTouch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ResetContinueSkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NormalHit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Fireball.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IceArrow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Icicle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Thunderbolt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="VirulentArrow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArrowExplosion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CombativeSpirit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArrowRain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Summon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BloodSuction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RevolveAttack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="EggThunderbolt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LifeAbsorption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlameInjection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LightningArrow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CounterDamage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lisans.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RemoteLisans.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Ornament.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShinRhoe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WhirlwindFeather.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZilPoong.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HighClassHiding.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Hiding.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Behead.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpinBlade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WrathOfHeaven.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShadowSlash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LightningSlash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpinAttack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArmorBreaker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TwinBladeStrike.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SuicidalBlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FinalBlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Assault.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AnkleAmputate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="VitalStrike.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FatalWound.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Rupture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Storage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Strangle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Stun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Confusion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DualShadow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CBaseDelete.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TheBoomOfEarth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TheWaveOfEarth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WalkOnTheAir.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShoutOfDefense.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShoutOfFightingSpirit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MassiveFire.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpiritOfTheArrows.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ArrowsOfTheMaster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OneHitStrike.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalStrike.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Punishment.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalDiffusion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DestroyingArmor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StrikeOfGod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Bombing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Incapacitation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReleasingTheEnergy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemFixes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MD5.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GhostKnife.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GhostFlash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SoulShield.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MudRoom.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Wave.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GhostWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TheSoulsPenance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ExecutiveDirector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JeungjangKing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JigukKing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WaveOfEmperor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AmplificationOfBlood.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Entangling.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpearOfPain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JeungjangKingOfTaein.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="JigukKingOfTaein.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SoulBlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DrainBlood.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MentalBreakdown.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MagicalExplosion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ExplodingSpirit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SixSouls.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Collapse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Doggebi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SpiritWave.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RisingKing4th.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Gangshin4th.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="base64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Sha256.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Duel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IBuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Menu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalSection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GetBuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sqlite3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Registration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ConcurrentMap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StandardPut.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TargetFind.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Buffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NPC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MapData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dirent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DirectoryReader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Guild.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Reload.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Channel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CChar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="XEATools.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lawless.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TriangularBattle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SufferingValley.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillCalculation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cleaner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkillCheck.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="curl\curl.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\curlbuild.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\curlrules.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\curlver.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\easy.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\mprintf.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\multi.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\stdcheaders.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\system.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
    <ClInclude Include="curl\typecheck-gcc.h">
      <Filter>Header Files\curl</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Exports.def">
      <Filter>Source Files</Filter>
    </None>
    <None Include="ClassDiagram.cd" />
    <None Include="PartyVsParty.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="WorldCup.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="DuelTournament.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="SystemRegistration.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="RegistrationMap.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="MutexMap.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="ConcurrentSet.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="ConcurrentVector.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="MyIterator.h">
      <Filter>Header Files</Filter>
    </None>
    <None Include="Pointer.h">
      <Filter>Header Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Core.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Tools.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IChar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Interface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ISkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Memory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IQuest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base64.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sha256.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IBuff.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sqlite3.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="shell.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>