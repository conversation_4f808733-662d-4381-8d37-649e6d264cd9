EXPORTS
A_SHAFinal=__E__0__ @1002
A_SHAInit=__E__1__ @1003
A_SHAUpdate=__E__2__ @1004
AbortSystemShutdownA=__E__3__ @1005
AbortSystemShutdownW=__E__4__ @1006
AccessCheck=__E__5__ @1007
AccessCheckAndAuditAlarmA=__E__6__ @1008
AccessCheckAndAuditAlarmW=__E__7__ @1009
AccessCheckByType=__E__8__ @1010
AccessCheckByTypeAndAuditAlarmA=__E__9__ @1011
AccessCheckByTypeAndAuditAlarmW=__E__10__ @1012
AccessCheckByTypeResultList=__E__11__ @1013
AccessCheckByTypeResultListAndAuditAlarmA=__E__12__ @1014
AccessCheckByTypeResultListAndAuditAlarmByHandleA=__E__13__ @1015
AccessCheckByTypeResultListAndAuditAlarmByHandleW=__E__14__ @1016
AccessCheckByTypeResultListAndAuditAlarmW=__E__15__ @1017
AddAccessAllowedAce=__E__16__ @1018
AddAccessAllowedAceEx=__E__17__ @1019
AddAccessAllowedObjectAce=__E__18__ @1020
AddAccessDeniedAce=__E__19__ @1021
AddAccessDeniedAceEx=__E__20__ @1022
AddAccessDeniedObjectAce=__E__21__ @1023
AddAce=__E__22__ @1024
AddAuditAccessAce=__E__23__ @1025
AddAuditAccessAceEx=__E__24__ @1026
AddAuditAccessObjectAce=__E__25__ @1027
AddConditionalAce=__E__26__ @1028
AddMandatoryAce=__E__27__ @1029
AddUsersToEncryptedFile=__E__28__ @1030
AddUsersToEncryptedFileEx=__E__29__ @1031
AdjustTokenGroups=__E__30__ @1032
AdjustTokenPrivileges=__E__31__ @1033
AllocateAndInitializeSid=__E__32__ @1034
AllocateLocallyUniqueId=__E__33__ @1035
AreAllAccessesGranted=__E__34__ @1036
AreAnyAccessesGranted=__E__35__ @1037
AuditComputeEffectivePolicyBySid=__E__36__ @1038
AuditComputeEffectivePolicyByToken=__E__37__ @1039
AuditEnumerateCategories=__E__38__ @1040
AuditEnumeratePerUserPolicy=__E__39__ @1041
AuditEnumerateSubCategories=__E__40__ @1042
AuditFree=__E__41__ @1043
AuditLookupCategoryGuidFromCategoryId=__E__42__ @1044
AuditLookupCategoryIdFromCategoryGuid=__E__43__ @1045
AuditLookupCategoryNameA=__E__44__ @1046
AuditLookupCategoryNameW=__E__45__ @1047
AuditLookupSubCategoryNameA=__E__46__ @1048
AuditLookupSubCategoryNameW=__E__47__ @1049
AuditQueryGlobalSaclA=__E__48__ @1050
AuditQueryGlobalSaclW=__E__49__ @1051
AuditQueryPerUserPolicy=__E__50__ @1052
AuditQuerySecurity=__E__51__ @1053
AuditQuerySystemPolicy=__E__52__ @1054
AuditSetGlobalSaclA=__E__53__ @1055
AuditSetGlobalSaclW=__E__54__ @1056
AuditSetPerUserPolicy=__E__55__ @1057
AuditSetSecurity=__E__56__ @1058
AuditSetSystemPolicy=__E__57__ @1059
BackupEventLogA=__E__58__ @1060
BackupEventLogW=__E__59__ @1061
BuildExplicitAccessWithNameA=__E__60__ @1062
BuildExplicitAccessWithNameW=__E__61__ @1063
BuildImpersonateExplicitAccessWithNameA=__E__62__ @1064
BuildImpersonateExplicitAccessWithNameW=__E__63__ @1065
BuildImpersonateTrusteeA=__E__64__ @1066
BuildImpersonateTrusteeW=__E__65__ @1067
BuildSecurityDescriptorA=__E__66__ @1068
BuildSecurityDescriptorW=__E__67__ @1069
BuildTrusteeWithNameA=__E__68__ @1070
BuildTrusteeWithNameW=__E__69__ @1071
BuildTrusteeWithObjectsAndNameA=__E__70__ @1072
BuildTrusteeWithObjectsAndNameW=__E__71__ @1073
BuildTrusteeWithObjectsAndSidA=__E__72__ @1074
BuildTrusteeWithObjectsAndSidW=__E__73__ @1075
BuildTrusteeWithSidA=__E__74__ @1076
BuildTrusteeWithSidW=__E__75__ @1077
CancelOverlappedAccess=__E__76__ @1078
ChangeServiceConfig2A=__E__77__ @1079
ChangeServiceConfig2W=__E__78__ @1080
ChangeServiceConfigA=__E__79__ @1081
ChangeServiceConfigW=__E__80__ @1082
CheckTokenMembership=__E__81__ @1083
ClearEventLogA=__E__82__ @1084
ClearEventLogW=__E__83__ @1085
CloseCodeAuthzLevel=__E__84__ @1086
CloseEncryptedFileRaw=__E__85__ @1087
CloseEventLog=__E__86__ @1088
CloseServiceHandle=__E__87__ @1089
CloseThreadWaitChainSession=__E__88__ @1090
CloseTrace=__E__89__ @1091
CommandLineFromMsiDescriptor=__E__90__ @1092
ComputeAccessTokenFromCodeAuthzLevel=__E__91__ @1093
ControlService=__E__92__ @1094
ControlServiceExA=__E__93__ @1095
ControlServiceExW=__E__94__ @1096
ControlTraceA=__E__95__ @1097
ControlTraceW=__E__96__ @1098
ConvertAccessToSecurityDescriptorA=__E__97__ @1099
ConvertAccessToSecurityDescriptorW=__E__98__ @1100
ConvertSDToStringSDRootDomainA=__E__99__ @1101
ConvertSDToStringSDRootDomainW=__E__100__ @1102
ConvertSecurityDescriptorToAccessA=__E__101__ @1103
ConvertSecurityDescriptorToAccessNamedA=__E__102__ @1104
ConvertSecurityDescriptorToAccessNamedW=__E__103__ @1105
ConvertSecurityDescriptorToAccessW=__E__104__ @1106
ConvertSecurityDescriptorToStringSecurityDescriptorA=__E__105__ @1107
ConvertSecurityDescriptorToStringSecurityDescriptorW=__E__106__ @1108
ConvertSidToStringSidA=__E__107__ @1109
ConvertSidToStringSidW=__E__108__ @1110
ConvertStringSDToSDDomainA=__E__109__ @1111
ConvertStringSDToSDDomainW=__E__110__ @1112
ConvertStringSDToSDRootDomainA=__E__111__ @1113
ConvertStringSDToSDRootDomainW=__E__112__ @1114
ConvertStringSecurityDescriptorToSecurityDescriptorA=__E__113__ @1115
ConvertStringSecurityDescriptorToSecurityDescriptorW=__E__114__ @1116
ConvertStringSidToSidA=__E__115__ @1117
ConvertStringSidToSidW=__E__116__ @1118
ConvertToAutoInheritPrivateObjectSecurity=__E__117__ @1119
CopySid=__E__118__ @1120
CreateCodeAuthzLevel=__E__119__ @1121
CreatePrivateObjectSecurity=__E__120__ @1122
CreatePrivateObjectSecurityEx=__E__121__ @1123
CreatePrivateObjectSecurityWithMultipleInheritance=__E__122__ @1124
CreateProcessAsUserA=__E__123__ @1125
CreateProcessAsUserW=__E__124__ @1126
CreateProcessWithLogonW=__E__125__ @1127
CreateProcessWithTokenW=__E__126__ @1128
CreateRestrictedToken=__E__127__ @1129
CreateServiceA=__E__128__ @1130
CreateServiceW=__E__129__ @1131
CreateTraceInstanceId=__E__130__ @1132
CreateWellKnownSid=__E__131__ @1133
CredBackupCredentials=__E__132__ @1134
CredDeleteA=__E__133__ @1135
CredDeleteW=__E__134__ @1136
CredEncryptAndMarshalBinaryBlob=__E__135__ @1137
CredEnumerateA=__E__136__ @1138
CredEnumerateW=__E__137__ @1139
CredFindBestCredentialA=__E__138__ @1140
CredFindBestCredentialW=__E__139__ @1141
CredFree=__E__140__ @1142
CredGetSessionTypes=__E__141__ @1143
CredGetTargetInfoA=__E__142__ @1144
CredGetTargetInfoW=__E__143__ @1145
CredIsMarshaledCredentialA=__E__144__ @1146
CredIsMarshaledCredentialW=__E__145__ @1147
CredIsProtectedA=__E__146__ @1148
CredIsProtectedW=__E__147__ @1149
CredMarshalCredentialA=__E__148__ @1150
CredMarshalCredentialW=__E__149__ @1151
CredProfileLoaded=__E__150__ @1152
CredProfileUnloaded=__E__151__ @1153
CredProtectA=__E__152__ @1154
CredProtectW=__E__153__ @1155
CredReadA=__E__154__ @1156
CredReadByTokenHandle=__E__155__ @1157
CredReadDomainCredentialsA=__E__156__ @1158
CredReadDomainCredentialsW=__E__157__ @1159
CredReadW=__E__158__ @1160
CredRenameA=__E__159__ @1161
CredRenameW=__E__160__ @1162
CredRestoreCredentials=__E__161__ @1163
CredUnmarshalCredentialA=__E__162__ @1164
CredUnmarshalCredentialW=__E__163__ @1165
CredUnprotectA=__E__164__ @1166
CredUnprotectW=__E__165__ @1167
CredWriteA=__E__166__ @1168
CredWriteDomainCredentialsA=__E__167__ @1169
CredWriteDomainCredentialsW=__E__168__ @1170
CredWriteW=__E__169__ @1171
CredpConvertCredential=__E__170__ @1172
CredpConvertOneCredentialSize=__E__171__ @1173
CredpConvertTargetInfo=__E__172__ @1174
CredpDecodeCredential=__E__173__ @1175
CredpEncodeCredential=__E__174__ @1176
CredpEncodeSecret=__E__175__ @1177
CryptAcquireContextA=__E__176__ @1178
CryptAcquireContextW=__E__177__ @1179
CryptContextAddRef=__E__178__ @1180
CryptCreateHash=__E__179__ @1181
CryptDecrypt=__E__180__ @1182
CryptDeriveKey=__E__181__ @1183
CryptDestroyHash=__E__182__ @1184
CryptDestroyKey=__E__183__ @1185
CryptDuplicateHash=__E__184__ @1186
CryptDuplicateKey=__E__185__ @1187
CryptEncrypt=__E__186__ @1188
CryptEnumProviderTypesA=__E__187__ @1189
CryptEnumProviderTypesW=__E__188__ @1190
CryptEnumProvidersA=__E__189__ @1191
CryptEnumProvidersW=__E__190__ @1192
CryptExportKey=__E__191__ @1193
CryptGenKey=__E__192__ @1194
CryptGenRandom=__E__193__ @1195
CryptGetDefaultProviderA=__E__194__ @1196
CryptGetDefaultProviderW=__E__195__ @1197
CryptGetHashParam=__E__196__ @1198
CryptGetKeyParam=__E__197__ @1199
CryptGetProvParam=__E__198__ @1200
CryptGetUserKey=__E__199__ @1201
CryptHashData=__E__200__ @1202
CryptHashSessionKey=__E__201__ @1203
CryptImportKey=__E__202__ @1204
CryptReleaseContext=__E__203__ @1205
CryptSetHashParam=__E__204__ @1206
CryptSetKeyParam=__E__205__ @1207
CryptSetProvParam=__E__206__ @1208
CryptSetProviderA=__E__207__ @1209
CryptSetProviderExA=__E__208__ @1210
CryptSetProviderExW=__E__209__ @1211
CryptSetProviderW=__E__210__ @1212
CryptSignHashA=__E__211__ @1213
CryptSignHashW=__E__212__ @1214
CryptVerifySignatureA=__E__213__ @1215
CryptVerifySignatureW=__E__214__ @1216
DecryptFileA=__E__215__ @1217
DecryptFileW=__E__216__ @1218
DeleteAce=__E__217__ @1219
DeleteService=__E__218__ @1220
DeregisterEventSource=__E__219__ @1221
DestroyPrivateObjectSecurity=__E__220__ @1222
DuplicateEncryptionInfoFile=__E__221__ @1223
DuplicateToken=__E__222__ @1224
DuplicateTokenEx=__E__223__ @1225
ElfBackupEventLogFileA=__E__224__ @1226
ElfBackupEventLogFileW=__E__225__ @1227
ElfChangeNotify=__E__226__ @1228
ElfClearEventLogFileA=__E__227__ @1229
ElfClearEventLogFileW=__E__228__ @1230
ElfCloseEventLog=__E__229__ @1231
ElfDeregisterEventSource=__E__230__ @1232
ElfFlushEventLog=__E__231__ @1233
ElfNumberOfRecords=__E__232__ @1234
ElfOldestRecord=__E__233__ @1235
ElfOpenBackupEventLogA=__E__234__ @1236
ElfOpenBackupEventLogW=__E__235__ @1237
ElfOpenEventLogA=__E__236__ @1238
ElfOpenEventLogW=__E__237__ @1239
ElfReadEventLogA=__E__238__ @1240
ElfReadEventLogW=__E__239__ @1241
ElfRegisterEventSourceA=__E__240__ @1242
ElfRegisterEventSourceW=__E__241__ @1243
ElfReportEventA=__E__242__ @1244
ElfReportEventAndSourceW=__E__243__ @1245
ElfReportEventW=__E__244__ @1246
EnableTrace=__E__245__ @1247
EnableTraceEx=__E__246__ @1249
EnableTraceEx2=__E__247__ @1248
EncryptFileA=__E__248__ @1250
EncryptFileW=__E__249__ @1251
EncryptedFileKeyInfo=__E__250__ @1252
EncryptionDisable=__E__251__ @1253
EnumDependentServicesA=__E__252__ @1254
EnumDependentServicesW=__E__253__ @1255
EnumServiceGroupW=__E__254__ @1256
EnumServicesStatusA=__E__255__ @1257
EnumServicesStatusExA=__E__256__ @1258
EnumServicesStatusExW=__E__257__ @1259
EnumServicesStatusW=__E__258__ @1260
EnumerateTraceGuids=__E__259__ @1261
EnumerateTraceGuidsEx=__E__260__ @1262
EqualDomainSid=__E__261__ @1263
EqualPrefixSid=__E__262__ @1264
EqualSid=__E__263__ @1265
EventAccessControl=__E__264__ @1266
EventAccessQuery=__E__265__ @1267
EventAccessRemove=__E__266__ @1268
EventActivityIdControl=__E__267__ @1269
EventEnabled=__E__268__ @1270
EventProviderEnabled=__E__269__ @1271
EventRegister=__E__270__ @1272
EventUnregister=__E__271__ @1273
EventWrite=__E__272__ @1274
EventWriteEndScenario=__E__273__ @1275
EventWriteEx=__E__274__ @1276
EventWriteStartScenario=__E__275__ @1277
EventWriteString=__E__276__ @1278
EventWriteTransfer=__E__277__ @1279
FileEncryptionStatusA=__E__278__ @1280
FileEncryptionStatusW=__E__279__ @1281
FindFirstFreeAce=__E__280__ @1282
FlushEfsCache=__E__281__ @1283
FlushTraceA=__E__282__ @1284
FlushTraceW=__E__283__ @1285
FreeEncryptedFileKeyInfo=__E__284__ @1286
FreeEncryptedFileMetadata=__E__285__ @1287
FreeEncryptionCertificateHashList=__E__286__ @1288
FreeInheritedFromArray=__E__287__ @1289
FreeSid=__E__288__ @1290
GetAccessPermissionsForObjectA=__E__289__ @1291
GetAccessPermissionsForObjectW=__E__290__ @1292
GetAce=__E__291__ @1293
GetAclInformation=__E__292__ @1294
GetAuditedPermissionsFromAclA=__E__293__ @1295
GetAuditedPermissionsFromAclW=__E__294__ @1296
GetCurrentHwProfileA=__E__295__ @1297
GetCurrentHwProfileW=__E__296__ @1298
GetEffectiveRightsFromAclA=__E__297__ @1299
GetEffectiveRightsFromAclW=__E__298__ @1300
GetEncryptedFileMetadata=__E__299__ @1301
GetEventLogInformation=__E__300__ @1302
GetExplicitEntriesFromAclA=__E__301__ @1303
GetExplicitEntriesFromAclW=__E__302__ @1304
GetFileSecurityA=__E__303__ @1305
GetFileSecurityW=__E__304__ @1306
GetInformationCodeAuthzLevelW=__E__305__ @1307
GetInformationCodeAuthzPolicyW=__E__306__ @1308
GetInheritanceSourceA=__E__307__ @1309
GetInheritanceSourceW=__E__308__ @1310
GetKernelObjectSecurity=__E__309__ @1311
GetLengthSid=__E__310__ @1312
GetLocalManagedApplicationData=__E__311__ @1313
GetLocalManagedApplications=__E__312__ @1314
GetManagedApplicationCategories=__E__313__ @1315
GetManagedApplications=__E__314__ @1316
GetMultipleTrusteeA=__E__315__ @1317
GetMultipleTrusteeOperationA=__E__316__ @1318
GetMultipleTrusteeOperationW=__E__317__ @1319
GetMultipleTrusteeW=__E__318__ @1320
GetNamedSecurityInfoA=__E__319__ @1321
GetNamedSecurityInfoExA=__E__320__ @1322
GetNamedSecurityInfoExW=__E__321__ @1323
GetNamedSecurityInfoW=__E__322__ @1324
GetNumberOfEventLogRecords=__E__323__ @1325
GetOldestEventLogRecord=__E__324__ @1326
GetOverlappedAccessResults=__E__325__ @1327
GetPrivateObjectSecurity=__E__326__ @1328
GetSecurityDescriptorControl=__E__327__ @1329
GetSecurityDescriptorDacl=__E__328__ @1330
GetSecurityDescriptorGroup=__E__329__ @1331
GetSecurityDescriptorLength=__E__330__ @1332
GetSecurityDescriptorOwner=__E__331__ @1333
GetSecurityDescriptorRMControl=__E__332__ @1334
GetSecurityDescriptorSacl=__E__333__ @1335
GetSecurityInfo=__E__334__ @1336
GetSecurityInfoExA=__E__335__ @1337
GetSecurityInfoExW=__E__336__ @1338
GetServiceDisplayNameA=__E__337__ @1339
GetServiceDisplayNameW=__E__338__ @1340
GetServiceKeyNameA=__E__339__ @1341
GetServiceKeyNameW=__E__340__ @1342
GetSidIdentifierAuthority=__E__341__ @1343
GetSidLengthRequired=__E__342__ @1344
GetSidSubAuthority=__E__343__ @1345
GetSidSubAuthorityCount=__E__344__ @1346
GetThreadWaitChain=__E__345__ @1347
GetTokenInformation=__E__346__ @1348
GetTraceEnableFlags=__E__347__ @1349
GetTraceEnableLevel=__E__348__ @1350
GetTraceLoggerHandle=__E__349__ @1351
GetTrusteeFormA=__E__350__ @1352
GetTrusteeFormW=__E__351__ @1353
GetTrusteeNameA=__E__352__ @1354
GetTrusteeNameW=__E__353__ @1355
GetTrusteeTypeA=__E__354__ @1356
GetTrusteeTypeW=__E__355__ @1357
GetUserNameA=__E__356__ @1358
GetUserNameW=__E__357__ @1359
GetWindowsAccountDomainSid=__E__358__ @1360
I_QueryTagInformation=__E__359__ @1361
I_ScGetCurrentGroupStateW=__E__360__ @1001
I_ScIsSecurityProcess=__E__361__ @1362
I_ScPnPGetServiceName=__E__362__ @1363
I_ScQueryServiceConfig=__E__363__ @1364
I_ScSendPnPMessage=__E__364__ @1365
I_ScSendTSMessage=__E__365__ @1366
I_ScSetServiceBitsA=__E__366__ @1367
I_ScSetServiceBitsW=__E__367__ @1368
I_ScValidatePnPService=__E__368__ @1369
IdentifyCodeAuthzLevelW=__E__369__ @1370
ImpersonateAnonymousToken=__E__370__ @1371
ImpersonateLoggedOnUser=__E__371__ @1372
ImpersonateNamedPipeClient=__E__372__ @1373
ImpersonateSelf=__E__373__ @1374
InitializeAcl=__E__374__ @1375
InitializeSecurityDescriptor=__E__375__ @1376
InitializeSid=__E__376__ @1377
InitiateShutdownA=__E__377__ @1378
InitiateShutdownW=__E__378__ @1379
InitiateSystemShutdownA=__E__379__ @1380
InitiateSystemShutdownExA=__E__380__ @1381
InitiateSystemShutdownExW=__E__381__ @1382
InitiateSystemShutdownW=__E__382__ @1383
InstallApplication=__E__383__ @1384
IsTextUnicode=__E__384__ @1385
IsTokenRestricted=__E__385__ @1386
IsTokenUntrusted=__E__386__ @1387
IsValidAcl=__E__387__ @1388
IsValidRelativeSecurityDescriptor=__E__388__ @1389
IsValidSecurityDescriptor=__E__389__ @1390
IsValidSid=__E__390__ @1391
IsWellKnownSid=__E__391__ @1392
LockServiceDatabase=__E__392__ @1393
LogonUserA=__E__393__ @1394
LogonUserExA=__E__394__ @1395
LogonUserExExW=__E__395__ @1396
LogonUserExW=__E__396__ @1397
LogonUserW=__E__397__ @1398
LookupAccountNameA=__E__398__ @1399
LookupAccountNameW=__E__399__ @1400
LookupAccountSidA=__E__400__ @1401
LookupAccountSidW=__E__401__ @1402
LookupPrivilegeDisplayNameA=__E__402__ @1403
LookupPrivilegeDisplayNameW=__E__403__ @1404
LookupPrivilegeNameA=__E__404__ @1405
LookupPrivilegeNameW=__E__405__ @1406
LookupPrivilegeValueA=__E__406__ @1407
LookupPrivilegeValueW=__E__407__ @1408
LookupSecurityDescriptorPartsA=__E__408__ @1409
LookupSecurityDescriptorPartsW=__E__409__ @1410
LsaAddAccountRights=__E__410__ @1411
LsaAddPrivilegesToAccount=__E__411__ @1412
LsaClearAuditLog=__E__412__ @1413
LsaClose=__E__413__ @1414
LsaCreateAccount=__E__414__ @1415
LsaCreateSecret=__E__415__ @1416
LsaCreateTrustedDomain=__E__416__ @1417
LsaCreateTrustedDomainEx=__E__417__ @1418
LsaDelete=__E__418__ @1419
LsaDeleteTrustedDomain=__E__419__ @1420
LsaEnumerateAccountRights=__E__420__ @1421
LsaEnumerateAccounts=__E__421__ @1422
LsaEnumerateAccountsWithUserRight=__E__422__ @1423
LsaEnumeratePrivileges=__E__423__ @1424
LsaEnumeratePrivilegesOfAccount=__E__424__ @1425
LsaEnumerateTrustedDomains=__E__425__ @1426
LsaEnumerateTrustedDomainsEx=__E__426__ @1427
LsaFreeMemory=__E__427__ @1428
LsaGetQuotasForAccount=__E__428__ @1429
LsaGetRemoteUserName=__E__429__ @1430
LsaGetSystemAccessAccount=__E__430__ @1431
LsaGetUserName=__E__431__ @1432
LsaICLookupNames=__E__432__ @1433
LsaICLookupNamesWithCreds=__E__433__ @1434
LsaICLookupSids=__E__434__ @1435
LsaICLookupSidsWithCreds=__E__435__ @1436
LsaLookupNames=__E__436__ @1438
LsaLookupNames2=__E__437__ @1437
LsaLookupPrivilegeDisplayName=__E__438__ @1439
LsaLookupPrivilegeName=__E__439__ @1440
LsaLookupPrivilegeValue=__E__440__ @1441
LsaLookupSids=__E__441__ @1442
LsaManageSidNameMapping=__E__442__ @1443
LsaNtStatusToWinError=__E__443__ @1444
LsaOpenAccount=__E__444__ @1445
LsaOpenPolicy=__E__445__ @1446
LsaOpenPolicySce=__E__446__ @1447
LsaOpenSecret=__E__447__ @1448
LsaOpenTrustedDomain=__E__448__ @1449
LsaOpenTrustedDomainByName=__E__449__ @1450
LsaQueryDomainInformationPolicy=__E__450__ @1451
LsaQueryForestTrustInformation=__E__451__ @1452
LsaQueryInfoTrustedDomain=__E__452__ @1453
LsaQueryInformationPolicy=__E__453__ @1454
LsaQuerySecret=__E__454__ @1455
LsaQuerySecurityObject=__E__455__ @1456
LsaQueryTrustedDomainInfo=__E__456__ @1457
LsaQueryTrustedDomainInfoByName=__E__457__ @1458
LsaRemoveAccountRights=__E__458__ @1459
LsaRemovePrivilegesFromAccount=__E__459__ @1460
LsaRetrievePrivateData=__E__460__ @1461
LsaSetDomainInformationPolicy=__E__461__ @1462
LsaSetForestTrustInformation=__E__462__ @1463
LsaSetInformationPolicy=__E__463__ @1464
LsaSetInformationTrustedDomain=__E__464__ @1465
LsaSetQuotasForAccount=__E__465__ @1466
LsaSetSecret=__E__466__ @1467
LsaSetSecurityObject=__E__467__ @1468
LsaSetSystemAccessAccount=__E__468__ @1469
LsaSetTrustedDomainInfoByName=__E__469__ @1470
LsaSetTrustedDomainInformation=__E__470__ @1471
LsaStorePrivateData=__E__471__ @1472
MD4Final=__E__472__ @1473
MD4Init=__E__473__ @1474
MD4Update=__E__474__ @1475
MD5Final=__E__475__ @1476
MD5Init=__E__476__ @1477
MD5Update=__E__477__ @1478
MSChapSrvChangePassword=__E__478__ @1480
MSChapSrvChangePassword2=__E__479__ @1479
MakeAbsoluteSD=__E__480__ @1482
MakeAbsoluteSD2=__E__481__ @1481
MakeSelfRelativeSD=__E__482__ @1483
MapGenericMask=__E__483__ @1484
NotifyBootConfigStatus=__E__484__ @1485
NotifyChangeEventLog=__E__485__ @1486
NotifyServiceStatusChange=__E__486__ @1487
NotifyServiceStatusChangeA=__E__487__ @1488
NotifyServiceStatusChangeW=__E__488__ @1489
ObjectCloseAuditAlarmA=__E__489__ @1490
ObjectCloseAuditAlarmW=__E__490__ @1491
ObjectDeleteAuditAlarmA=__E__491__ @1492
ObjectDeleteAuditAlarmW=__E__492__ @1493
ObjectOpenAuditAlarmA=__E__493__ @1494
ObjectOpenAuditAlarmW=__E__494__ @1495
ObjectPrivilegeAuditAlarmA=__E__495__ @1496
ObjectPrivilegeAuditAlarmW=__E__496__ @1497
OpenBackupEventLogA=__E__497__ @1498
OpenBackupEventLogW=__E__498__ @1499
OpenEncryptedFileRawA=__E__499__ @1500
OpenEncryptedFileRawW=__E__500__ @1501
OpenEventLogA=__E__501__ @1502
OpenEventLogW=__E__502__ @1503
OpenProcessToken=__E__503__ @1504
OpenSCManagerA=__E__504__ @1505
OpenSCManagerW=__E__505__ @1506
OpenServiceA=__E__506__ @1507
OpenServiceW=__E__507__ @1508
OpenThreadToken=__E__508__ @1509
OpenThreadWaitChainSession=__E__509__ @1510
OpenTraceA=__E__510__ @1511
OpenTraceW=__E__511__ @1512
PerfAddCounters=__E__512__ @1513
PerfCloseQueryHandle=__E__513__ @1514
PerfCreateInstance=__E__514__ @1515
PerfDecrementULongCounterValue=__E__515__ @1516
PerfDecrementULongLongCounterValue=__E__516__ @1517
PerfDeleteCounters=__E__517__ @1518
PerfDeleteInstance=__E__518__ @1519
PerfEnumerateCounterSet=__E__519__ @1520
PerfEnumerateCounterSetInstances=__E__520__ @1521
PerfIncrementULongCounterValue=__E__521__ @1522
PerfIncrementULongLongCounterValue=__E__522__ @1523
PerfOpenQueryHandle=__E__523__ @1524
PerfQueryCounterData=__E__524__ @1525
PerfQueryCounterInfo=__E__525__ @1526
PerfQueryCounterSetRegistrationInfo=__E__526__ @1527
PerfQueryInstance=__E__527__ @1528
PerfSetCounterRefValue=__E__528__ @1529
PerfSetCounterSetInfo=__E__529__ @1530
PerfSetULongCounterValue=__E__530__ @1531
PerfSetULongLongCounterValue=__E__531__ @1532
PerfStartProvider=__E__532__ @1533
PerfStartProviderEx=__E__533__ @1534
PerfStopProvider=__E__534__ @1535
PrivilegeCheck=__E__535__ @1536
PrivilegedServiceAuditAlarmA=__E__536__ @1537
PrivilegedServiceAuditAlarmW=__E__537__ @1538
ProcessIdleTasks=__E__538__ @1539
ProcessIdleTasksW=__E__539__ @1540
ProcessTrace=__E__540__ @1541
QueryAllTracesA=__E__541__ @1542
QueryAllTracesW=__E__542__ @1543
QueryRecoveryAgentsOnEncryptedFile=__E__543__ @1544
QuerySecurityAccessMask=__E__544__ @1545
QueryServiceConfig2A=__E__545__ @1546
QueryServiceConfig2W=__E__546__ @1547
QueryServiceConfigA=__E__547__ @1548
QueryServiceConfigW=__E__548__ @1549
QueryServiceLockStatusA=__E__549__ @1550
QueryServiceLockStatusW=__E__550__ @1551
QueryServiceObjectSecurity=__E__551__ @1552
QueryServiceStatus=__E__552__ @1553
QueryServiceStatusEx=__E__553__ @1554
QueryTraceA=__E__554__ @1555
QueryTraceW=__E__555__ @1556
QueryUsersOnEncryptedFile=__E__556__ @1557
ReadEncryptedFileRaw=__E__557__ @1558
ReadEventLogA=__E__558__ @1559
ReadEventLogW=__E__559__ @1560
RegCloseKey=__E__560__ @1561
RegConnectRegistryA=__E__561__ @1562
RegConnectRegistryExA=__E__562__ @1563
RegConnectRegistryExW=__E__563__ @1564
RegConnectRegistryW=__E__564__ @1565
RegCopyTreeA=__E__565__ @1566
RegCopyTreeW=__E__566__ @1567
RegCreateKeyA=__E__567__ @1568
RegCreateKeyExA=__E__568__ @1569
RegCreateKeyExW=__E__569__ @1570
RegCreateKeyTransactedA=__E__570__ @1571
RegCreateKeyTransactedW=__E__571__ @1572
RegCreateKeyW=__E__572__ @1573
RegDeleteKeyA=__E__573__ @1574
RegDeleteKeyExA=__E__574__ @1575
RegDeleteKeyExW=__E__575__ @1576
RegDeleteKeyTransactedA=__E__576__ @1577
RegDeleteKeyTransactedW=__E__577__ @1578
RegDeleteKeyValueA=__E__578__ @1579
RegDeleteKeyValueW=__E__579__ @1580
RegDeleteKeyW=__E__580__ @1581
RegDeleteTreeA=__E__581__ @1582
RegDeleteTreeW=__E__582__ @1583
RegDeleteValueA=__E__583__ @1584
RegDeleteValueW=__E__584__ @1585
RegDisablePredefinedCache=__E__585__ @1586
RegDisablePredefinedCacheEx=__E__586__ @1587
RegDisableReflectionKey=__E__587__ @1588
RegEnableReflectionKey=__E__588__ @1589
RegEnumKeyA=__E__589__ @1590
RegEnumKeyExA=__E__590__ @1591
RegEnumKeyExW=__E__591__ @1592
RegEnumKeyW=__E__592__ @1593
RegEnumValueA=__E__593__ @1594
RegEnumValueW=__E__594__ @1595
RegFlushKey=__E__595__ @1596
RegGetKeySecurity=__E__596__ @1597
RegGetValueA=__E__597__ @1598
RegGetValueW=__E__598__ @1599
RegLoadAppKeyA=__E__599__ @1600
RegLoadAppKeyW=__E__600__ @1601
RegLoadKeyA=__E__601__ @1602
RegLoadKeyW=__E__602__ @1603
RegLoadMUIStringA=__E__603__ @1604
RegLoadMUIStringW=__E__604__ @1605
RegNotifyChangeKeyValue=__E__605__ @1606
RegOpenCurrentUser=__E__606__ @1607
RegOpenKeyA=__E__607__ @1608
RegOpenKeyExA=__E__608__ @1609
RegOpenKeyExW=__E__609__ @1610
RegOpenKeyTransactedA=__E__610__ @1611
RegOpenKeyTransactedW=__E__611__ @1612
RegOpenKeyW=__E__612__ @1613
RegOpenUserClassesRoot=__E__613__ @1614
RegOverridePredefKey=__E__614__ @1615
RegQueryInfoKeyA=__E__615__ @1616
RegQueryInfoKeyW=__E__616__ @1617
RegQueryMultipleValuesA=__E__617__ @1618
RegQueryMultipleValuesW=__E__618__ @1619
RegQueryReflectionKey=__E__619__ @1620
RegQueryValueA=__E__620__ @1621
RegQueryValueExA=__E__621__ @1622
RegQueryValueExW=__E__622__ @1623
RegQueryValueW=__E__623__ @1624
RegRenameKey=__E__624__ @1625
RegReplaceKeyA=__E__625__ @1626
RegReplaceKeyW=__E__626__ @1627
RegRestoreKeyA=__E__627__ @1628
RegRestoreKeyW=__E__628__ @1629
RegSaveKeyA=__E__629__ @1630
RegSaveKeyExA=__E__630__ @1631
RegSaveKeyExW=__E__631__ @1632
RegSaveKeyW=__E__632__ @1633
RegSetKeySecurity=__E__633__ @1634
RegSetKeyValueA=__E__634__ @1635
RegSetKeyValueW=__E__635__ @1636
RegSetValueA=__E__636__ @1637
RegSetValueExA=__E__637__ @1638
RegSetValueExW=__E__638__ @1639
RegSetValueW=__E__639__ @1640
RegUnLoadKeyA=__E__640__ @1641
RegUnLoadKeyW=__E__641__ @1642
RegisterEventSourceA=__E__642__ @1643
RegisterEventSourceW=__E__643__ @1644
RegisterIdleTask=__E__644__ @1645
RegisterServiceCtrlHandlerA=__E__645__ @1646
RegisterServiceCtrlHandlerExA=__E__646__ @1647
RegisterServiceCtrlHandlerExW=__E__647__ @1648
RegisterServiceCtrlHandlerW=__E__648__ @1649
RegisterTraceGuidsA=__E__649__ @1650
RegisterTraceGuidsW=__E__650__ @1651
RegisterWaitChainCOMCallback=__E__651__ @1652
RemoveTraceCallback=__E__652__ @1653
RemoveUsersFromEncryptedFile=__E__653__ @1654
ReportEventA=__E__654__ @1655
ReportEventW=__E__655__ @1656
RevertToSelf=__E__656__ @1657
SaferCloseLevel=__E__657__ @1658
SaferComputeTokenFromLevel=__E__658__ @1659
SaferCreateLevel=__E__659__ @1660
SaferGetLevelInformation=__E__660__ @1661
SaferGetPolicyInformation=__E__661__ @1662
SaferIdentifyLevel=__E__662__ @1663
SaferRecordEventLogEntry=__E__663__ @1664
SaferSetLevelInformation=__E__664__ @1665
SaferSetPolicyInformation=__E__665__ @1666
SaferiChangeRegistryScope=__E__666__ @1667
SaferiCompareTokenLevels=__E__667__ @1668
SaferiIsDllAllowed=__E__668__ @1669
SaferiIsExecutableFileType=__E__669__ @1670
SaferiPopulateDefaultsInRegistry=__E__670__ @1671
SaferiRecordEventLogEntry=__E__671__ @1672
SaferiSearchMatchingHashRules=__E__672__ @1673
SetAclInformation=__E__673__ @1674
SetEncryptedFileMetadata=__E__674__ @1675
SetEntriesInAccessListA=__E__675__ @1676
SetEntriesInAccessListW=__E__676__ @1677
SetEntriesInAclA=__E__677__ @1678
SetEntriesInAclW=__E__678__ @1679
SetEntriesInAuditListA=__E__679__ @1680
SetEntriesInAuditListW=__E__680__ @1681
SetFileSecurityA=__E__681__ @1682
SetFileSecurityW=__E__682__ @1683
SetInformationCodeAuthzLevelW=__E__683__ @1684
SetInformationCodeAuthzPolicyW=__E__684__ @1685
SetKernelObjectSecurity=__E__685__ @1686
SetNamedSecurityInfoA=__E__686__ @1687
SetNamedSecurityInfoExA=__E__687__ @1688
SetNamedSecurityInfoExW=__E__688__ @1689
SetNamedSecurityInfoW=__E__689__ @1690
SetPrivateObjectSecurity=__E__690__ @1691
SetPrivateObjectSecurityEx=__E__691__ @1692
SetSecurityAccessMask=__E__692__ @1693
SetSecurityDescriptorControl=__E__693__ @1694
SetSecurityDescriptorDacl=__E__694__ @1695
SetSecurityDescriptorGroup=__E__695__ @1696
SetSecurityDescriptorOwner=__E__696__ @1697
SetSecurityDescriptorRMControl=__E__697__ @1698
SetSecurityDescriptorSacl=__E__698__ @1699
SetSecurityInfo=__E__699__ @1700
SetSecurityInfoExA=__E__700__ @1701
SetSecurityInfoExW=__E__701__ @1702
SetServiceBits=__E__702__ @1703
SetServiceObjectSecurity=__E__703__ @1704
SetServiceStatus=__E__704__ @1705
SetThreadToken=__E__705__ @1706
SetTokenInformation=__E__706__ @1707
SetTraceCallback=__E__707__ @1708
SetUserFileEncryptionKey=__E__708__ @1709
SetUserFileEncryptionKeyEx=__E__709__ @1710
StartServiceA=__E__710__ @1711
StartServiceCtrlDispatcherA=__E__711__ @1712
StartServiceCtrlDispatcherW=__E__712__ @1713
StartServiceW=__E__713__ @1714
StartTraceA=__E__714__ @1715
StartTraceW=__E__715__ @1716
StopTraceA=__E__716__ @1717
StopTraceW=__E__717__ @1718
SystemFunction001=__E__718__ @1719
SystemFunction002=__E__719__ @1720
SystemFunction003=__E__720__ @1721
SystemFunction004=__E__721__ @1722
SystemFunction005=__E__722__ @1723
SystemFunction006=__E__723__ @1724
SystemFunction007=__E__724__ @1725
SystemFunction008=__E__725__ @1726
SystemFunction009=__E__726__ @1727
SystemFunction010=__E__727__ @1728
SystemFunction011=__E__728__ @1729
SystemFunction012=__E__729__ @1730
SystemFunction013=__E__730__ @1731
SystemFunction014=__E__731__ @1732
SystemFunction015=__E__732__ @1733
SystemFunction016=__E__733__ @1734
SystemFunction017=__E__734__ @1735
SystemFunction018=__E__735__ @1736
SystemFunction019=__E__736__ @1737
SystemFunction020=__E__737__ @1738
SystemFunction021=__E__738__ @1739
SystemFunction022=__E__739__ @1740
SystemFunction023=__E__740__ @1741
SystemFunction024=__E__741__ @1742
SystemFunction025=__E__742__ @1743
SystemFunction026=__E__743__ @1744
SystemFunction027=__E__744__ @1745
SystemFunction028=__E__745__ @1746
SystemFunction029=__E__746__ @1747
SystemFunction030=__E__747__ @1748
SystemFunction031=__E__748__ @1749
SystemFunction032=__E__749__ @1750
SystemFunction033=__E__750__ @1751
SystemFunction034=__E__751__ @1752
SystemFunction035=__E__752__ @1753
SystemFunction036=__E__753__ @1754
SystemFunction040=__E__754__ @1755
SystemFunction041=__E__755__ @1756
TraceEvent=__E__756__ @1757
TraceEventInstance=__E__757__ @1758
TraceMessage=__E__758__ @1759
TraceMessageVa=__E__759__ @1760
TraceSetInformation=__E__760__ @1761
TreeResetNamedSecurityInfoA=__E__761__ @1762
TreeResetNamedSecurityInfoW=__E__762__ @1763
TreeSetNamedSecurityInfoA=__E__763__ @1764
TreeSetNamedSecurityInfoW=__E__764__ @1765
TrusteeAccessToObjectA=__E__765__ @1766
TrusteeAccessToObjectW=__E__766__ @1767
UninstallApplication=__E__767__ @1768
UnlockServiceDatabase=__E__768__ @1769
UnregisterIdleTask=__E__769__ @1770
UnregisterTraceGuids=__E__770__ @1771
UpdateTraceA=__E__771__ @1772
UpdateTraceW=__E__772__ @1773
UsePinForEncryptedFilesA=__E__773__ @1774
UsePinForEncryptedFilesW=__E__774__ @1775
WmiCloseBlock=__E__775__ @1776
WmiDevInstToInstanceNameA=__E__776__ @1777
WmiDevInstToInstanceNameW=__E__777__ @1778
WmiEnumerateGuids=__E__778__ @1779
WmiExecuteMethodA=__E__779__ @1780
WmiExecuteMethodW=__E__780__ @1781
WmiFileHandleToInstanceNameA=__E__781__ @1782
WmiFileHandleToInstanceNameW=__E__782__ @1783
WmiFreeBuffer=__E__783__ @1784
WmiMofEnumerateResourcesA=__E__784__ @1785
WmiMofEnumerateResourcesW=__E__785__ @1786
WmiNotificationRegistrationA=__E__786__ @1787
WmiNotificationRegistrationW=__E__787__ @1788
WmiOpenBlock=__E__788__ @1789
WmiQueryAllDataA=__E__789__ @1790
WmiQueryAllDataMultipleA=__E__790__ @1791
WmiQueryAllDataMultipleW=__E__791__ @1792
WmiQueryAllDataW=__E__792__ @1793
WmiQueryGuidInformation=__E__793__ @1794
WmiQuerySingleInstanceA=__E__794__ @1795
WmiQuerySingleInstanceMultipleA=__E__795__ @1796
WmiQuerySingleInstanceMultipleW=__E__796__ @1797
WmiQuerySingleInstanceW=__E__797__ @1798
WmiReceiveNotificationsA=__E__798__ @1799
WmiReceiveNotificationsW=__E__799__ @1800
WmiSetSingleInstanceA=__E__800__ @1801
WmiSetSingleInstanceW=__E__801__ @1802
WmiSetSingleItemA=__E__802__ @1803
WmiSetSingleItemW=__E__803__ @1804
WriteEncryptedFileRaw=__E__804__ @1805
___XXX___806=__E__805__ @1000 NONAME