/**
 * @file Fireball.h
 * @brief Fireball skill implementation for the game server
 *
 * This file implements the Fireball magic skill (ID: 73) which allows players
 * to cast a targeted fire-based magical attack on enemies. The skill consumes
 * mana and deals magical damage based on the caster's magic attack power.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

/**
 * @brief Executes the Fireball skill
 *
 * Implements the Fireball magic skill which deals fire-based magical damage
 * to a targeted enemy. The damage is calculated based on the caster's magic
 * attack power and skill grade, with reduced damage against players.
 *
 * @param IPlayer The character casting the skill
 * @param pPacket Pointer to the skill packet data
 * @param pPos Position in the packet to read target information
 */
void __fastcall Fireball(IChar IPlayer, int pPacket, int pPos)
{
	// Get the skill pointer for Fireball (skill ID 73)
	int pSkill = IPlayer.GetSkillPointer(73);

	// Validate player and skill availability
	if (IPlayer.IsValid() && pSkill)
	{
		ISkill xSkill((void*)pSkill);
		int nSkillGrade = xSkill.GetGrade();
		int nTargetID = 0; char bType = 0; void *pTarget = 0;

		// Read target information from packet
		CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);

		// Calculate mana cost based on skill grade
		int nMana = (int)(((((nSkillGrade-1)+30) * nSkillGrade)+300) * 0.79);

		TargetFind myTarget(bType, 0, nTargetID);
		pTarget = myTarget.getTarget();

		if (bType >= 2)
			return;

		if (pTarget && nSkillGrade && IPlayer.IsValid())
		{
			IChar Target(pTarget);

			if (IPlayer.GetCurMp() < nMana) {

				return;
			}

			if (pTarget == IPlayer.GetOffset()) {

				return;
			}

			if (IPlayer.IsValid() && Target.IsValid())
			{
				if (!IPlayer.IsInRange(Target,300)) {

					return;
				}

				int nDmg = (IPlayer.GetMagic() * MFBMul) + (xSkill.GetGrade() * CTools::Rate(MFBMin,MFBMax));

				if (Target.GetType() == 0)
					nDmg = (nDmg * MFBReduce) / 100;

				IPlayer.OktayDamageSingle(Target,nDmg,73);
				IPlayer.SetDirection(Target);
				IPlayer.DecreaseMana(nMana);
			}
		}

	}
}