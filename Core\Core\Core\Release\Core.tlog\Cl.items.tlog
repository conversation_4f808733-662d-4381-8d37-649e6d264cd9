G:\Kal\New Project\Packet Protection\Core\Core\Core\base64.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\base64.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\BufferTest.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\BufferTest.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\C++14_Compatibility_Test.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\C++14_Compatibility_Test.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\Core.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\Core.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\IBuff.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\IBuff.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\IChar.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\IChar.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\IItem.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\IItem.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\Interface.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\Interface.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\IQuest.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\IQuest.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\ISkill.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\ISkill.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\Memory.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\Memory.obj
G:\Kal\New Project\Packet Protection\PacketProtection.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\PacketProtection.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\sha256.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\sha256.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\shell.c;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\shell.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\sqlite3.c;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\sqlite3.obj
G:\Kal\New Project\Packet Protection\Core\Core\Core\Tools.cpp;G:\Kal\New Project\Packet Protection\Core\Core\Core\Release\Tools.obj
