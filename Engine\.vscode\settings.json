{"files.associations": {"xstring": "cpp", "algorithm": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "random": "cpp", "ratio": "cpp", "set": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "strstream": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}