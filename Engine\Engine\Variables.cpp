#include <string>
#include "Variables.h"
#include "Lock.h"
#include "Engine.h"
int PressableKey = 0;
int numLock = 0;
int UniqueKey = 0;
int CryptKey = 0;
int MyHonor = 0;
int VButton = 0;
int sentPacketNum = 0;
int InvTime1 = 0;
int InvTime2 = 0;
int InvTime3 = 0;
int BTime = 0;
LONG DelayCheck = 0;
LONG CurHash = 0;
LONG CurPos = 0;
LONG MD5Time = 0;
bool StopClient = false;
HINSTANCE hL2 = 0;
int latestPacket = 0;
int numPadClick = 0;
LONG MD5TimeToSleep = 0;
std::string mac = "";
Lock buffLock = Lock();
Lock HonorLock = Lock();
Lock PacketLock = Lock();
std::vector<BuffConfig> BuffIcons = std::vector<BuffConfig>();
std::vector<std::string> MD5Files = std::vector<std::string>();
std::map<int, std::vector<SoundCheck>> SoundMaps;
std::map<int, std::string> SoundKSMs;
std::set<int> Costumes;
std::map<int, HonorStruct> PlayerHonors;

void LockHonor() {
	HonorLock.Enter();
}

void UnlockHonor() {
	HonorLock.Leave();
}

void Console() {
	static bool consoleAllocated = false;
	if (!consoleAllocated) {
		if (AllocConsole()) {
			FILE* pCout;
			freopen_s(&pCout, "CONOUT$", "w", stdout);
			consoleAllocated = true;
		}
	}
}

uint32_t hash_string(const char * s)
{
	uint32_t hash = 0;

	for (; *s; ++s)
	{
		hash += *s;
		hash += (hash << 10);
		hash ^= (hash >> 6);
	}

	hash += (hash << 3);
	hash ^= (hash >> 11);
	hash += (hash << 15);

	return hash;
}

uint32_t sha256(std::string input) {
	return hash_string(input.c_str());
}

bool modifyBuff(int Key, int Name, int Time) {
	bool result = false;
	buffLock.Enter();
	int Size = BuffIcons.size();
	for (int i = 0; i < Size; i++) {
		BuffConfig x = BuffIcons[i];
		if (x.SBName == Name && x.SBKey == Key) {
			BuffIcons[i].Time = Time;
			result = true;
			break;
		}
	}
	buffLock.Leave();
	return result;
}

bool exists(int Key, int Name) {
	bool result = false;
	buffLock.Enter();
	int Size = BuffIcons.size();
	for (int i = 0; i < Size; i++) {
		BuffConfig x = BuffIcons[i];
		if (x.SBName == Name && x.SBKey == Key) {
			result = true;
			break;
		}
	}
	buffLock.Leave();
	return result;
}

void insertBuff(int Key, int Name, int Time) {
	DWORD t = Time + GetTickCount();
	if (modifyBuff(Key, Name, t))
		return;

	BuffConfig x = BuffConfig();
	x.SBKey = Key;
	x.SBName = Name;
	x.Time = t;

	buffLock.Enter();
	BuffIcons.push_back(x);
	buffLock.Leave();
}

void removeBuff(int Key, int Name) {
	buffLock.Enter();
	// Find and remove the buff while holding the lock
	for (auto it = BuffIcons.begin(); it != BuffIcons.end(); ++it) {
		if (it->SBKey == Key && it->SBName == Name) {
			BuffIcons.erase(it);
			break;
		}
	}
	buffLock.Leave();
}

void buffTimer() {
	buffLock.Enter();
	DWORD currentTime = GetTickCount();

	// Use iterator to safely remove expired buffs
	for (auto it = BuffIcons.begin(); it != BuffIcons.end();) {
		if (it->Time && (it->Time <= currentTime)) {
			if (it->SBKey && it->SBName) {
				// Store values before erasing
				int key = it->SBKey;
				int name = it->SBName;

				// Remove the expired buff
				it = BuffIcons.erase(it);
				buffLock.Leave();

				// Call the buff function outside the lock
				Engine::KGameSys::CallBuff(0, 0, 0, 0, 0, 0, 6000, key, name);

				// Recursively call to check for more expired buffs
				return buffTimer();
			} else {
				++it;
			}
		} else {
			++it;
		}
	}
	buffLock.Leave();
}